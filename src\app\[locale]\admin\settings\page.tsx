
"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Settings as SettingsIcon, Save, Bell, Server, Database, Shield, Globe, Construction } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';
import React from 'react';

export default function AdminApplicationSettingsPage() {
  const t = useScopedI18n('adminAppSettings');
  const tCommon = useScopedI18n('common');
  const { toast } = useToast();

  // Mock settings state - in a real app, these would be fetched and updated via API
  const [appName, setAppName] = React.useState('MarketSync');
  const [defaultLanguage, setDefaultLanguage] = React.useState('ar');
  const [maintenanceMode, setMaintenanceMode] = React.useState(false);
  const [maxUsers, setMaxUsers] = React.useState(5000);
  const [enable2FA, setEnable2FA] = React.useState(false);
  const [sessionTimeout, setSessionTimeout] = React.useState(30);
  const [emailNotifications, setEmailNotifications] = React.useState(true);
  const [autoBackup, setAutoBackup] = React.useState(true);

  const handleSaveChanges = () => {
    toast({
      title: t('settingsSavedTitle'),
      description: t('settingsSavedDesc'),
    });
    // Logic to save settings to backend would go here
  };

  const handleResetSettings = () => {
    setAppName('MarketSync');
    setDefaultLanguage('ar');
    setMaintenanceMode(false);
    setMaxUsers(5000);
    setEnable2FA(false);
    setSessionTimeout(30);
    setEmailNotifications(true);
    setAutoBackup(true);
    toast({
      title: 'تم إعادة تعيين الإعدادات',
      description: 'تم إعادة تعيين جميع الإعدادات إلى القيم الافتراضية',
    });
  };

  return (
    <AuthenticatedLayout expectedRole="admin">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <SettingsIcon className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card className="shadow-lg border-0">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Globe className="h-6 w-6 text-primary" />
                <CardTitle className="text-lg">{t('generalConfigTitle')}</CardTitle>
              </div>
              <CardDescription>{t('generalConfigDesc')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="appName" className="text-sm font-medium">{t('appNameLabel')}</Label>
                <Input
                  id="appName"
                  value={appName}
                  onChange={(e) => setAppName(e.target.value)}
                  className="h-10"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="defaultLanguage" className="text-sm font-medium">{t('defaultLanguageLabel')}</Label>
                <Input
                  id="defaultLanguage"
                  value={defaultLanguage}
                  onChange={(e) => setDefaultLanguage(e.target.value)}
                  placeholder="ar, en"
                  className="h-10"
                />
              </div>
              <div className="flex items-center justify-between rounded-lg border p-4 bg-muted/30">
                <div className="space-y-1">
                  <Label htmlFor="maintenanceMode" className="font-medium text-sm">{t('maintenanceModeLabel')}</Label>
                  <p className="text-xs text-muted-foreground">{t('maintenanceModeDesc')}</p>
                </div>
                <Switch
                  id="maintenanceMode"
                  checked={maintenanceMode}
                  onCheckedChange={setMaintenanceMode}
                />
              </div>
               <div className="space-y-2">
                  <Label htmlFor="maxUsers" className="text-sm font-medium">{t('maxUsersLabel')}</Label>
                  <Input
                    id="maxUsers"
                    type="number"
                    value={maxUsers}
                    onChange={(e) => setMaxUsers(parseInt(e.target.value, 10))}
                    className="h-10"
                  />
                  <p className="text-xs text-muted-foreground">{t('maxUsersDesc')}</p>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0">
            <CardHeader className="pb-4">
               <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Shield className="h-6 w-6 text-primary" />
                  <CardTitle className="text-lg">{t('securitySettingsTitle')}</CardTitle>
              </div>
              <CardDescription>{t('securitySettingsDesc')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between rounded-lg border p-4 bg-muted/30">
                <div className="space-y-1">
                  <Label htmlFor="enable2FA" className="font-medium text-sm">{t('twoFactorAuthLabel')}</Label>
                  <p className="text-xs text-muted-foreground">{t('twoFactorAuthDesc')}</p>
                </div>
                <Switch
                  id="enable2FA"
                  checked={enable2FA}
                  onCheckedChange={setEnable2FA}
                />
              </div>
              <div className="space-y-2">
                  <Label htmlFor="sessionTimeout" className="text-sm font-medium">{t('sessionTimeoutLabel')}</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    value={sessionTimeout}
                    onChange={(e) => setSessionTimeout(parseInt(e.target.value, 10))}
                    placeholder={t('sessionTimeoutPlaceholder')}
                    className="h-10"
                  />
                  <p className="text-xs text-muted-foreground">{t('sessionTimeoutDesc')}</p>
              </div>
              <div className="flex items-center justify-between rounded-lg border p-4 bg-muted/30">
                <div className="space-y-1">
                  <Label htmlFor="emailNotifications" className="font-medium text-sm">إشعارات البريد الإلكتروني</Label>
                  <p className="text-xs text-muted-foreground">تلقي إشعارات مهمة عبر البريد الإلكتروني</p>
                </div>
                <Switch
                  id="emailNotifications"
                  checked={emailNotifications}
                  onCheckedChange={setEmailNotifications}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card className="shadow-lg border-0">
            <CardHeader className="pb-4">
               <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Database className="h-6 w-6 text-primary" />
                  <CardTitle className="text-lg">إعدادات النظام</CardTitle>
              </div>
              <CardDescription>تكوين إعدادات النظام والنسخ الاحتياطي</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between rounded-lg border p-4 bg-muted/30">
                <div className="space-y-1">
                  <Label htmlFor="autoBackup" className="font-medium text-sm">النسخ الاحتياطي التلقائي</Label>
                  <p className="text-xs text-muted-foreground">تشغيل النسخ الاحتياطي التلقائي يومياً</p>
                </div>
                <Switch
                  id="autoBackup"
                  checked={autoBackup}
                  onCheckedChange={setAutoBackup}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium">حالة قاعدة البيانات</Label>
                <div className="p-3 rounded-lg border bg-green-50 dark:bg-green-950">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium text-green-700 dark:text-green-300">متصلة وتعمل بشكل طبيعي</span>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium">مساحة التخزين المستخدمة</Label>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>2.4 GB من 10 GB</span>
                    <span>24%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full" style={{ width: '24%' }}></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0">
            <CardHeader className="pb-4">
               <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Bell className="h-6 w-6 text-primary" />
                  <CardTitle className="text-lg">إعدادات الإشعارات</CardTitle>
              </div>
              <CardDescription>تكوين إعدادات الإشعارات والتنبيهات</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between rounded-lg border p-4 bg-muted/30">
                <div className="space-y-1">
                  <Label className="font-medium text-sm">إشعارات الأمان</Label>
                  <p className="text-xs text-muted-foreground">تلقي تنبيهات عند حدوث مشاكل أمنية</p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between rounded-lg border p-4 bg-muted/30">
                <div className="space-y-1">
                  <Label className="font-medium text-sm">تقارير الأداء</Label>
                  <p className="text-xs text-muted-foreground">تلقي تقارير أداء النظام أسبوعياً</p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between rounded-lg border p-4 bg-muted/30">
                <div className="space-y-1">
                  <Label className="font-medium text-sm">تحديثات النظام</Label>
                  <p className="text-xs text-muted-foreground">إشعارات عند توفر تحديثات جديدة</p>
                </div>
                <Switch defaultChecked />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-between items-center mt-8 p-4 rounded-lg border bg-muted/30">
          <div>
            <h3 className="font-semibold">حفظ التغييرات</h3>
            <p className="text-sm text-muted-foreground">تأكد من حفظ التغييرات قبل مغادرة الصفحة</p>
          </div>
          <div className="flex gap-3">
            <Button onClick={handleResetSettings} variant="outline">
                <SettingsIcon className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> إعادة تعيين
            </Button>
            <Button onClick={handleSaveChanges} className="bg-primary hover:bg-primary/90 text-primary-foreground">
                <Save className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {tCommon('save')}
            </Button>
          </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
