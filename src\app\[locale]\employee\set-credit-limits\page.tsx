"use client";

import { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth-context';
import { useScopedI18n } from '@/lib/i18n/client';
import {
  Landmark,
  Search,
  Edit,
  History,
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Filter,
  Download,
  Upload,
  Eye
} from 'lucide-react';

// Mock data interfaces
interface CustomerBalance {
  id: string;
  customerName: string;
  customerId: string;
  totalDebt: number;
  totalCredit: number;
  netBalance: number;
  creditLimit: number;
  lastPayment: string;
  lastTransaction: string;
  status: 'good' | 'warning' | 'overdue';
  phone?: string;
  email?: string;
  creditScore?: number;
  accountOpenDate?: string;
}

interface CreditLimitHistory {
  id: string;
  customerId: string;
  customerName: string;
  oldLimit: number;
  newLimit: number;
  changeReason: string;
  changedBy: string;
  changeDate: string;
  approvedBy?: string;
  approvalDate?: string;
}

// Mock data
const mockCustomerBalances: CustomerBalance[] = [
  {
    id: '1',
    customerName: 'أحمد محمد علي',
    customerId: 'customer001',
    totalDebt: 15000,
    totalCredit: 2000,
    netBalance: 13000,
    creditLimit: 20000,
    lastPayment: '2024-01-15',
    lastTransaction: '2024-01-20',
    status: 'good',
    phone: '*********',
    email: '<EMAIL>',
    creditScore: 85,
    accountOpenDate: '2023-06-15'
  },
  {
    id: '2',
    customerName: 'فاطمة حسن',
    customerId: 'customer002',
    totalDebt: 25000,
    totalCredit: 1000,
    netBalance: 24000,
    creditLimit: 20000,
    lastPayment: '2024-01-10',
    lastTransaction: '2024-01-22',
    status: 'warning',
    phone: '*********',
    email: '<EMAIL>',
    creditScore: 72,
    accountOpenDate: '2023-08-20'
  },
  {
    id: '3',
    customerName: 'محمد أحمد',
    customerId: 'customer003',
    totalDebt: 35000,
    totalCredit: 500,
    netBalance: 34500,
    creditLimit: 30000,
    lastPayment: '2024-01-05',
    lastTransaction: '2024-01-25',
    status: 'overdue',
    phone: '*********',
    email: '<EMAIL>',
    creditScore: 58,
    accountOpenDate: '2023-04-10'
  },
  {
    id: '4',
    customerName: 'سارة علي',
    customerId: 'customer004',
    totalDebt: 8000,
    totalCredit: 3000,
    netBalance: 5000,
    creditLimit: 15000,
    lastPayment: '2024-01-18',
    lastTransaction: '2024-01-23',
    status: 'good',
    phone: '*********',
    email: '<EMAIL>',
    creditScore: 92,
    accountOpenDate: '2023-09-05'
  }
];

const mockCreditHistory: CreditLimitHistory[] = [
  {
    id: '1',
    customerId: 'customer001',
    customerName: 'أحمد محمد علي',
    oldLimit: 15000,
    newLimit: 20000,
    changeReason: 'تحسن في سجل الدفع',
    changedBy: 'أحمد الموظف',
    changeDate: '2024-01-10',
    approvedBy: 'مدير الائتمان',
    approvalDate: '2024-01-11'
  },
  {
    id: '2',
    customerId: 'customer002',
    customerName: 'فاطمة حسن',
    oldLimit: 25000,
    newLimit: 20000,
    changeReason: 'تأخر في السداد',
    changedBy: 'فاطمة الموظفة',
    changeDate: '2024-01-08',
    approvedBy: 'مدير الائتمان',
    approvalDate: '2024-01-09'
  }
];

export default function EmployeeSetCreditLimitsPage() {
  const t = useScopedI18n('common');
  const tSpecific = useScopedI18n('employeeSetCreditLimits');
  const tCommon = useScopedI18n('common');
  const { user } = useAuth();
  const { toast } = useToast();
  const [customers, setCustomers] = useState<CustomerBalance[]>(mockCustomerBalances);
  const [creditHistory, setCreditHistory] = useState<CreditLimitHistory[]>(mockCreditHistory);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerBalance | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isBulkUpdateOpen, setIsBulkUpdateOpen] = useState(false);
  const [newCreditLimit, setNewCreditLimit] = useState('');
  const [changeReason, setChangeReason] = useState('');
  const [bulkUpdateData, setBulkUpdateData] = useState({
    percentage: '',
    minLimit: '',
    maxLimit: '',
    reason: ''
  });

  // Check permissions
  const hasPermission = user?.permissions?.canSetCreditLimits;

  if (!hasPermission) {
    return (
      <AuthenticatedLayout expectedRole="employee">
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-center">ليس لديك صلاحية</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-muted-foreground">
                ليس لديك صلاحية للوصول إلى هذه الصفحة
              </p>
            </CardContent>
          </Card>
        </div>
      </AuthenticatedLayout>
    );
  }

  // Filter customers based on search and status
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.customerId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone?.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || customer.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'good':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />جيد</Badge>;
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-500"><Clock className="w-3 h-3 mr-1" />تحذير</Badge>;
      case 'overdue':
        return <Badge variant="destructive"><AlertTriangle className="w-3 h-3 mr-1" />متأخر</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Handle credit limit update
  const handleUpdateCreditLimit = () => {
    if (!selectedCustomer || !newCreditLimit || !changeReason) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال حد ائتمان صحيح وسبب التغيير',
        variant: 'destructive'
      });
      return;
    }

    const limitValue = parseFloat(newCreditLimit);
    if (limitValue < 0) {
      toast({
        title: 'خطأ',
        description: 'حد الائتمان يجب أن يكون أكبر من أو يساوي صفر',
        variant: 'destructive'
      });
      return;
    }

    // Update customer credit limit
    const updatedCustomers = customers.map(customer => {
      if (customer.id === selectedCustomer.id) {
        const updatedCustomer = { ...customer, creditLimit: limitValue };

        // Update status based on new credit limit
        if (updatedCustomer.netBalance > updatedCustomer.creditLimit) {
          updatedCustomer.status = 'overdue';
        } else if (updatedCustomer.netBalance > updatedCustomer.creditLimit * 0.8) {
          updatedCustomer.status = 'warning';
        } else {
          updatedCustomer.status = 'good';
        }

        return updatedCustomer;
      }
      return customer;
    });

    // Add to credit history
    const historyEntry: CreditLimitHistory = {
      id: Date.now().toString(),
      customerId: selectedCustomer.id,
      customerName: selectedCustomer.customerName,
      oldLimit: selectedCustomer.creditLimit,
      newLimit: limitValue,
      changeReason,
      changedBy: user?.name || user?.username || 'موظف',
      changeDate: new Date().toISOString().split('T')[0],
      approvedBy: user?.name || user?.username || 'موظف',
      approvalDate: new Date().toISOString().split('T')[0]
    };

    setCustomers(updatedCustomers);
    setCreditHistory([historyEntry, ...creditHistory]);
    setSelectedCustomer(updatedCustomers.find(c => c.id === selectedCustomer.id) || null);

    toast({
      title: 'تم بنجاح',
      description: `تم تحديث حد الائتمان للعميل ${selectedCustomer.customerName} من ${selectedCustomer.creditLimit.toLocaleString()} إلى ${limitValue.toLocaleString()} ر.ي`
    });

    setNewCreditLimit('');
    setChangeReason('');
    setIsEditDialogOpen(false);
  };

  // Handle bulk update
  const handleBulkUpdate = () => {
    if (!bulkUpdateData.percentage || !bulkUpdateData.reason) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال نسبة التغيير وسبب التحديث',
        variant: 'destructive'
      });
      return;
    }

    const percentage = parseFloat(bulkUpdateData.percentage);
    const minLimit = bulkUpdateData.minLimit ? parseFloat(bulkUpdateData.minLimit) : 0;
    const maxLimit = bulkUpdateData.maxLimit ? parseFloat(bulkUpdateData.maxLimit) : Infinity;

    let updatedCount = 0;
    const updatedCustomers = customers.map(customer => {
      if (customer.creditLimit >= minLimit && customer.creditLimit <= maxLimit) {
        const newLimit = Math.round(customer.creditLimit * (1 + percentage / 100));
        updatedCount++;

        // Add to history
        const historyEntry: CreditLimitHistory = {
          id: `${Date.now()}-${customer.id}`,
          customerId: customer.id,
          customerName: customer.customerName,
          oldLimit: customer.creditLimit,
          newLimit,
          changeReason: `تحديث جماعي: ${bulkUpdateData.reason}`,
          changedBy: user?.name || user?.username || 'موظف',
          changeDate: new Date().toISOString().split('T')[0]
        };

        setCreditHistory(prev => [historyEntry, ...prev]);

        const updatedCustomer = { ...customer, creditLimit: newLimit };

        // Update status
        if (updatedCustomer.netBalance > updatedCustomer.creditLimit) {
          updatedCustomer.status = 'overdue';
        } else if (updatedCustomer.netBalance > updatedCustomer.creditLimit * 0.8) {
          updatedCustomer.status = 'warning';
        } else {
          updatedCustomer.status = 'good';
        }

        return updatedCustomer;
      }
      return customer;
    });

    setCustomers(updatedCustomers);

    toast({
      title: 'تم التحديث بنجاح',
      description: `تم تحديث حدود الائتمان لـ ${updatedCount} عميل`
    });

    setBulkUpdateData({ percentage: '', minLimit: '', maxLimit: '', reason: '' });
    setIsBulkUpdateOpen(false);
  };
  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-6 p-1">
        {/* Header */}
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Landmark className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('nav_setCreditLimits')}</h1>
            <p className="text-muted-foreground">
              {tSpecific('description')}
            </p>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي العملاء</CardTitle>
              <Users className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{customers.length}</div>
              <p className="text-xs text-muted-foreground">عميل مسجل</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">حالة جيدة</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {customers.filter(c => c.status === 'good').length}
              </div>
              <p className="text-xs text-muted-foreground">عميل</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">تحذير</CardTitle>
              <Clock className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {customers.filter(c => c.status === 'warning').length}
              </div>
              <p className="text-xs text-muted-foreground">عميل</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">متأخر</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {customers.filter(c => c.status === 'overdue').length}
              </div>
              <p className="text-xs text-muted-foreground">عميل</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="manage" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="manage">إدارة حدود الائتمان</TabsTrigger>
            <TabsTrigger value="history">سجل التغييرات</TabsTrigger>
          </TabsList>

          {/* Manage Credit Limits Tab */}
          <TabsContent value="manage" className="space-y-4">
            {/* Search and Filter */}
            <Card>
              <CardHeader>
                <CardTitle>البحث والتصفية</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <Label htmlFor="search">البحث</Label>
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="search"
                        placeholder="البحث بالاسم، رقم العميل، أو الهاتف..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div className="w-full md:w-48">
                    <Label htmlFor="status-filter">تصفية حسب الحالة</Label>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الحالة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">جميع الحالات</SelectItem>
                        <SelectItem value="good">جيد</SelectItem>
                        <SelectItem value="warning">تحذير</SelectItem>
                        <SelectItem value="overdue">متأخر</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={() => setIsBulkUpdateOpen(true)}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    تحديث جماعي
                  </Button>
                  <Button
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    تصدير البيانات
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Customers Table */}
            <Card>
              <CardHeader>
                <CardTitle>قائمة العملاء وحدود الائتمان</CardTitle>
                <CardDescription>
                  عرض {filteredCustomers.length} من {customers.length} عميل
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>اسم العميل</TableHead>
                      <TableHead>رقم الهاتف</TableHead>
                      <TableHead>الرصيد الصافي</TableHead>
                      <TableHead>حد الائتمان</TableHead>
                      <TableHead>الائتمان المتاح</TableHead>
                      <TableHead>نقاط الائتمان</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredCustomers.map((customer) => (
                      <TableRow key={customer.id}>
                        <TableCell className="font-medium">{customer.customerName}</TableCell>
                        <TableCell>{customer.phone}</TableCell>
                        <TableCell className={customer.netBalance > 0 ? "text-red-600" : "text-green-600"}>
                          {customer.netBalance.toLocaleString()} ر.ي
                        </TableCell>
                        <TableCell className="font-semibold">
                          {customer.creditLimit.toLocaleString()} ر.ي
                        </TableCell>
                        <TableCell className="text-blue-600">
                          {(customer.creditLimit - customer.netBalance).toLocaleString()} ر.ي
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className={`font-semibold ${
                              (customer.creditScore || 0) >= 80 ? 'text-green-600' :
                              (customer.creditScore || 0) >= 60 ? 'text-yellow-600' : 'text-red-600'
                            }`}>
                              {customer.creditScore || 'غير محدد'}
                            </span>
                            {(customer.creditScore || 0) >= 80 && <TrendingUp className="h-4 w-4 text-green-500" />}
                            {(customer.creditScore || 0) < 60 && <TrendingDown className="h-4 w-4 text-red-500" />}
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(customer.status)}</TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedCustomer(customer);
                                setNewCreditLimit(customer.creditLimit.toString());
                                setIsEditDialogOpen(true);
                              }}
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              تعديل
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
          {/* Credit History Tab */}
          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  سجل تغييرات حدود الائتمان
                </CardTitle>
                <CardDescription>
                  عرض جميع التغييرات التي تمت على حدود الائتمان
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>اسم العميل</TableHead>
                      <TableHead>الحد السابق</TableHead>
                      <TableHead>الحد الجديد</TableHead>
                      <TableHead>التغيير</TableHead>
                      <TableHead>سبب التغيير</TableHead>
                      <TableHead>تم بواسطة</TableHead>
                      <TableHead>تاريخ التغيير</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {creditHistory.map((entry) => (
                      <TableRow key={entry.id}>
                        <TableCell className="font-medium">{entry.customerName}</TableCell>
                        <TableCell>{entry.oldLimit.toLocaleString()} ر.ي</TableCell>
                        <TableCell>{entry.newLimit.toLocaleString()} ر.ي</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {entry.newLimit > entry.oldLimit ? (
                              <>
                                <TrendingUp className="h-4 w-4 text-green-500" />
                                <span className="text-green-600 font-semibold">
                                  +{(entry.newLimit - entry.oldLimit).toLocaleString()} ر.ي
                                </span>
                              </>
                            ) : (
                              <>
                                <TrendingDown className="h-4 w-4 text-red-500" />
                                <span className="text-red-600 font-semibold">
                                  -{(entry.oldLimit - entry.newLimit).toLocaleString()} ر.ي
                                </span>
                              </>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{entry.changeReason}</TableCell>
                        <TableCell>{entry.changedBy}</TableCell>
                        <TableCell>{entry.changeDate}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                {creditHistory.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    لا توجد تغييرات في سجل حدود الائتمان
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Edit Credit Limit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>تعديل حد الائتمان</DialogTitle>
              <DialogDescription>
                تعديل حد الائتمان للعميل: {selectedCustomer?.customerName}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label htmlFor="current-limit">حد الائتمان الحالي</Label>
                <Input
                  id="current-limit"
                  value={selectedCustomer?.creditLimit.toLocaleString() + ' ر.ي'}
                  disabled
                />
              </div>

              <div>
                <Label htmlFor="new-limit">حد الائتمان الجديد (ر.ي)</Label>
                <Input
                  id="new-limit"
                  type="number"
                  placeholder="أدخل حد الائتمان الجديد"
                  value={newCreditLimit}
                  onChange={(e) => setNewCreditLimit(e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="change-reason">سبب التغيير</Label>
                <Select value={changeReason} onValueChange={setChangeReason}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر سبب التغيير" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="تحسن في سجل الدفع">تحسن في سجل الدفع</SelectItem>
                    <SelectItem value="تأخر في السداد">تأخر في السداد</SelectItem>
                    <SelectItem value="زيادة حجم التعامل">زيادة حجم التعامل</SelectItem>
                    <SelectItem value="تقليل المخاطر">تقليل المخاطر</SelectItem>
                    <SelectItem value="طلب العميل">طلب العميل</SelectItem>
                    <SelectItem value="مراجعة دورية">مراجعة دورية</SelectItem>
                    <SelectItem value="أخرى">أخرى</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {selectedCustomer && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span>الرصيد الحالي:</span>
                      <span className={selectedCustomer.netBalance > 0 ? "text-red-600" : "text-green-600"}>
                        {selectedCustomer.netBalance.toLocaleString()} ر.ي
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>نقاط الائتمان:</span>
                      <span className={`font-semibold ${
                        (selectedCustomer.creditScore || 0) >= 80 ? 'text-green-600' :
                        (selectedCustomer.creditScore || 0) >= 60 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {selectedCustomer.creditScore || 'غير محدد'}
                      </span>
                    </div>
                    {newCreditLimit && (
                      <div className="flex justify-between font-semibold">
                        <span>الائتمان المتاح الجديد:</span>
                        <span className="text-blue-600">
                          {(parseFloat(newCreditLimit) - selectedCustomer.netBalance).toLocaleString()} ر.ي
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <AlertTriangle className="h-4 w-4 inline mr-1" />
                  تأكد من صحة المبلغ وسبب التغيير قبل التأكيد. هذا الإجراء سيؤثر على حالة العميل.
                </p>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleUpdateCreditLimit}>
                تحديث حد الائتمان
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        {/* Bulk Update Dialog */}
        <Dialog open={isBulkUpdateOpen} onOpenChange={setIsBulkUpdateOpen}>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>تحديث جماعي لحدود الائتمان</DialogTitle>
              <DialogDescription>
                تطبيق تغيير نسبي على حدود الائتمان لمجموعة من العملاء
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label htmlFor="percentage">نسبة التغيير (%)</Label>
                <Input
                  id="percentage"
                  type="number"
                  placeholder="مثال: 10 للزيادة 10% أو -5 للتقليل 5%"
                  value={bulkUpdateData.percentage}
                  onChange={(e) => setBulkUpdateData(prev => ({ ...prev, percentage: e.target.value }))}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  أدخل رقم موجب للزيادة أو سالب للتقليل
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="min-limit">الحد الأدنى (اختياري)</Label>
                  <Input
                    id="min-limit"
                    type="number"
                    placeholder="0"
                    value={bulkUpdateData.minLimit}
                    onChange={(e) => setBulkUpdateData(prev => ({ ...prev, minLimit: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="max-limit">الحد الأعلى (اختياري)</Label>
                  <Input
                    id="max-limit"
                    type="number"
                    placeholder="بدون حد أعلى"
                    value={bulkUpdateData.maxLimit}
                    onChange={(e) => setBulkUpdateData(prev => ({ ...prev, maxLimit: e.target.value }))}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="bulk-reason">سبب التحديث الجماعي</Label>
                <Select
                  value={bulkUpdateData.reason}
                  onValueChange={(value) => setBulkUpdateData(prev => ({ ...prev, reason: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر سبب التحديث" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="تحديث سنوي">تحديث سنوي</SelectItem>
                    <SelectItem value="تعديل السياسة">تعديل السياسة</SelectItem>
                    <SelectItem value="تحسن الأوضاع الاقتصادية">تحسن الأوضاع الاقتصادية</SelectItem>
                    <SelectItem value="تشديد السياسة الائتمانية">تشديد السياسة الائتمانية</SelectItem>
                    <SelectItem value="مراجعة دورية">مراجعة دورية</SelectItem>
                    <SelectItem value="تعديل حسب التضخم">تعديل حسب التضخم</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {bulkUpdateData.percentage && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="text-sm space-y-2">
                    <div className="font-semibold">معاينة التحديث:</div>
                    <div>
                      العملاء المتأثرون: {customers.filter(c => {
                        const minLimit = bulkUpdateData.minLimit ? parseFloat(bulkUpdateData.minLimit) : 0;
                        const maxLimit = bulkUpdateData.maxLimit ? parseFloat(bulkUpdateData.maxLimit) : Infinity;
                        return c.creditLimit >= minLimit && c.creditLimit <= maxLimit;
                      }).length} عميل
                    </div>
                    <div>
                      نسبة التغيير: {parseFloat(bulkUpdateData.percentage) > 0 ? '+' : ''}{bulkUpdateData.percentage}%
                    </div>
                    {bulkUpdateData.minLimit && (
                      <div>الحد الأدنى للتطبيق: {parseFloat(bulkUpdateData.minLimit).toLocaleString()} ر.ي</div>
                    )}
                    {bulkUpdateData.maxLimit && (
                      <div>الحد الأعلى للتطبيق: {parseFloat(bulkUpdateData.maxLimit).toLocaleString()} ر.ي</div>
                    )}
                  </div>
                </div>
              )}

              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-800">
                  <AlertTriangle className="h-4 w-4 inline mr-1" />
                  تحذير: هذا الإجراء سيؤثر على عدة عملاء في نفس الوقت. تأكد من صحة البيانات قبل التأكيد.
                </p>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsBulkUpdateOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleBulkUpdate} variant="destructive">
                تطبيق التحديث الجماعي
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AuthenticatedLayout>
  );
}