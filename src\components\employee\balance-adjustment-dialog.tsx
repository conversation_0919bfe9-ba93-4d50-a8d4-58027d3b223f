"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';

interface CustomerBalance {
  id: string;
  customerName: string;
  currentBalance: number;
  creditLimit: number;
}

interface BalanceAdjustmentDialogProps {
  customer: CustomerBalance | null;
  isOpen: boolean;
  onClose: () => void;
  onAdjustmentComplete: () => void;
}

export function BalanceAdjustmentDialog({
  customer,
  isOpen,
  onClose,
  onAdjustmentComplete
}: BalanceAdjustmentDialogProps) {
  const t = useScopedI18n('employeeManageBalances');
  const tCommon = useScopedI18n('common');
  const { toast } = useToast();

  const [adjustmentType, setAdjustmentType] = useState<'debit' | 'credit'>('debit');
  const [amount, setAmount] = useState('');
  const [reason, setReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [notes, setNotes] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const predefinedReasons = [
    { value: 'sale', label: t('reasonSale') },
    { value: 'payment', label: t('reasonPayment') },
    { value: 'return', label: t('reasonReturn') },
    { value: 'adjustment', label: t('reasonAdjustment') },
    { value: 'other', label: t('reasonOther') }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!customer || !amount || !reason) {
      toast({
        title: tCommon('error'),
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    const adjustmentAmount = parseFloat(amount);
    if (isNaN(adjustmentAmount) || adjustmentAmount <= 0) {
      toast({
        title: tCommon('error'),
        description: 'Please enter a valid amount',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Mock API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 1000));

      const finalAmount = adjustmentType === 'debit' ? -adjustmentAmount : adjustmentAmount;
      const finalReason = reason === 'other' ? customReason : predefinedReasons.find(r => r.value === reason)?.label || reason;

      console.log('Balance adjustment:', {
        customerId: customer.id,
        amount: finalAmount,
        reason: finalReason,
        notes,
        type: adjustmentType
      });

      toast({
        title: t('balanceAdjustedSuccess'),
        description: `${adjustmentType === 'debit' ? 'Debited' : 'Credited'} ${adjustmentAmount} YER for ${customer.customerName}`,
      });

      // Reset form
      setAmount('');
      setReason('');
      setCustomReason('');
      setNotes('');
      setAdjustmentType('debit');

      onAdjustmentComplete();
      onClose();

    } catch (error) {
      console.error('Error adjusting balance:', error);
      toast({
        title: t('errorAdjustingBalance'),
        description: 'Failed to adjust customer balance',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-YE', {
      style: 'currency',
      currency: 'YER',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const calculateNewBalance = () => {
    if (!customer || !amount) return customer?.currentBalance || 0;
    
    const adjustmentAmount = parseFloat(amount);
    if (isNaN(adjustmentAmount)) return customer.currentBalance;

    const finalAmount = adjustmentType === 'debit' ? -adjustmentAmount : adjustmentAmount;
    return customer.currentBalance + finalAmount;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t('adjustBalanceTitle')}</DialogTitle>
          <DialogDescription>
            {t('adjustBalanceDesc')}
          </DialogDescription>
        </DialogHeader>

        {customer && (
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Customer Info */}
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">{customer.customerName}</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">{t('currentBalance')}: </span>
                  <span className={customer.currentBalance >= 0 ? 'text-green-600' : 'text-destructive'}>
                    {formatCurrency(customer.currentBalance)}
                  </span>
                </div>
                <div>
                  <span className="text-muted-foreground">{t('creditLimit')}: </span>
                  <span>{formatCurrency(customer.creditLimit)}</span>
                </div>
              </div>
            </div>

            {/* Adjustment Type */}
            <div className="space-y-3">
              <Label>{t('adjustmentType')}</Label>
              <RadioGroup
                value={adjustmentType}
                onValueChange={(value) => setAdjustmentType(value as 'debit' | 'credit')}
                className="flex space-x-6"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="debit" id="debit" />
                  <Label htmlFor="debit" className="text-destructive">
                    {t('typeDebit')} (-)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="credit" id="credit" />
                  <Label htmlFor="credit" className="text-green-600">
                    {t('typeCredit')} (+)
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Amount */}
            <div className="space-y-2">
              <Label htmlFor="amount">{t('adjustmentAmount')} *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.00"
                required
              />
            </div>

            {/* Reason */}
            <div className="space-y-2">
              <Label htmlFor="reason">{t('adjustmentReason')} *</Label>
              <Select value={reason} onValueChange={setReason} required>
                <SelectTrigger>
                  <SelectValue placeholder="Select reason" />
                </SelectTrigger>
                <SelectContent>
                  {predefinedReasons.map((reasonOption) => (
                    <SelectItem key={reasonOption.value} value={reasonOption.value}>
                      {reasonOption.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Custom Reason */}
            {reason === 'other' && (
              <div className="space-y-2">
                <Label htmlFor="customReason">Custom Reason *</Label>
                <Input
                  id="customReason"
                  value={customReason}
                  onChange={(e) => setCustomReason(e.target.value)}
                  placeholder="Enter custom reason"
                  required
                />
              </div>
            )}

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Additional notes (optional)"
                rows={3}
              />
            </div>

            {/* New Balance Preview */}
            {amount && (
              <div className="bg-muted p-4 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">New Balance:</span>
                  <span className={`font-medium ${calculateNewBalance() >= 0 ? 'text-green-600' : 'text-destructive'}`}>
                    {formatCurrency(calculateNewBalance())}
                  </span>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                {tCommon('cancel')}
              </Button>
              <Button 
                type="submit" 
                disabled={isProcessing || !amount || !reason || (reason === 'other' && !customReason)}
              >
                {isProcessing ? 'Processing...' : t('confirmAdjustment')}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
