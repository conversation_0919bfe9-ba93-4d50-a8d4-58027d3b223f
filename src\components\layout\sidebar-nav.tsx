
"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import type { NavItem } from "@/config/site";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useI18n } from "@/lib/i18n/client";
import { useAuth } from "@/contexts/auth-context"; // Import useAuth

interface SidebarNavProps {
  items: NavItem[];
  className?: string;
}

export function SidebarNav({ items, className }: SidebarNavProps) {
  const pathname = usePathname();
  const t = useI18n();
  const { user } = useAuth(); // Get user from auth context

  if (!items?.length) {
    return null;
  }

  return (
    <nav className={cn("flex flex-col space-y-1 p-2", className)}>
      <ScrollArea className="h-[calc(100vh-8rem)]"> {/* Adjust height as needed */}
        {items.map((item, index) => {
          if (item.href) {
            // Permission check for employees
            if (user?.role === 'employee' && item.permissionKey) {
              // Item should only be shown if user.permissions exists and the specific permissionKey is explicitly true
              if (!(user.permissions && user.permissions[item.permissionKey] === true)) {
                return null; 
              }
            }

            return (
              <Link key={index} href={item.href} legacyBehavior passHref>
                <Button
                  variant={pathname === item.href ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start mb-1",
                    item.disabled && "cursor-not-allowed opacity-80"
                  )}
                  disabled={item.disabled}
                >
                  <item.icon className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
                  {t(item.title as any)} {/* Translate item.title */}
                  {item.label && (
                    <span className="ml-auto rounded-lg bg-primary px-2 py-0.5 text-xs text-primary-foreground">
                      {item.label} {/* Labels could also be translation keys if needed */}
                    </span>
                  )}
                </Button>
              </Link>
            );
          }
          return null;
        })}
      </ScrollArea>
    </nav>
  );
}
