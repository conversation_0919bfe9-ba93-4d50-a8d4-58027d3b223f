
import type { Role, EmployeePermissions } from '@/types';
import type { LucideIcon } from 'lucide-react';
import { LayoutDashboard, Users, ShoppingCart, Package, Briefcase, Truck, Settings, DollarSign, BarChart3, FileText, Bell, ShieldCheck, MessageSquare, CreditCard, MapPin, History, Star, Heart, ListChecks, BookText, Printer, Store, Globe, DatabaseBackup, Undo2, Landmark, BadgeDollarSign, SearchCode, CalendarClock, TrendingUp, FileSignature, UsersRound, LineChart, Megaphone, Factory, Banknote, Puzzle, Award, Send, LifeBuoy, Building, Shield } from 'lucide-react';

export interface NavItem {
  title: string; // This will now be a translation key, e.g., "nav_dashboard"
  href: string;
  icon: LucideIcon;
  disabled?: boolean;
  external?: boolean;
  label?: string;
  description?: string; // This could also be a translation key
  roles: Role[];
  permissionKey?: keyof EmployeePermissions; // Added for granular employee permissions
}

export interface RoleConfig {
  mainNav: NavItem[];
  sidebarNav?: NavItem[]; 
}

// These common items will also use translation keys
const commonSettings: NavItem = {
  title: 'nav_generalSettings', // Changed from 'Settings'
  href: '/settings',
  icon: Settings,
  roles: ['admin', 'owner', 'wholesaler', 'customer', 'employee', 'agent'],
};

const commonProfile: NavItem = {
  title: 'nav_profile', // Changed from 'Profile'
  href: '/profile',
  icon: Users,
  roles: ['admin', 'owner', 'wholesaler', 'customer', 'employee', 'agent'],
}

const commonSecuritySettings: NavItem = {
  title: 'nav_securitySettings',
  href: '/settings/security',
  icon: Shield,
  roles: ['admin', 'owner', 'wholesaler', 'customer', 'employee', 'agent'],
}

export const siteConfig = {
  name: 'common.appName', // Use translation key
  description: 'common.appDescription', // Use translation key
  url: 'http://localhost:3000', 
  ogImage: 'http://localhost:3000/og.jpg', 
  links: {
    twitter: 'https://twitter.com/yourhandle', 
    github: 'https://github.com/yourrepo', 
  },
  roleNavs: {
    admin: {
      mainNav: [
        { title: 'nav_dashboard', href: '/admin/dashboard', icon: LayoutDashboard, roles: ['admin'] },
        { title: 'nav_userManagement', href: '/admin/user-management', icon: Users, roles: ['admin'] },
        { title: 'nav_subscriptionControl', href: '/admin/subscriptions', icon: DollarSign, roles: ['admin'] },
        { title: 'nav_systemLogs', href: '/admin/logs', icon: FileText, roles: ['admin'] },
        { title: 'nav_accessControl', href: '/admin/access-control', icon: ShieldCheck, roles: ['admin'] },
        { title: 'nav_backupManagement', href: '/admin/backup-management', icon: DatabaseBackup, roles: ['admin'] },
        { title: 'nav_alerts', href: '/admin/alerts', icon: Send, roles: ['admin'] },
        { title: 'nav_supportTickets', href: '/admin/support', icon: LifeBuoy, roles: ['admin'] },
        { title: 'nav_appSettings', href: '/admin/settings', icon: Settings, roles: ['admin'] },
        commonProfile,
        // commonSettings, // Admin has its own app settings page
      ],
    },
    owner: {
      mainNav: [
        { title: 'nav_dashboard', href: '/owner/dashboard', icon: LayoutDashboard, roles: ['owner'] },
        { title: 'nav_staffManagement', href: '/owner/staff', icon: Users, roles: ['owner'] }, 
        { title: 'nav_customerManagement', href: '/owner/customers', icon: UsersRound, roles: ['owner'] },
        { title: 'nav_productManagement', href: '/owner/products', icon: Package, roles: ['owner'] }, 
        { title: 'nav_onlineStore', href: '/owner/online-store', icon: Store, roles: ['owner'], description: 'Manage products visible in your online store.' },
        { title: 'nav_browseWholesale', href: '/owner/browse-wholesale', icon: Building, roles: ['owner'], description: 'تصفح منتجات تجار الجملة.' },
        { title: 'nav_customerOrders', href: '/owner/purchases', icon: ShoppingCart, roles: ['owner'] }, 
        { title: 'nav_debtsManagement', href: '/owner/debts', icon: BookText, roles: ['owner'] },
        { title: 'nav_grantCreditOwner', href: '/owner/debts', icon: BadgeDollarSign, roles: ['owner'] }, 
        { title: 'nav_manageBalancesOwner', href: '/owner/debts', icon: DollarSign, roles: ['owner'] }, 
        { title: 'nav_setCreditLimitsOwner', href: '/owner/set-credit-limits', icon: Landmark, roles: ['owner'] }, 
        { title: 'nav_processReturnsOwner', href: '/owner/process-returns', icon: Undo2, roles: ['owner'] }, 
        { title: 'nav_inventoryCheck', href: '/owner/inventory', icon: Briefcase, roles: ['owner'] },
        { title: 'nav_supplierManagement', href: '/owner/suppliers', icon: Factory, roles: ['owner'] },
        { title: 'nav_salesReports', href: '/owner/reports', icon: BarChart3, roles: ['owner'] },
        { title: 'nav_advancedAnalytics', href: '/owner/advanced-analytics', icon: LineChart, roles: ['owner'] },
        { title: 'nav_marketingTools', href: '/owner/marketing', icon: Megaphone, roles: ['owner'] },
        { title: 'nav_loyaltyProgram', href: '/owner/loyalty', icon: Award, roles: ['owner'] },
        { title: 'nav_financialManagement', href: '/owner/financials', icon: Banknote, roles: ['owner'] },
        { title: 'nav_smartPredictions', href: '/owner/predictions', icon: BarChart3, roles: ['owner']},
        { title: 'nav_subscription', href: '/owner/subscription', icon: DollarSign, roles: ['owner'] },
        { title: 'nav_ownerSettings', href: '/owner/settings', icon: Settings, roles: ['owner'] },
        commonProfile,
        commonSecuritySettings,
      ],
    },
    employee: {
      mainNav: [
        { title: 'nav_dashboard', href: '/employee/dashboard', icon: LayoutDashboard, roles: ['employee'] },
        { title: 'nav_tasks', href: '/employee/tasks', icon: FileSignature, roles: ['employee'], permissionKey: 'canManageTasks' },
        { title: 'nav_approveOrders', href: '/employee/approve-orders', icon: ListChecks, roles: ['employee'], permissionKey: 'canReceiveOrders' },
        { title: 'nav_dispatchOrders', href: '/employee/dispatch', icon: Truck, roles: ['employee'], permissionKey: 'canDispatchOrders' }, 
        { title: 'nav_recordTransactions', href: '/employee/transactions', icon: CreditCard, roles: ['employee'], permissionKey: 'canRecordTransactions' },
        { title: 'nav_grantCredit', href: '/employee/grant-credit', icon: BadgeDollarSign, roles: ['employee'], permissionKey: 'canGrantCredit' },
        { title: 'nav_customerBalances', href: '/employee/customer-balances', icon: DollarSign, roles: ['employee'], permissionKey: 'canManageCustomerBalances' },
        { title: 'nav_setCreditLimits', href: '/employee/set-credit-limits', icon: Landmark, roles: ['employee'], permissionKey: 'canSetCreditLimits' },
        { title: 'nav_returnsManagement', href: '/employee/returns-management', icon: Undo2, roles: ['employee'], permissionKey: 'canProcessSalesReturns' },
        { title: 'nav_manageEmpProducts', href: '/employee/manage-products', icon: Package, roles: ['employee'], permissionKey: 'canManageProducts' },
        { title: 'nav_productLookup', href: '/employee/product-lookup', icon: SearchCode, roles: ['employee'], permissionKey: 'canAccessProductLookup'},
        { title: 'nav_inventoryReports', href: '/employee/inventory-reports', icon: Briefcase, roles: ['employee'], permissionKey: 'canViewInventoryReports'},
        { title: 'nav_performanceReports', href: '/employee/performance', icon: TrendingUp, roles: ['employee'], permissionKey: 'canViewPerformanceReports'},
        { title: 'nav_shifts', href: '/employee/shifts', icon: CalendarClock, roles: ['employee'], permissionKey: 'canManageShifts'},
        { title: 'nav_communicationCenter', href: '/employee/communication', icon: MessageSquare, roles: ['employee'], permissionKey: 'canAccessCommunicationTools'},
        { title: 'nav_printReports', href: '/employee/print-reports', icon: Printer, roles: ['employee'], permissionKey: 'canPrintReports'},
        { title: 'nav_notifications', href: '/employee/notifications', icon: Bell, roles: ['employee'] },
        commonProfile,
        commonSecuritySettings,
        commonSettings,
      ],
    },
    customer: {
      mainNav: [
        { title: 'nav_shop', href: '/customer/shop', icon: ShoppingCart, roles: ['customer'] },
        { title: 'nav_myOrders', href: '/customer/orders', icon: Package, roles: ['customer'] },
        { title: 'nav_myDebts', href: '/customer/debts', icon: BookText, roles: ['customer'] },
        { title: 'nav_orderHistory', href: '/customer/order-history', icon: History, roles: ['customer'] },
        { title: 'nav_favorites', href: '/customer/favorites', icon: Heart, roles: ['customer'] },
        commonProfile,
        commonSecuritySettings,
        commonSettings,
      ],
    },
    wholesaler: {
      mainNav: [
        { title: 'nav_dashboard', href: '/wholesaler/dashboard', icon: LayoutDashboard, roles: ['wholesaler'] },
        { title: 'nav_manageWholesaleProducts', href: '/wholesaler/products', icon: Package, roles: ['wholesaler'] },
        { title: 'nav_incomingOrders', href: '/wholesaler/orders', icon: ShoppingCart, roles: ['wholesaler'] },
        { title: 'nav_manageAgents', href: '/wholesaler/agents', icon: Users, roles: ['wholesaler'] },
        { title: 'nav_subscription', href: '/wholesaler/subscription', icon: DollarSign, roles: ['wholesaler'] },
        commonProfile,
        commonSecuritySettings,
        commonSettings,
      ],
    },
    agent: {
      mainNav: [
        { title: 'nav_dashboard', href: '/agent/dashboard', icon: LayoutDashboard, roles: ['agent'] },
        { title: 'nav_assignedOrders', href: '/agent/orders', icon: Truck, roles: ['agent'] },
        commonProfile,
        commonSecuritySettings,
        commonSettings,
      ],
    },
  } as Record<Role, RoleConfig>,
};

export type SiteConfig = typeof siteConfig;

