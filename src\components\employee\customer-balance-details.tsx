"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  User, 
  CreditCard, 
  History, 
  Phone, 
  Mail,
  Calendar,
  DollarSign,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { format } from 'date-fns';

interface CustomerBalance {
  id: string;
  customerName: string;
  email?: string;
  phone?: string;
  currentBalance: number;
  creditLimit: number;
  lastPaymentDate?: string;
  lastPaymentAmount?: number;
  status: 'good' | 'warning' | 'overdue' | 'blocked';
  totalOrders: number;
  totalSpent: number;
}

interface BalanceTransaction {
  id: string;
  date: string;
  type: 'sale' | 'payment' | 'return' | 'adjustment';
  amount: number;
  balanceAfter: number;
  reason: string;
  processedBy: string;
  orderId?: string;
}

interface CustomerBalanceDetailsProps {
  customer: CustomerBalance | null;
  isOpen: boolean;
  onClose: () => void;
}

export function CustomerBalanceDetails({
  customer,
  isOpen,
  onClose
}: CustomerBalanceDetailsProps) {
  const t = useScopedI18n('employeeManageBalances');
  const tCommon = useScopedI18n('common');

  const [transactions, setTransactions] = useState<BalanceTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (customer && isOpen) {
      loadTransactionHistory();
    }
  }, [customer, isOpen]);

  const loadTransactionHistory = async () => {
    if (!customer) return;

    setIsLoading(true);
    try {
      // Mock data - replace with actual API call
      const mockTransactions: BalanceTransaction[] = [
        {
          id: '1',
          date: '2024-01-20',
          type: 'payment',
          amount: 1000,
          balanceAfter: customer.currentBalance,
          reason: 'Cash payment',
          processedBy: 'أحمد الموظف',
          orderId: 'ORD-001'
        },
        {
          id: '2',
          date: '2024-01-18',
          type: 'sale',
          amount: -1500,
          balanceAfter: customer.currentBalance - 1000,
          reason: 'Purchase',
          processedBy: 'فاطمة الموظفة',
          orderId: 'ORD-002'
        },
        {
          id: '3',
          date: '2024-01-15',
          type: 'return',
          amount: 200,
          balanceAfter: customer.currentBalance - 1000 + 1500,
          reason: 'Product return',
          processedBy: 'محمد الموظف',
          orderId: 'ORD-003'
        },
        {
          id: '4',
          date: '2024-01-10',
          type: 'adjustment',
          amount: -100,
          balanceAfter: customer.currentBalance - 1000 + 1500 - 200,
          reason: 'Balance correction',
          processedBy: 'علي المدير'
        }
      ];

      setTransactions(mockTransactions);
    } catch (error) {
      console.error('Error loading transaction history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-YE', {
      style: 'currency',
      currency: 'YER',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case 'sale':
        return <TrendingDown className="h-4 w-4 text-destructive" />;
      case 'payment':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'return':
        return <TrendingUp className="h-4 w-4 text-blue-600" />;
      case 'adjustment':
        return <DollarSign className="h-4 w-4 text-orange-600" />;
      default:
        return <DollarSign className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getTransactionTypeBadge = (type: string) => {
    const typeConfig = {
      sale: { label: t('reasonSale'), variant: 'destructive' as const },
      payment: { label: t('reasonPayment'), variant: 'default' as const },
      return: { label: t('reasonReturn'), variant: 'secondary' as const },
      adjustment: { label: t('reasonAdjustment'), variant: 'outline' as const }
    };

    const config = typeConfig[type as keyof typeof typeConfig] || { 
      label: type, 
      variant: 'outline' as const 
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      good: { label: t('statusGood'), variant: 'default' as const },
      warning: { label: t('statusWarning'), variant: 'secondary' as const },
      overdue: { label: t('statusOverdue'), variant: 'destructive' as const },
      blocked: { label: t('statusBlocked'), variant: 'outline' as const }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  if (!customer) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {customer.customerName}
          </DialogTitle>
          <DialogDescription>
            Customer balance details and transaction history
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="history">{t('balanceHistory')}</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{customer.customerName}</span>
                  </div>
                  {customer.email && (
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{customer.email}</span>
                    </div>
                  )}
                  {customer.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{customer.phone}</span>
                    </div>
                  )}
                </div>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm text-muted-foreground">Status: </span>
                    {getStatusBadge(customer.status)}
                  </div>
                  <div>
                    <span className="text-sm text-muted-foreground">Total Orders: </span>
                    <span className="font-medium">{customer.totalOrders}</span>
                  </div>
                  <div>
                    <span className="text-sm text-muted-foreground">Total Spent: </span>
                    <span className="font-medium">{formatCurrency(customer.totalSpent)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Balance Information */}
            <div className="grid gap-4 md:grid-cols-3">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t('currentBalance')}</CardTitle>
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className={`text-2xl font-bold ${customer.currentBalance >= 0 ? 'text-green-600' : 'text-destructive'}`}>
                    {formatCurrency(customer.currentBalance)}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t('creditLimit')}</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatCurrency(customer.creditLimit)}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Available Credit</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {formatCurrency(customer.creditLimit + Math.min(0, customer.currentBalance))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Last Payment */}
            {customer.lastPaymentDate && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    {t('lastPayment')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="font-medium">
                        {format(new Date(customer.lastPaymentDate), 'MMMM dd, yyyy')}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Payment Date
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-green-600">
                        {formatCurrency(customer.lastPaymentAmount || 0)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Amount
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  {t('recentTransactions')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Loading transaction history...</p>
                  </div>
                ) : transactions.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No transactions found</p>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>{t('transactionDate')}</TableHead>
                          <TableHead>{t('transactionType')}</TableHead>
                          <TableHead>{t('transactionAmount')}</TableHead>
                          <TableHead>{t('transactionBalance')}</TableHead>
                          <TableHead>Processed By</TableHead>
                          <TableHead>Order ID</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {transactions.map((transaction) => (
                          <TableRow key={transaction.id}>
                            <TableCell>
                              {format(new Date(transaction.date), 'MMM dd, yyyy')}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                {getTransactionTypeIcon(transaction.type)}
                                {getTransactionTypeBadge(transaction.type)}
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className={transaction.amount >= 0 ? 'text-green-600' : 'text-destructive'}>
                                {transaction.amount >= 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                              </span>
                            </TableCell>
                            <TableCell>
                              <span className={transaction.balanceAfter >= 0 ? 'text-green-600' : 'text-destructive'}>
                                {formatCurrency(transaction.balanceAfter)}
                              </span>
                            </TableCell>
                            <TableCell className="text-sm">
                              {transaction.processedBy}
                            </TableCell>
                            <TableCell className="text-sm">
                              {transaction.orderId || tCommon('N_A')}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
