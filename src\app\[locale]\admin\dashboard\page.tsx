
"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { PlaceholderChart } from '@/components/dashboard/placeholder-chart';
import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { Users, DollarSign, FileText, BarChart3, ShieldCheck, Bell, MessageSquare, DatabaseBackup, CheckCircle, AlertTriangle, Settings, LifeBuoy, Activity, TrendingUp, Server, Zap } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import React, { useEffect, useState } from 'react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { EnhancedStatsCard, StatsGrid } from '@/components/admin/enhanced-stats-card';

const LAST_BACKUP_TIMESTAMP_KEY = 'marketSyncLastBackupTimestamp';

export default function AdminDashboardPage() {
  const tAdmin = useScopedI18n('adminDashboard');
  const tCommon = useScopedI18n('common');
  const tBackup = useScopedI18n('backupManagement');
  const [lastBackupTimestamp, setLastBackupTimestamp] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedTimestamp = localStorage.getItem(LAST_BACKUP_TIMESTAMP_KEY);
      if (storedTimestamp) {
        setLastBackupTimestamp(new Date(storedTimestamp).toLocaleString());
      }
    }
  }, []);


  const stats = [
    {
      titleKey: "totalUsers",
      value: "1,250",
      icon: Users,
      color: "text-blue-500",
      bgColor: "bg-blue-50 dark:bg-blue-950",
      href: "/admin/user-management",
      change: "+12%",
      changeType: "positive"
    },
    {
      titleKey: "activeSubscriptions",
      value: "850",
      icon: DollarSign,
      color: "text-green-500",
      bgColor: "bg-green-50 dark:bg-green-950",
      href: "/admin/subscriptions",
      change: "+8%",
      changeType: "positive"
    },
    {
      titleKey: "systemLogs",
      value: "2,847",
      icon: FileText,
      color: "text-yellow-500",
      bgColor: "bg-yellow-50 dark:bg-yellow-950",
      href: "/admin/logs",
      change: "+24%",
      changeType: "neutral"
    },
    {
      titleKey: "pendingSupportTickets",
      value: "12",
      icon: MessageSquare,
      color: "text-red-500",
      bgColor: "bg-red-50 dark:bg-red-950",
      href: "/admin/support",
      change: "-3",
      changeType: "positive"
    },
  ];

  const quickActions = [
    { labelKey: "manageUsers", href: "/admin/user-management", icon: Users },
    { labelKey: "controlSubscriptions", href: "/admin/subscriptions", icon: DollarSign },
    { labelKey: "accessControl", href: "/admin/access-control", icon: ShieldCheck },
    { labelKey: "nav_systemLogs", href: "/admin/logs", icon: FileText }, // Changed to use nav_ key
    { labelKey: "sendNotifications", href: "/admin/alerts", icon: Bell },
    { labelKey: "backupManagement", href: "/admin/backup-management", icon: DatabaseBackup},
    { labelKey: "nav_supportTickets", href: "/admin/support", icon: LifeBuoy },
    { labelKey: "nav_appSettings", href: "/admin/settings", icon: Settings },
  ];

  return (
    <AuthenticatedLayout expectedRole="admin">
      <div className="space-y-6 p-1">
        <h1 className="text-3xl font-bold text-foreground">{tAdmin('title')}</h1>
        
        <StatsGrid columns={4}>
          <EnhancedStatsCard
            title={tAdmin('totalUsers')}
            value="1,250"
            icon={Users}
            change="+12%"
            changeType="positive"
            color="blue"
            href="/admin/user-management"
            description="جميع المستخدمين المسجلين في النظام"
            trend={[45, 52, 48, 61, 55, 67, 73]}
          />
          <EnhancedStatsCard
            title={tAdmin('activeSubscriptions')}
            value="850"
            icon={DollarSign}
            change="+8%"
            changeType="positive"
            color="green"
            href="/admin/subscriptions"
            description="الاشتراكات النشطة والمدفوعة"
            trend={[30, 35, 32, 45, 42, 48, 52]}
          />
          <EnhancedStatsCard
            title={tAdmin('systemLogs')}
            value="2,847"
            icon={FileText}
            change="+24%"
            changeType="neutral"
            color="yellow"
            href="/admin/logs"
            description="إجمالي السجلات المسجلة اليوم"
            trend={[20, 25, 30, 28, 35, 40, 45]}
          />
          <EnhancedStatsCard
            title={tAdmin('pendingSupportTickets')}
            value="12"
            icon={MessageSquare}
            change="-3"
            changeType="positive"
            color="red"
            href="/admin/support"
            description="التذاكر المعلقة والمفتوحة"
            trend={[15, 18, 16, 14, 12, 10, 12]}
          />
        </StatsGrid>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card className="shadow-lg border-0 lg:col-span-2">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-primary" />
                <CardTitle className="text-xl">{tAdmin('quickActions')}</CardTitle>
              </div>
              <CardDescription>{tAdmin('quickActionsDescription')}</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {quickActions.map(action => (
                <Button
                  key={action.labelKey}
                  variant="ghost"
                  asChild
                  className="justify-start h-auto p-4 border border-border hover:border-primary/50 hover:bg-primary/5 transition-all duration-200"
                >
                  <Link href={action.href} className="flex flex-col items-start space-y-2">
                    <action.icon className="h-5 w-5 text-primary" />
                    <span className="text-sm font-medium">
                      {action.labelKey.startsWith('nav_') ? tCommon(action.labelKey as any) : tAdmin(action.labelKey as any)}
                    </span>
                  </Link>
                </Button>
              ))}
            </CardContent>
          </Card>

          {/* System Performance Card */}
          <Card className="shadow-lg border-0">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-2">
                <Activity className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">أداء النظام</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>استخدام المعالج</span>
                  <span className="font-medium">45%</span>
                </div>
                <Progress value={45} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>استخدام الذاكرة</span>
                  <span className="font-medium">62%</span>
                </div>
                <Progress value={62} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>مساحة القرص</span>
                  <span className="font-medium">78%</span>
                </div>
                <Progress value={78} className="h-2" />
              </div>
              <div className="pt-2 border-t">
                <div className="flex items-center text-sm text-green-600">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  النظام يعمل بشكل طبيعي
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

         <div className="grid gap-6 md:grid-cols-2">
            <Card className="shadow-lg border-0">
                <CardHeader className="pb-4">
                    <div className="flex items-center space-x-2">
                        <Server className="h-5 w-5 text-primary" />
                        <CardTitle className="text-lg">{tAdmin('systemHealth')}</CardTitle>
                    </div>
                    <CardDescription>{tAdmin('systemHealthDescription')}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="p-4 rounded-lg bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800">
                        <div className="flex items-center text-sm">
                            <CheckCircle className="mr-2 h-5 w-5 text-green-500 rtl:ml-2 rtl:mr-0" />
                            <span className="font-medium">{tAdmin('overallSystemStatus')}</span>
                            <Badge variant="default" className="ml-auto bg-green-500 hover:bg-green-600">
                                {tAdmin('optimal')}
                            </Badge>
                        </div>
                    </div>

                    <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 rounded-lg border">
                            <div className="flex items-center">
                                <DatabaseBackup className="mr-2 h-4 w-4 text-primary rtl:ml-2 rtl:mr-0" />
                                <span className="text-sm font-medium">{tBackup('lastBackupLabel')}</span>
                            </div>
                            <span className="text-sm text-primary font-semibold">
                                {lastBackupTimestamp ? lastBackupTimestamp : tBackup('noBackupPerformed')}
                            </span>
                        </div>

                        <div className="flex items-center justify-between p-3 rounded-lg border">
                            <div className="flex items-center">
                                <AlertTriangle className="mr-2 h-4 w-4 text-yellow-500 rtl:ml-2 rtl:mr-0" />
                                <span className="text-sm font-medium">{tAdmin('recentSecurityAlerts')}</span>
                            </div>
                            <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                0
                            </Badge>
                        </div>
                    </div>
                </CardContent>
            </Card>

             <Card className="shadow-lg border-0">
                <CardHeader className="pb-4">
                    <div className="flex items-center space-x-2">
                        <DatabaseBackup className="h-5 w-5 text-primary" />
                        <CardTitle className="text-lg">{tBackup('title')}</CardTitle>
                    </div>
                    <CardDescription>{tBackup('description')}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="p-4 rounded-lg border bg-muted/30">
                        <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{tBackup('lastBackupLabel')}</span>
                            <span className="text-sm text-primary font-semibold">
                                {lastBackupTimestamp ? lastBackupTimestamp : tBackup('noBackupPerformed')}
                            </span>
                        </div>
                    </div>

                    <Button variant="outline" asChild className="w-full">
                        <Link href="/admin/backup-management" className="flex items-center justify-center">
                            <DatabaseBackup className="mr-2 h-4 w-4" />
                            {tCommon('viewDetails')}
                        </Link>
                    </Button>
                </CardContent>
            </Card>
         </div>

      </div>
    </AuthenticatedLayout>
  );
}
