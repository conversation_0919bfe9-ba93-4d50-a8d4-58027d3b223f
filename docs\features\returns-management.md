# إدارة المرتجعات (Returns Management)

## نظرة عامة

ميزة إدارة المرتجعات تتيح للموظفين والمالكين معالجة مرتجعات العملاء بطريقة منظمة وفعالة، مع إدارة المبالغ المستردة وتحديث المخزون تلقائياً.

## الميزات الرئيسية

### 1. معالجة المرتجعات
- **عرض قائمة المرتجعات**: عرض جميع المرتجعات مع إمكانية التصفية والبحث
- **تفاصيل المرتجع**: عرض تفاصيل كاملة لكل مرتجع
- **معالجة المرتجعات**: الموافقة أو رفض أو معالجة المرتجعات
- **إنشاء مرتجع جديد**: إنشاء مرتجعات جديدة للطلبات المكتملة

### 2. أنواع المرتجعات
- **منتج معيب**: منتجات بها عيوب تصنيع
- **منتج خاطئ**: طلب منتج مختلف عن المطلوب
- **عدم رضا العميل**: عدم إعجاب العميل بالمنتج
- **تلف أثناء الشحن**: منتجات تضررت أثناء التوصيل
- **منتج منتهي الصلاحية**: منتجات تجاوزت تاريخ الصلاحية
- **أخرى**: أسباب أخرى يحددها العميل

### 3. طرق الاسترداد
- **استرداد نقدي**: إرجاع المبلغ نقداً
- **رصيد حساب**: إضافة المبلغ لرصيد العميل
- **استبدال**: استبدال المنتج بمنتج آخر
- **رصيد المتجر**: رصيد يمكن استخدامه في المتجر فقط

### 4. حالات المرتجع
- **معلق (Pending)**: مرتجع جديد في انتظار المراجعة
- **موافق عليه (Approved)**: تمت الموافقة على المرتجع
- **مرفوض (Rejected)**: تم رفض المرتجع
- **تم المعالجة (Processed)**: تم معالجة المرتجع
- **تم الاسترداد (Refunded)**: تم استرداد المبلغ للعميل

## البنية التقنية

### أنواع البيانات (Types)

```typescript
interface Return {
  id: string;
  orderId: string;
  customerId: string;
  customerName: string;
  customerPhone?: string;
  items: ReturnItem[];
  totalReturnAmount: number;
  returnReason: 'defective' | 'wrong_item' | 'not_satisfied' | 'damaged_shipping' | 'expired' | 'other';
  returnReasonDetails?: string;
  status: 'pending' | 'approved' | 'rejected' | 'processed' | 'refunded';
  refundMethod: 'cash' | 'credit' | 'exchange' | 'store_credit';
  refundAmount?: number;
  exchangeItems?: ExchangeItem[];
  createdAt: string;
  processedAt?: string;
  processedBy?: string;
  processedByName?: string;
  approvedBy?: string;
  approvedByName?: string;
  notes?: string;
  attachments?: string[];
}

interface ReturnItem {
  id: string;
  productId: string;
  productName: string;
  originalQuantity: number;
  returnQuantity: number;
  originalPrice: number;
  returnAmount: number;
  condition: 'excellent' | 'good' | 'damaged' | 'defective';
  reason: string;
  imageUrl?: string;
}

interface ReturnPolicy {
  id: string;
  ownerId: string;
  maxReturnDays: number;
  allowedReasons: string[];
  requiresApproval: boolean;
  refundMethods: ('cash' | 'credit' | 'exchange' | 'store_credit')[];
  restockingFee?: number;
  conditions: {
    defective: { allowedDays: number; requiresProof: boolean };
    wrongItem: { allowedDays: number; requiresProof: boolean };
    notSatisfied: { allowedDays: number; requiresProof: boolean };
    damagedShipping: { allowedDays: number; requiresProof: boolean };
    expired: { allowedDays: number; requiresProof: boolean };
  };
  createdAt: string;
  updatedAt: string;
}
```

### المكونات (Components)

#### 1. ReturnsList
- عرض قائمة المرتجعات مع التصفية والبحث
- إمكانية عرض تفاصيل أو معالجة المرتجعات

#### 2. ReturnDetails
- عرض تفاصيل كاملة للمرتجع
- معلومات العميل والطلب والمنتجات المرتجعة

#### 3. ProcessReturnForm
- نموذج معالجة المرتجع
- الموافقة أو الرفض مع حساب المبالغ المستردة

#### 4. NewReturnForm
- نموذج إنشاء مرتجع جديد
- اختيار الطلب والمنتجات وتحديد الأسباب

#### 5. ReturnsStats
- إحصائيات المرتجعات
- عدد المرتجعات المعلقة والمعالجة والمبالغ المستردة

### الصفحات (Pages)

#### للموظفين: `/employee/returns-management`
- عرض وإدارة المرتجعات
- معالجة المرتجعات المعلقة
- إنشاء مرتجعات جديدة

#### للمالكين: `/owner/process-returns`
- جميع ميزات الموظفين
- إعدادات سياسة الإرجاع
- تحليلات المرتجعات المتقدمة

## الصلاحيات

### الموظفين
- يحتاجون إلى صلاحية `canProcessSalesReturns` لمعالجة المرتجعات
- يمكنهم عرض وإنشاء ومعالجة المرتجعات

### المالكين
- صلاحيات كاملة لإدارة المرتجعات
- إعداد سياسات الإرجاع
- عرض التحليلات والتقارير

## البيانات الوهمية

تم إنشاء بيانات وهمية شاملة تشمل:
- 5 مرتجعات بحالات مختلفة
- أسباب متنوعة للإرجاع
- طرق استرداد مختلفة
- سياسة إرجاع افتراضية

## الترجمة

تم إضافة ترجمات كاملة باللغتين العربية والإنجليزية لجميع عناصر الواجهة:
- نصوص الواجهة
- رسائل التأكيد والأخطاء
- تسميات الحقول والأزرار
- حالات وأسباب المرتجعات

## المميزات المستقبلية

### قيد التطوير
1. **سياسة الإرجاع المتقدمة**: إعدادات مفصلة لقواعد الإرجاع
2. **تحليلات المرتجعات**: تقارير مفصلة عن أنماط المرتجعات
3. **تكامل المخزون**: تحديث المخزون تلقائياً عند المعالجة
4. **إشعارات العملاء**: إشعارات تلقائية للعملاء عن حالة المرتجع
5. **طباعة التقارير**: تقارير قابلة للطباعة للمرتجعات

### تحسينات مخططة
- رفع الصور والمرفقات
- تتبع تاريخ المرتجعات
- تقييم أسباب الإرجاع الشائعة
- تحليل تكلفة المرتجعات
- تكامل مع أنظمة الدفع

## الاستخدام

### إنشاء مرتجع جديد
1. انتقل إلى صفحة إدارة المرتجعات
2. اضغط على "مرتجع جديد"
3. اختر الطلب المراد إرجاعه
4. حدد المنتجات والكميات
5. اختر سبب الإرجاع وطريقة الاسترداد
6. أضف ملاحظات إضافية
7. اضغط "تقديم المرتجع"

### معالجة مرتجع
1. اختر المرتجع من القائمة
2. اضغط "معالجة"
3. اختر الإجراء (موافقة/رفض/معالجة)
4. حدد مبلغ الاسترداد ورسوم إعادة التخزين
5. أضف ملاحظات المعالجة
6. اضغط "تأكيد الإجراء"

## الدعم التقني

للمساعدة في استخدام ميزة إدارة المرتجعات، يرجى مراجعة:
- دليل المستخدم
- الأسئلة الشائعة
- فريق الدعم التقني
