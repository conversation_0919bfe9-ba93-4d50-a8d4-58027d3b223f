"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Fingerprint, 
  Key, 
  Smartphone, 
  AlertTriangle, 
  CheckCircle,
  Trash2,
  Plus,
  Eye,
  EyeOff
} from 'lucide-react';
import type { Role } from '@/types';
import { useScopedI18n } from '@/lib/i18n/client';
import { BiometricManager } from '@/components/security/biometric-manager';
import { useState } from 'react';

const allRoles: Role[] = ['admin', 'owner', 'employee', 'customer', 'wholesaler', 'agent'];

export default function SecuritySettingsPage() {
  const tCommon = useScopedI18n('common');

  // States
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);



  return (
    <AuthenticatedLayout expectedRole={allRoles}>
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Shield className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">إعدادات الأمان</h1>
            <p className="text-muted-foreground">
              قم بإدارة إعدادات الأمان وطرق المصادقة لحسابك
            </p>
          </div>
        </div>

        {/* Biometric Authentication Card */}
        <BiometricManager />

        {/* Password Settings Card */}
        <Card className="shadow-lg">
          <CardHeader>
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Key className="h-6 w-6 text-primary" />
              <CardTitle>إعدادات كلمة المرور</CardTitle>
            </div>
            <CardDescription>
              قم بتحديث كلمة المرور الخاصة بك للحفاظ على أمان حسابك
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <form className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="currentPassword">كلمة المرور الحالية</Label>
                <div className="relative">
                  <Input
                    id="currentPassword"
                    type={showCurrentPassword ? "text" : "password"}
                    placeholder="أدخل كلمة المرور الحالية"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  >
                    {showCurrentPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="newPassword">كلمة المرور الجديدة</Label>
                <div className="relative">
                  <Input
                    id="newPassword"
                    type={showNewPassword ? "text" : "password"}
                    placeholder="أدخل كلمة المرور الجديدة"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">تأكيد كلمة المرور الجديدة</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="أعد إدخال كلمة المرور الجديدة"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <Button type="submit" className="w-full">
                تحديث كلمة المرور
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Two-Factor Authentication Card */}
        <Card className="shadow-lg">
          <CardHeader>
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Smartphone className="h-6 w-6 text-primary" />
              <CardTitle>المصادقة الثنائية</CardTitle>
            </div>
            <CardDescription>
              أضف طبقة حماية إضافية لحسابك باستخدام رمز التحقق
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="twoFactorAuth" className="font-medium">تفعيل المصادقة الثنائية</Label>
                <p className="text-sm text-muted-foreground">
                  سيتم إرسال رمز تحقق إلى هاتفك عند تسجيل الدخول
                </p>
              </div>
              <Switch id="twoFactorAuth" />
            </div>
          </CardContent>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}
