// use server'

/**
 * @fileOverview Provides AI-driven predictions for supermarket inventory management.
 *
 * - predictDemand - Predicts future demand for products based on historical sales data.
 * - PredictDemandInput - The input type for the predictDemand function.
 * - PredictDemandOutput - The return type for the predictDemand function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const PredictDemandInputSchema = z.object({
  productCategory: z.string().describe('The category of the product (e.g., dairy, produce, bakery).'),
  historicalSalesData: z.string().describe('Historical sales data for the product category, in JSON format.'),
  currentStockLevel: z.number().describe('The current stock level for the product category.'),
  leadTimeDays: z.number().describe('The lead time in days for reordering the product category.'),
  seasonalTrends: z
    .string()
    .optional()
    .describe('Optional: Any known seasonal trends affecting demand, in JSON format.'),
});
export type PredictDemandInput = z.infer<typeof PredictDemandInputSchema>;

const PredictDemandOutputSchema = z.object({
  predictedDemand: z.number().describe('The predicted demand for the product category over the next lead time period.'),
  recommendedOrderQuantity: z
    .number()
    .describe('The recommended order quantity to meet the predicted demand and optimize stock levels.'),
  confidenceLevel: z.number().describe('A confidence level (0-1) indicating the reliability of the prediction.'),
  explanation: z.string().describe('An explanation of the factors influencing the prediction.'),
});
export type PredictDemandOutput = z.infer<typeof PredictDemandOutputSchema>;

export async function predictDemand(input: PredictDemandInput): Promise<PredictDemandOutput> {
  return predictDemandFlow(input);
}

const prompt = ai.definePrompt({
  name: 'predictDemandPrompt',
  input: {schema: PredictDemandInputSchema},
  output: {schema: PredictDemandOutputSchema},
  prompt: `You are an expert in demand forecasting for supermarkets. Analyze the provided historical sales data, current stock levels, and any seasonal trends to predict future demand and recommend optimal order quantities.

  Product Category: {{{productCategory}}}
  Historical Sales Data: {{{historicalSalesData}}}
  Current Stock Level: {{{currentStockLevel}}}
  Lead Time (Days): {{{leadTimeDays}}}
  Seasonal Trends (if any): {{{seasonalTrends}}}

  Based on this information, provide the following:
  - Predicted Demand: The estimated demand for the product category over the next {{{leadTimeDays}}} days.
  - Recommended Order Quantity: The quantity of the product category that should be ordered to meet the predicted demand, taking into account the current stock level.
  - Confidence Level: A value between 0 and 1 indicating your confidence in the accuracy of the prediction.
  - Explanation: A brief explanation of the factors that influenced your prediction.

  Ensure that the predicted demand and recommended order quantity are realistic and consider potential fluctuations in demand.
  Remember to output the response in the format defined by the PredictDemandOutputSchema.
`,
});

const predictDemandFlow = ai.defineFlow(
  {
    name: 'predictDemandFlow',
    inputSchema: PredictDemandInputSchema,
    outputSchema: PredictDemandOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
