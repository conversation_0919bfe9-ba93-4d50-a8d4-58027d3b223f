
"use client"; // Make this a client component

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { DollarSign, Users, MoreHorizontal, Edit, FileWarning, Filter, PlusCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { User } from '@/types'; 
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';

const mockSubscribers: (User & { plan?: string, status?: 'active' | 'expired' | 'cancelled' })[] = [
  { id: 'owner001', username: 'owner', role: 'owner', name: 'Supermarket Owner', email: '<EMAIL>', subscriptionEndDate: '2024-12-31', plan: 'Premium Yearly', status: 'active' },
  { id: 'wholesaler001', username: 'wholesaler', role: 'wholesaler', name: 'Wholesale Supplier', email: '<EMAIL>', subscriptionEndDate: '2024-11-30', plan: 'Standard Monthly', status: 'active' },
  { id: 'owner002', username: 'anotherowner', role: 'owner', name: 'Corner Groceries', email: '<EMAIL>', subscriptionEndDate: '2023-10-31', plan: 'Basic Monthly', status: 'expired' },
  { id: 'wholesaler002', username: 'supplyco', role: 'wholesaler', name: 'Supply Co.', email: '<EMAIL>', subscriptionEndDate: '2024-01-15', plan: 'Premium Yearly', status: 'cancelled' },
];


export default function AdminSubscriptionsPage() {
  const t = useScopedI18n('adminSubscriptions'); 
  const tCommon = useScopedI18n('common');
  const tUserManagement = useScopedI18n('userManagement');
  const { toast } = useToast();

  const getStatusBadgeVariant = (status?: 'active' | 'expired' | 'cancelled') => {
    switch (status) {
      case 'active': return 'default'; 
      case 'expired': return 'secondary';
      case 'cancelled': return 'destructive';
      default: return 'outline';
    }
  };

  const getStatusTranslation = (status?: 'active' | 'expired' | 'cancelled') => {
    if (!status) return t('statusUnknown');
    return t(`status${status.charAt(0).toUpperCase() + status.slice(1)}` as any, undefined, status);
  }

  const handleCreatePlan = () => {
    toast({
      title: t('createNewPlanToastTitle'),
      description: t('createNewPlanToastDesc'),
    });
    // Placeholder for actual plan creation logic
  };

  return (
    <AuthenticatedLayout expectedRole="admin">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <DollarSign className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                <div>
                    <CardTitle>{t('manageSubscriptionsTitle')}</CardTitle>
                    <CardDescription>
                    {t('manageSubscriptionsDescription')}
                    </CardDescription>
                </div>
                 <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Button onClick={handleCreatePlan} variant="outline">
                        <PlusCircle className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('createNewPlanButton')}
                    </Button>
                </div>
            </div>
             <div className="flex items-center space-x-2 pt-4 rtl:space-x-reverse">
                <Filter className="h-5 w-5 text-muted-foreground" />
                <Input placeholder={t('filterPlaceholder')} className="max-w-sm" />
            </div>
          </CardHeader>
          <CardContent>
            <CardDescription className="mb-4 text-sm italic">
              {t('paymentTrackingInfo')} {t('planManagementInfo')}
            </CardDescription>
            {mockSubscribers.length === 0 ? (
                <p className="text-muted-foreground">{t('noSubscriptionsFound')}</p>
            ) : (
            <div className="rounded-md border">
                <Table>
                <TableHeader>
                    <TableRow>
                    <TableHead>{t('userHeader')}</TableHead>
                    <TableHead>{t('emailHeader')}</TableHead>
                    <TableHead>{t('roleHeader')}</TableHead>
                    <TableHead>{t('planHeader')}</TableHead>
                    <TableHead>{t('endDateHeader')}</TableHead>
                    <TableHead>{tCommon('status')}</TableHead>
                    <TableHead className="text-right rtl:text-left">{tCommon('actions')}</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {mockSubscribers.map((subscriber) => (
                    <TableRow key={subscriber.id}>
                        <TableCell className="font-medium">{subscriber.name || subscriber.username}</TableCell>
                        <TableCell>{subscriber.email}</TableCell>
                        <TableCell><Badge variant="outline" className="capitalize">{tUserManagement(`role_${subscriber.role}` as any, undefined, subscriber.role)}</Badge></TableCell>
                        <TableCell>{subscriber.plan || tCommon('N_A')}</TableCell>
                        <TableCell>{subscriber.subscriptionEndDate ? new Date(subscriber.subscriptionEndDate).toLocaleDateString() : tCommon('N_A')}</TableCell>
                        <TableCell>
                            <Badge variant={getStatusBadgeVariant(subscriber.status) as any} className="capitalize">
                                {getStatusTranslation(subscriber.status)}
                            </Badge>
                        </TableCell>
                        <TableCell className="text-right rtl:text-left">
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">{tCommon('actions')}</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('editSubscriptionItem')}
                            </DropdownMenuItem>
                            <DropdownMenuItem className="text-destructive focus:text-destructive focus:bg-destructive/10">
                                <FileWarning className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('cancelSubscriptionItem')}
                            </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                        </TableCell>
                    </TableRow>
                    ))}
                </TableBody>
                </Table>
            </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}
