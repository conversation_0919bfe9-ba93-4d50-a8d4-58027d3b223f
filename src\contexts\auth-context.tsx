"use client";

import type { Role, User } from '@/types';
import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { flussoUserLogin } from '@/lib/auth'; // Mock auth functions
import { useToast } from '@/hooks/use-toast';
import { useScopedI18n, useI18n } from '@/lib/i18n/client'; // For Client Components

interface AuthContextType {
  user: User | null;
  login: (username: string, pass: string) => Promise<void>;
  biometricLogin: (username: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const { toast } = useToast();
  const tAuth = useScopedI18n('auth'); // Scoped translations for auth messages
  const tCommon = useScopedI18n('common'); // For generic messages like "Error"


  useEffect(() => {
    // Check for persisted user session (e.g., from localStorage)
    try {
      const storedUser = localStorage.getItem('marketsyncUser');
      if (storedUser) {
        const parsedUser: User = JSON.parse(storedUser);
        // Basic validation of stored user object
        if (parsedUser && parsedUser.id && parsedUser.role) {
          setUser(parsedUser);
        } else {
          localStorage.removeItem('marketsyncUser'); // Clear invalid stored user
        }
      }
    } catch (error) {
      console.error("Error parsing stored user:", error);
      localStorage.removeItem('marketsyncUser');
    }
    setIsLoading(false);
  }, []);

  const login = async (username: string, pass: string) => {
    setIsLoading(true);
    try {
      const loggedInUser = await flussoUserLogin(username, pass); 
      if (loggedInUser) {
        setUser(loggedInUser);
        localStorage.setItem('marketsyncUser', JSON.stringify(loggedInUser)); 
        toast({ 
          title: tAuth('loginSuccess'), 
          description: tAuth('welcomeBack', { name: loggedInUser.name || loggedInUser.username }) 
        });
        router.push(`/${loggedInUser.role}/dashboard`);
      } else {
        toast({ 
          title: tAuth('loginFailed'), 
          description: tAuth('invalidCredentials'), 
          variant: 'destructive' 
        });
      }
    } catch (error) {
      toast({ 
        title: tAuth('loginError'), 
        description: tAuth('unexpectedError'), 
        variant: 'destructive' 
      });
      console.error("Login error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const biometricLogin = async (username: string) => {
    setIsLoading(true);
    try {
      const loggedInUser = await flussoUserLogin(username, 'biometric-auth');
      if (loggedInUser) {
        // Update user with biometric login timestamp
        const updatedUser = {
          ...loggedInUser,
          lastBiometricLogin: new Date().toISOString()
        };

        setUser(updatedUser);
        localStorage.setItem('marketsyncUser', JSON.stringify(updatedUser));
        toast({
          title: 'تم تسجيل الدخول بالبصمة',
          description: `مرحباً ${updatedUser.name || updatedUser.username}`
        });
        router.push(`/${updatedUser.role}/dashboard`);
      } else {
        toast({
          title: 'فشل تسجيل الدخول',
          description: 'لم يتم العثور على المستخدم',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'خطأ في تسجيل الدخول',
        description: 'حدث خطأ غير متوقع',
        variant: 'destructive'
      });
      console.error("Biometric login error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('marketsyncUser');
    toast({
      title: tAuth('loggedOut'),
      description: tAuth('loggedOutSuccess')
    });
    router.push('/');
  };

  return (
    <AuthContext.Provider value={{ user, login, biometricLogin, logout, isLoading, isAuthenticated: !!user }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
