
"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { BarChart3, DollarSign, ShoppingCart, TrendingUp, List, FileSignature } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { Button } from '@/components/ui/button';
import { PlaceholderChart } from '@/components/dashboard/placeholder-chart';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from '@/hooks/use-toast'; // Import useToast

// Mock data for demonstration
const mockReportStats = {
  totalSales: "YER 150,750.00",
  totalOrders: 1230,
  averageOrderValue: "YER 122.56",
};

const mockBestSellingProducts = [
  { id: "prod001", name: "Organic Fuji Apples", sales: 250, revenue: "YER 197.50" },
  { id: "prod002", name: "Whole Grain Bread Loaf", sales: 180, revenue: "YER 628.20" },
  { id: "prod005", name: "Ground Coffee Beans (1kg)", sales: 150, revenue: "YER 2325.00" },
  { id: "prod003", name: "Free-Range Eggs (Dozen)", sales: 120, revenue: "YER 598.80" },
  { id: "prod006", name: "Cheddar Cheese Block", sales: 90, revenue: "YER 405.00" },
];

export default function OwnerReportsPage() {
  const t = useScopedI18n('ownerSalesReports');
  const tCommon = useScopedI18n('common');
  const { toast } = useToast(); // Initialize useToast

  const handleGenerateReport = (reportType: string) => {
    toast({
      title: t('reportGenerationInProgressTitle'),
      description: t('reportGenerationInProgressDesc', { reportType }),
    });
    // In a real app, this would trigger the actual report generation logic.
    // For now, it just shows a toast.
  };


  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <BarChart3 className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('totalSales')}</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockReportStats.totalSales}</div>
              <p className="text-xs text-muted-foreground">+10.2% {t('fromLastMonth')}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('totalOrders')}</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockReportStats.totalOrders}</div>
              <p className="text-xs text-muted-foreground">+5.5% {t('fromLastMonth')}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('averageOrderValue')}</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockReportStats.averageOrderValue}</div>
              <p className="text-xs text-muted-foreground">+2.1% {t('fromLastMonth')}</p>
            </CardContent>
          </Card>
        </div>
        
        <div className="grid gap-6 md:grid-cols-2">
            <PlaceholderChart />
            <Card className="shadow-md">
                <CardHeader>
                    <CardTitle>{t('detailedReportsTitle')}</CardTitle>
                    <CardDescription>{t('detailedReportsDescription')}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                    <Button className="w-full" variant="outline" onClick={() => handleGenerateReport(t('generateDailyReport'))}>{t('generateDailyReport')}</Button>
                    <Button className="w-full" variant="outline" onClick={() => handleGenerateReport(t('generateWeeklyReport'))}>{t('generateWeeklyReport')}</Button>
                    <Button className="w-full" variant="outline" onClick={() => handleGenerateReport(t('generateMonthlyReport'))}>{t('generateMonthlyReport')}</Button>
                </CardContent>
            </Card>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center">
              <List className="mr-2 h-5 w-5 text-primary" />
              {t('bestSellingProducts')}
            </CardTitle>
            <CardDescription>
              {t('bestSellingProductsDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{tCommon('nav_productManagement')}</TableHead>
                    <TableHead className="text-center">{t('salesCount')}</TableHead>
                    <TableHead className="text-right">{t('revenueGenerated')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockBestSellingProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell className="text-center">{product.sales}</TableCell>
                      <TableCell className="text-right">{product.revenue}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            {mockBestSellingProducts.length === 0 && (
              <p className="text-center text-muted-foreground py-4">{tCommon('noData')}</p>
            )}
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <div className="flex items-center space-x-2">
                <FileSignature className="h-6 w-6 text-primary" />
                <CardTitle>{t('customReportsTitle')}</CardTitle>
            </div>
            <CardDescription>{t('customReportsDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-center py-6">
              {t('customReportsComingSoon')}
            </p>
             <Button className="w-full mt-4" variant="outline" onClick={() => handleGenerateReport(t('generateCustomReport'))} disabled>
                {t('generateCustomReport')}
            </Button>
          </CardContent>
        </Card>

      </div>
    </AuthenticatedLayout>
  );
}
