"use client";

import React, { useState, useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Info, 
  Bug, 
  XCircle,
  Activity,
  Clock,
  User,
  Calendar
} from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG';
  user: string;
  action: string;
  details: string;
}

interface LogsAnalyticsProps {
  logs: LogEntry[];
}

export function LogsAnalytics({ logs }: LogsAnalyticsProps) {
  const t = useScopedI18n('adminLogs');
  const tCommon = useScopedI18n('common');

  // Calculate analytics
  const analytics = useMemo(() => {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Filter logs by time periods
    const logsLast24h = logs.filter(log => new Date(log.timestamp) >= last24Hours);
    const logsLast7d = logs.filter(log => new Date(log.timestamp) >= last7Days);

    // Count by level
    const levelCounts = logs.reduce((acc, log) => {
      acc[log.level] = (acc[log.level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Count by user
    const userCounts = logs.reduce((acc, log) => {
      acc[log.user] = (acc[log.user] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Top users by activity
    const topUsers = Object.entries(userCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([user, count]) => ({ user, count }));

    // Activity by hour (last 24h)
    const hourlyActivity = Array.from({ length: 24 }, (_, hour) => {
      const count = logsLast24h.filter(log => {
        const logHour = new Date(log.timestamp).getHours();
        return logHour === hour;
      }).length;
      return { hour, count };
    });

    // Error trends
    const errorTrend = {
      last24h: logsLast24h.filter(log => log.level === 'ERROR').length,
      last7d: logsLast7d.filter(log => log.level === 'ERROR').length,
      total: levelCounts.ERROR || 0
    };

    // Recent critical events
    const criticalEvents = logs
      .filter(log => log.level === 'ERROR' || log.level === 'WARN')
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 5);

    return {
      total: logs.length,
      last24h: logsLast24h.length,
      last7d: logsLast7d.length,
      levelCounts,
      userCounts,
      topUsers,
      hourlyActivity,
      errorTrend,
      criticalEvents
    };
  }, [logs]);

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'ERROR': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'WARN': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'INFO': return <Info className="h-4 w-4 text-blue-500" />;
      case 'DEBUG': return <Bug className="h-4 w-4 text-gray-500" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'ERROR': return 'text-red-600 bg-red-50 border-red-200';
      case 'WARN': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'INFO': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'DEBUG': return 'text-gray-600 bg-gray-50 border-gray-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const maxHourlyCount = Math.max(...analytics.hourlyActivity.map(h => h.count));

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('totalLogs')}
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.total.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.last24h} {t('inLast24Hours')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('errorCount')}
            </CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {analytics.levelCounts.ERROR || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {analytics.errorTrend.last24h} {t('inLast24Hours')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('warningCount')}
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {analytics.levelCounts.WARN || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('systemWarnings')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('activeUsers')}
            </CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.keys(analytics.userCounts).length}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('uniqueUsers')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Log Level Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            {t('logLevelDistribution')}
          </CardTitle>
          <CardDescription>
            {t('distributionOfLogLevels')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(analytics.levelCounts).map(([level, count]) => {
              const percentage = analytics.total > 0 ? (count / analytics.total) * 100 : 0;
              return (
                <div key={level} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    {getLevelIcon(level)}
                    <span className="font-medium">{level}</span>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse flex-1 max-w-xs">
                    <Progress value={percentage} className="flex-1" />
                    <span className="text-sm font-medium w-16 text-right">
                      {count} ({percentage.toFixed(1)}%)
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Hourly Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            {t('hourlyActivity')}
          </CardTitle>
          <CardDescription>
            {t('activityLast24Hours')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-12 gap-1">
            {analytics.hourlyActivity.map(({ hour, count }) => {
              const height = maxHourlyCount > 0 ? (count / maxHourlyCount) * 100 : 0;
              return (
                <div key={hour} className="flex flex-col items-center">
                  <div 
                    className="w-full bg-primary rounded-sm mb-1 transition-all hover:bg-primary/80"
                    style={{ height: `${Math.max(height, 2)}px`, minHeight: '2px' }}
                    title={`${hour}:00 - ${count} logs`}
                  />
                  <span className="text-xs text-muted-foreground">
                    {hour.toString().padStart(2, '0')}
                  </span>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Top Active Users */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              {t('topActiveUsers')}
            </CardTitle>
            <CardDescription>
              {t('mostActiveUsers')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.topUsers.map(({ user, count }, index) => (
                <div key={user} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <span className="font-medium">{user}</span>
                  </div>
                  <Badge variant="secondary">
                    {count} {t('actions')}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Critical Events */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
              {t('recentCriticalEvents')}
            </CardTitle>
            <CardDescription>
              {t('latestErrorsAndWarnings')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.criticalEvents.map((event) => (
                <div key={event.id} className={`p-3 rounded-lg border ${getLevelColor(event.level)}`}>
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      {getLevelIcon(event.level)}
                      <Badge variant="outline" className="text-xs">
                        {event.level}
                      </Badge>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {new Date(event.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <p className="text-sm font-medium mb-1">{event.action}</p>
                  <p className="text-xs text-muted-foreground">{event.details}</p>
                </div>
              ))}
              
              {analytics.criticalEvents.length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  <AlertTriangle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">{t('noCriticalEvents')}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
