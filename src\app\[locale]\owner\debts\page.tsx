
"use client";

import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from '@/components/ui/badge';
import { BookText, CalendarIcon, PlusCircle, Edit, Trash2, Filter, DollarSign, Printer, ShoppingBag, Trash } from 'lucide-react';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import type { Debt, DebtItem, Product } from '@/types';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Form, FormField, FormItem, FormControl, FormMessage, FormLabel } from '@/components/ui/form';
import { getMockProducts } from '@/lib/mock-product-data';
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';


const initialMockDebtsData: Omit<Debt, 'id' | 'ownerId'>[] = [ 
  { partyName: 'John Customer', type: 'debt', amount: 150.75, date: '2024-07-10', dueDate: '2024-08-10', status: 'unpaid', notes: 'For groceries bought on credit.', items: [{productId: 'prod001', productName: 'Organic Fuji Apples', quantity: 10, price: 0.79, dataAiHint: 'apple'}, {productId: 'prod002', productName: 'Whole Grain Bread Loaf', quantity: 2, price: 3.49, dataAiHint: 'bread'}] },
  { partyName: 'Supplier GreenFarm', type: 'credit', amount: 500.00, date: '2024-07-15', status: 'unpaid', notes: 'Payment for last vegetable shipment.', items: [] },
  { partyName: 'Alice Neighbor', type: 'debt', amount: 25.50, date: '2024-06-20', status: 'paid', notes: 'Borrowed for milk & bread.', items: [{productId: 'prod003', productName: 'Free-Range Eggs (Dozen)', quantity: 1, price: 4.99, dataAiHint: 'eggs'}] },
];


const DEBTS_STORAGE_KEY = 'marketSyncOwnerDebts';

const getStoredDebts = (ownerId?: string): Debt[] => {
  if (typeof window === 'undefined') return [];
  const stored = localStorage.getItem(DEBTS_STORAGE_KEY);
  let allDebts: Debt[] = [];
  if (stored) {
    allDebts = JSON.parse(stored);
  } else {
    // Initialize with mock data only if localStorage is empty and for a specific mock owner
    if (ownerId === 'owner001') {
      allDebts = initialMockDebtsData.map((d, i) => ({ ...d, id: `debtInit${i}`, ownerId: 'owner001' }));
      localStorage.setItem(DEBTS_STORAGE_KEY, JSON.stringify(allDebts));
    }
  }
  return ownerId ? allDebts.filter(d => d.ownerId === ownerId) : allDebts;
};

const saveStoredDebts = (debts: Debt[]) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(DEBTS_STORAGE_KEY, JSON.stringify(debts));
  }
};


export default function OwnerDebtsPage() {
  const { user } = useAuth();
  const [allDebtsState, setAllDebtsState] = useState<Debt[]>([]); // Holds all debts from storage
  const [ownerDebts, setOwnerDebts] = useState<Debt[]>([]); // Filtered debts for the current owner
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingDebt, setEditingDebt] = useState<Debt | null>(null);
  const [availableProducts, setAvailableProducts] = useState<Product[]>([]);
  const [selectedProductToAdd, setSelectedProductToAdd] = useState<Product | null>(null);
  const [quantityToAdd, setQuantityToAdd] = useState<string | number>("1");
  const [priceToAdd, setPriceToAdd] = useState<string | number>("0");

  const { toast } = useToast();
  const t = useScopedI18n('debtsManagement');
  const tCommon = useScopedI18n('common');
  const tForm = useScopedI18n('form');

  const debtItemSchema = z.object({
    productId: z.string().min(1),
    productName: z.string().min(1),
    quantity: z.coerce.number().min(1, tForm('fieldInvalid', {field: t('itemQuantity')})),
    price: z.coerce.number().positive(tForm('fieldInvalid', {field: t('itemPrice')})),
    dataAiHint: z.string().optional(),
  });

  const debtFormSchema = z.object({
    partyName: z.string().min(1, tForm('fieldRequired', {field: t('partyName')})),
    type: z.enum(['debt', 'credit']),
    status: z.enum(['unpaid', 'paid', 'partially_paid']),
    date: z.date({ required_error: tForm('fieldRequired', {field: t('date')}) }),
    dueDate: z.date().optional(),
    notes: z.string().optional(),
    items: z.array(debtItemSchema).min(1, t('itemsRequiredError')),
  });

  type DebtFormValues = z.infer<typeof debtFormSchema>;
  
  const form = useForm<DebtFormValues>({
    resolver: zodResolver(debtFormSchema),
    defaultValues: {
      partyName: '',
      type: 'debt',
      status: 'unpaid',
      notes: '',
      items: [],
    },
  });

  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: "items"
  });

  useEffect(() => {
    if (user && typeof window !== 'undefined') {
      const allStoredDebts = getStoredDebts(); 
      setAllDebtsState(allStoredDebts);
      
      const productsForOwner = getMockProducts().filter(p => p.ownerId === user.id);
      setAvailableProducts(productsForOwner);
      
      const currentOwnerDebts = allStoredDebts.filter(d => d.ownerId === user.id);
      setOwnerDebts(currentOwnerDebts);
    }
  }, [user]);
  
  useEffect(() => {
    if (selectedProductToAdd) {
      setPriceToAdd(selectedProductToAdd.price.toString());
    } else {
      setPriceToAdd("0");
    }
    setQuantityToAdd("1");
  }, [selectedProductToAdd]);

  const watchItems = form.watch("items");
  const calculatedTotal = watchItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);


  const onSubmit = (data: DebtFormValues) => {
    console.log("OwnerDebtsPage: onSubmit called with data:", data); // Diagnostic log
    if (!user) {
      toast({ title: tCommon('error'), description: tForm('fieldRequired', {field: "User authentication"}), variant: "destructive"});
      return;
    }
    const totalAmount = data.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
    const newOrUpdatedDebt: Debt = {
      id: editingDebt ? editingDebt.id : `debt${Date.now()}`,
      ...data,
      amount: totalAmount,
      date: format(data.date, 'yyyy-MM-dd'),
      dueDate: data.dueDate ? format(data.dueDate, 'yyyy-MM-dd') : undefined,
      ownerId: user.id,
    };

    let updatedAllDebts;
    if (editingDebt) {
      updatedAllDebts = allDebtsState.map(d => d.id === newOrUpdatedDebt.id ? newOrUpdatedDebt : d);
      toast({ title: t('debtUpdated'), description: t('debtUpdatedDesc', {partyName: newOrUpdatedDebt.partyName }) });
    } else {
      updatedAllDebts = [newOrUpdatedDebt, ...allDebtsState];
      const typeDisplay = newOrUpdatedDebt.type === 'debt' ? t('typeDebt') : t('typeCredit');
      toast({ title: t('debtRecorded'), description: t('debtRecordedDesc', {type: typeDisplay, partyName: newOrUpdatedDebt.partyName}) });
    }
    setAllDebtsState(updatedAllDebts);
    setOwnerDebts(updatedAllDebts.filter(d => d.ownerId === user.id)); 
    saveStoredDebts(updatedAllDebts);

    form.reset({ partyName: '', type: 'debt', status: 'unpaid', notes: '', items: []});
    setIsFormOpen(false);
    setEditingDebt(null);
    setSelectedProductToAdd(null);
  };

  const handleEdit = (debt: Debt) => {
    setEditingDebt(debt);
    form.reset({
      ...debt,
      date: new Date(debt.date),
      dueDate: debt.dueDate ? new Date(debt.dueDate) : undefined,
      items: debt.items.map(item => ({...item})) 
    });
    setIsFormOpen(true);
  };

  const handleDelete = (debtId: string) => {
     if (!user) return;
    const updatedAllDebts = allDebtsState.filter(d => d.id !== debtId);
    setAllDebtsState(updatedAllDebts);
    setOwnerDebts(updatedAllDebts.filter(d => d.ownerId === user.id));
    saveStoredDebts(updatedAllDebts);
    toast({ title: t('debtRemoved'), description: t('debtRemovedDesc'), variant: "destructive" });
  };
  
  const getStatusBadgeVariant = (status: Debt['status']) => {
    if (status === 'paid') return 'default'; 
    if (status === 'partially_paid') return 'secondary';
    return 'destructive'; 
  };
  
  const handleAddItemToForm = () => {
    const currentQuantity = parseFloat(String(quantityToAdd));
    const currentPrice = parseFloat(String(priceToAdd));

    if (selectedProductToAdd && !isNaN(currentQuantity) && currentQuantity > 0 && !isNaN(currentPrice) && currentPrice >= 0) {
      append({
        productId: selectedProductToAdd.id,
        productName: selectedProductToAdd.name,
        quantity: currentQuantity,
        price: currentPrice,
        dataAiHint: selectedProductToAdd.dataAiHint,
      });
      setSelectedProductToAdd(null); 
      setQuantityToAdd("1");
      setPriceToAdd("0");
    } else {
      toast({ title: tCommon('error'), description: t('addItemError'), variant: 'destructive' });
    }
  };

  const handlePrintDebtInvoice = (debt: Debt) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const direction = tCommon('language') === 'Arabic' ? 'rtl' : 'ltr';
      let invoiceHTML = `<html><head><title>${t('invoiceTitle')} - ${debt.id}</title><style>`;
      invoiceHTML += `
        body { font-family: Arial, sans-serif; margin: 20px; color: #333; direction: ${direction}; }
        .invoice-box { max-width: 800px; margin: auto; padding: 30px; border: 1px solid #eee; box-shadow: 0 0 10px rgba(0, 0, 0, 0.15); font-size: 16px; line-height: 24px; }
        .invoice-header { text-align: center; margin-bottom: 20px; }
        .invoice-header h1 { margin: 0; font-size: 2em; color: hsl(var(--primary)); }
        .invoice-header p { margin: 2px 0; font-size: 0.9em; color: hsl(var(--muted-foreground)); }
        .details-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }
        .party-details strong, .invoice-details strong { display: block; margin-bottom: 5px; color: hsl(var(--foreground)); }
        .invoice-details { text-align: ${direction === 'rtl' ? 'left' : 'right'}; }
        .item-table { width: 100%; line-height: inherit; text-align: ${direction === 'rtl' ? 'right' : 'left'}; border-collapse: collapse; }
        .item-table td, .item-table th { padding: 8px; border-bottom: 1px solid #eee; }
        .item-table tr:last-child td { border-bottom: none; }
        .item-table th { background: hsl(var(--muted)); font-weight: bold; color: hsl(var(--foreground)); }
        .item-table .heading td { background: #eee; border-bottom: 1px solid #ddd; font-weight: bold; }
        .item-table .item td { border-bottom: 1px solid #eee; }
        .item-table .total td { border-top: 2px solid #eee; font-weight: bold; }
        .text-right { text-align: right; }
        .text-left { text-align: left; }
        .notes { margin-top: 30px; font-size: 0.9em; color: #555; }
        .footer { text-align: center; margin-top: 40px; font-size: 0.8em; color: #777; }
      `;
      invoiceHTML += `</style></head><body><div class="invoice-box">`;
      invoiceHTML += `<div class="invoice-header"><h1>${t('invoiceTitle')}</h1><p>${user?.name || t('storeNamePlaceholder')}</p><p>${t('storeAddressPlaceholder')}</p></div>`;
      invoiceHTML += `<div class="details-grid">`;
      invoiceHTML += `<div class="party-details"><strong>${t('invoiceTo')}:</strong>${debt.partyName}<br>${t('debtTypeLabel')}: ${debt.type === 'debt' ? t('typeDebt') : t('typeCredit')}</div>`;
      invoiceHTML += `<div class="invoice-details"><strong>${t('invoiceNumber')}:</strong> ${debt.id}<br><strong>${t('date')}:</strong> ${format(new Date(debt.date), "PP")}<br>${debt.dueDate ? `<strong>${t('dueDateOptional').replace(' (Optional)','').replace('(اختياري)', '')}</strong>: ${format(new Date(debt.dueDate), "PP")}` : ''}</div>`;
      invoiceHTML += `</div>`;
      invoiceHTML += `<table class="item-table"><thead><tr><th>${t('itemProduct')}</th><th class="${direction === 'rtl' ? 'text-left' : 'text-right'}">${t('itemQuantity')}</th><th class="${direction === 'rtl' ? 'text-left' : 'text-right'}">${t('itemPrice')}</th><th class="${direction === 'rtl' ? 'text-left' : 'text-right'}">${t('itemSubtotal')}</th></tr></thead><tbody>`;
      debt.items.forEach(item => {
        invoiceHTML += `<tr class="item"><td>${item.productName}</td><td class="${direction === 'rtl' ? 'text-left' : 'text-right'}">${item.quantity}</td><td class="${direction === 'rtl' ? 'text-left' : 'text-right'}">YER ${item.price.toFixed(2)}</td><td class="${direction === 'rtl' ? 'text-left' : 'text-right'}">YER ${(item.quantity * item.price).toFixed(2)}</td></tr>`;
      });
      invoiceHTML += `</tbody></table>`;
      invoiceHTML += `<div class="${direction === 'rtl' ? 'text-left' : 'text-right'}" style="margin-top: 20px;"><strong>${t('totalAmount')}: YER ${debt.amount.toFixed(2)}</strong></div>`;
      if (debt.notes) {
        invoiceHTML += `<div class="notes"><strong>${t('notesOptional').replace(' (Optional)','').replace('(اختياري)', '')}</strong><p>${debt.notes}</p></div>`;
      }
      invoiceHTML += `<div class="footer">${t('thankYouMessage')}</div>`;
      invoiceHTML += `</div></body></html>`;
      printWindow.document.write(invoiceHTML);
      printWindow.document.close();
      printWindow.focus(); 
      printWindow.print();
    } else {
      toast({ title: tCommon('error'), description: t('printWindowError'), variant: 'destructive' });
    }
  };

 if (!user) {
    return (
      <AuthenticatedLayout expectedRole="owner">
        <div className="flex flex-col justify-center items-center h-64 p-4">
          <p className="text-muted-foreground">{tCommon('error')}: {tForm('fieldRequired', {field: "User authentication"})}</p>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center space-x-3">
            <BookText className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
              <p className="text-muted-foreground">
                {t('description')}
              </p>
            </div>
          </div>
          <Button onClick={() => { setEditingDebt(null); form.reset({ partyName: '', type: 'debt', status: 'unpaid', notes: '', items: []}); setIsFormOpen(true); setSelectedProductToAdd(null); setQuantityToAdd("1"); setPriceToAdd("0");}} className="bg-accent hover:bg-accent/90 text-accent-foreground">
            <PlusCircle className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('recordNewDebtCredit')}
          </Button>
        </div>

        {isFormOpen && (
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>{editingDebt ? t('formTitleEdit') : t('formTitleRecord')}</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="partyName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel htmlFor="partyName">{t('partyName')}</FormLabel>
                        <FormControl>
                        <Input id="partyName" placeholder={t('partyNamePlaceholder')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                 <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel htmlFor="type">{t('type')}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                          <SelectTrigger id="type">
                            <SelectValue placeholder={t('selectType')} />
                          </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="debt">{t('typeDebt')}</SelectItem>
                            <SelectItem value="credit">{t('typeCredit')}</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel htmlFor="status">{t('status')}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                           <FormControl>
                          <SelectTrigger id="status">
                            <SelectValue placeholder={t('selectStatus')} />
                          </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="unpaid">{t('statusUnpaid')}</SelectItem>
                            <SelectItem value="paid">{t('statusPaid')}</SelectItem>
                            <SelectItem value="partially_paid">{t('statusPartiallyPaid')}</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                   <FormField
                        control={form.control}
                        name="date"
                        render={({ field }) => (
                        <FormItem className="flex flex-col">
                            <FormLabel htmlFor="date">{t('date')}</FormLabel>
                            <Popover>
                            <PopoverTrigger asChild>
                                <FormControl>
                                <Button
                                variant={"outline"}
                                className={`w-full justify-start text-left font-normal ${!field.value && "text-muted-foreground"}`}
                                >
                                <CalendarIcon className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
                                {field.value ? format(field.value, "PPP") : <span>{tCommon('pickADate')}</span>}
                                </Button>
                                </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                                <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                initialFocus
                                />
                            </PopoverContent>
                            </Popover>
                           <FormMessage />
                        </FormItem>
                        )}
                    />
                </div>
                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                   
                    <FormField
                        control={form.control}
                        name="dueDate"
                        render={({ field }) => (
                        <FormItem className="flex flex-col">
                            <FormLabel htmlFor="dueDate">{t('dueDateOptional')}</FormLabel>
                            <Popover>
                            <PopoverTrigger asChild>
                                 <FormControl>
                                <Button
                                variant={"outline"}
                                className={`w-full justify-start text-left font-normal ${!field.value && "text-muted-foreground"}`}
                                >
                                <CalendarIcon className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
                                {field.value ? format(field.value, "PPP") : <span>{t('pickDueDate')}</span>}
                                </Button>
                                </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                                <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                />
                            </PopoverContent>
                            </Popover>
                             <FormMessage />
                        </FormItem>
                        )}
                    />
                </div>

                {/* Items Section */}
                <div className="space-y-4 border-t pt-4">
                  <h3 className="text-lg font-medium">{t('itemsForDebt')}</h3>
                  {/* Add Item Sub-Form */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-2 items-end p-2 border rounded-md">
                    <div className="md:col-span-2">
                      <Label htmlFor="product-select">{t('selectProduct')}</Label>
                      <Select
                        onValueChange={(productId) => {
                          const product = availableProducts.find(p => p.id === productId);
                          setSelectedProductToAdd(product || null);
                        }}
                        value={selectedProductToAdd?.id || ""}
                      >
                        <SelectTrigger id="product-select">
                          <SelectValue placeholder={t('selectProductPlaceholder')} />
                        </SelectTrigger>
                        <SelectContent>
                          {availableProducts.map(p => (
                            <SelectItem key={p.id} value={p.id}>{p.name} (YER {p.price.toFixed(2)})</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="quantity-to-add">{t('itemQuantity')}</Label>
                      <Input id="quantity-to-add" type="number" value={quantityToAdd} onChange={e => setQuantityToAdd(e.target.value)} min="1" />
                    </div>
                     <div>
                      <Label htmlFor="price-to-add">{t('itemPrice')}</Label>
                      <Input id="price-to-add" type="number" value={priceToAdd} onChange={e => setPriceToAdd(e.target.value)} step="0.01" />
                    </div>
                    <Button type="button" onClick={handleAddItemToForm} className="md:col-span-4 bg-secondary hover:bg-secondary/80 text-secondary-foreground mt-2 md:mt-0">
                      <ShoppingBag className="mr-2 h-4 w-4" /> {t('addItemButton')}
                    </Button>
                  </div>

                  {/* Display Added Items */}
                  <div className="space-y-2">
                    {fields.map((item, index) => (
                      <div key={item.id} className="flex items-center justify-between gap-2 p-2 border rounded-md bg-muted/50">
                        <div>
                          <p className="font-medium">{item.productName}</p>
                          <p className="text-sm text-muted-foreground">{t('itemQuantity')}: {item.quantity} @ YER {item.price.toFixed(2)} {t('each')}</p>
                        </div>
                        <div className="text-right rtl:text-left">
                            <p className="font-medium">YER {(item.quantity * item.price).toFixed(2)}</p>
                            <Button type="button" variant="ghost" size="icon" onClick={() => remove(index)} aria-label={tCommon('remove')}>
                                <Trash className="h-4 w-4 text-destructive" />
                            </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                  {fields.length > 0 && (
                    <div className="text-right font-semibold text-lg mt-2 rtl:text-left">
                      {t('totalAmount')}: YER {calculatedTotal.toFixed(2)}
                    </div>
                  )}
                  <FormField name="items" control={form.control} render={({fieldState}) => <FormMessage>{fieldState.error?.message}</FormMessage>} />
                </div>


                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel htmlFor="notes">{t('notesOptional')}</FormLabel>
                      <FormControl>
                      <Textarea id="notes" placeholder={t('notesPlaceholder')} {...field} />
                      </FormControl>
                       <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end space-x-2 rtl:space-x-reverse">
                  <Button type="button" variant="outline" onClick={() => { setIsFormOpen(false); setEditingDebt(null); form.reset({ partyName: '', type: 'debt', status: 'unpaid', notes: '', items: []}); setSelectedProductToAdd(null); setQuantityToAdd("1"); setPriceToAdd("0"); }}>{tCommon('cancel')}</Button>
                  <Button type="submit" className="bg-accent hover:bg-accent/90 text-accent-foreground">{editingDebt ? tCommon('save') : t('recordNewDebtCredit').split(' ')[0]}</Button>
                </div>
              </form>
              </Form>
            </CardContent>
          </Card>
        )}

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{t('recordsTitle')}</CardTitle>
            <CardDescription>
              {t('recordsDescription')}
            </CardDescription>
            <div className="mt-4">
                 <Input placeholder={t('filterRecordsPlaceholder')} className="max-w-sm" />
            </div>
          </CardHeader>
          <CardContent>
            {ownerDebts.length === 0 ? (
              <p className="text-muted-foreground text-center py-6">{t('noRecords')}</p>
            ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('partyName')}</TableHead>
                    <TableHead>{t('type')}</TableHead>
                    <TableHead className="text-right rtl:text-left">{t('amount')}</TableHead>
                    <TableHead className="text-center">{t('itemsCount')}</TableHead>
                    <TableHead>{t('date')}</TableHead>
                    <TableHead>{t('dueDateOptional').replace(' (Optional)','').replace('(اختياري)', '')}</TableHead>
                    <TableHead>{t('status')}</TableHead>
                    <TableHead className="text-right rtl:text-left">{tCommon('actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {ownerDebts.map((debt) => (
                    <TableRow key={debt.id}>
                      <TableCell className="font-medium">{debt.partyName}</TableCell>
                      <TableCell>
                        <Badge variant={debt.type === 'debt' ? 'outline' : 'secondary'} className="capitalize">
                            {debt.type === 'debt' ? t('typeDebt') : t('typeCredit')}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right rtl:text-left">YER {debt.amount.toFixed(2)}</TableCell>
                      <TableCell className="text-center">{debt.items.length}</TableCell>
                      <TableCell>{format(new Date(debt.date), "PP")}</TableCell>
                      <TableCell>{debt.dueDate ? format(new Date(debt.dueDate), "PP") : tCommon('N_A')}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(debt.status) as any} className="capitalize">
                            {t(`status${debt.status.charAt(0).toUpperCase() + debt.status.slice(1).replace('_', '')}` as any, undefined, debt.status.replace('_', ' '))}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right rtl:text-left space-x-1 rtl:space-x-reverse">
                        <Button variant="ghost" size="icon" onClick={() => handlePrintDebtInvoice(debt)} aria-label={t('printInvoice')}>
                            <Printer className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleEdit(debt)} aria-label={tCommon('edit')}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="icon" className="text-destructive hover:text-destructive" aria-label={tCommon('delete')}>
                                <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>{tCommon('areYouSure')}</AlertDialogTitle>
                              <AlertDialogDescription>
                                {tCommon('thisActionCannotBeUndone')} {t('debtRemovedDesc')} {debt.partyName}.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>{tCommon('cancel')}</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleDelete(debt.id)} className="bg-destructive hover:bg-destructive/90 text-destructive-foreground">
                                {tCommon('delete')}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            )}
          </CardContent>
           <CardFooter className="text-sm text-muted-foreground">
                {t('recordsCount', { count: ownerDebts.length.toString(), total: ownerDebts.length.toString() })}
            </CardFooter>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}

