"use client";

import { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  CreditCard, 
  Search, 
  Filter, 
  Plus, 
  Minus,
  Eye,
  Edit,
  DollarSign,
  Users,
  TrendingUp,
  TrendingDown,
  Al<PERSON>Triangle,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { BalanceAdjustmentDialog } from '@/components/employee/balance-adjustment-dialog';
import { CustomerBalanceDetails } from '@/components/employee/customer-balance-details';

// Mock data types
interface CustomerBalance {
  id: string;
  customerName: string;
  email?: string;
  phone?: string;
  currentBalance: number;
  creditLimit: number;
  lastPaymentDate?: string;
  lastPaymentAmount?: number;
  status: 'good' | 'warning' | 'overdue' | 'blocked';
  totalOrders: number;
  totalSpent: number;
}

interface BalanceTransaction {
  id: string;
  customerId: string;
  date: string;
  type: 'sale' | 'payment' | 'return' | 'adjustment';
  amount: number;
  balanceAfter: number;
  reason: string;
  processedBy: string;
}

interface BalanceOverview {
  totalCustomers: number;
  totalDebt: number;
  totalCredit: number;
  netBalance: number;
}

export default function EmployeeManageBalancesPage() {
  const t = useScopedI18n('employeeManageBalances');
  const tCommon = useScopedI18n('common');
  const { user } = useAuth();
  const { toast } = useToast();

  const [customers, setCustomers] = useState<CustomerBalance[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<CustomerBalance[]>([]);
  const [overview, setOverview] = useState<BalanceOverview>({
    totalCustomers: 0,
    totalDebt: 0,
    totalCredit: 0,
    netBalance: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerBalance | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showAdjustDialog, setShowAdjustDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);

  // Check permissions
  const hasPermission = user?.role === 'employee' && user?.permissions?.canManageCustomerBalances;

  useEffect(() => {
    if (hasPermission) {
      loadBalances();
    }
  }, [hasPermission]);

  useEffect(() => {
    filterCustomers();
  }, [customers, searchTerm, statusFilter]);

  const loadBalances = async () => {
    setIsLoading(true);
    try {
      // Mock data - replace with actual API call
      const mockCustomers: CustomerBalance[] = [
        {
          id: '1',
          customerName: 'أحمد محمد',
          email: '<EMAIL>',
          phone: '+967771234567',
          currentBalance: -1500,
          creditLimit: 5000,
          lastPaymentDate: '2024-01-15',
          lastPaymentAmount: 2000,
          status: 'warning',
          totalOrders: 25,
          totalSpent: 45000
        },
        {
          id: '2',
          customerName: 'فاطمة علي',
          email: '<EMAIL>',
          phone: '+967771234568',
          currentBalance: 500,
          creditLimit: 3000,
          lastPaymentDate: '2024-01-20',
          lastPaymentAmount: 1000,
          status: 'good',
          totalOrders: 18,
          totalSpent: 32000
        },
        {
          id: '3',
          customerName: 'محمد حسن',
          email: '<EMAIL>',
          phone: '+967771234569',
          currentBalance: -3500,
          creditLimit: 2000,
          lastPaymentDate: '2023-12-10',
          lastPaymentAmount: 500,
          status: 'overdue',
          totalOrders: 42,
          totalSpent: 78000
        }
      ];

      setCustomers(mockCustomers);
      
      // Calculate overview
      const totalCustomers = mockCustomers.length;
      const totalDebt = mockCustomers.reduce((sum, c) => sum + Math.min(0, c.currentBalance), 0);
      const totalCredit = mockCustomers.reduce((sum, c) => sum + Math.max(0, c.currentBalance), 0);
      const netBalance = totalCredit + totalDebt;

      setOverview({
        totalCustomers,
        totalDebt: Math.abs(totalDebt),
        totalCredit,
        netBalance
      });

    } catch (error) {
      console.error('Error loading balances:', error);
      toast({
        title: t('errorAdjustingBalance'),
        description: 'Failed to load customer balances',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filterCustomers = () => {
    let filtered = customers;

    if (searchTerm) {
      filtered = filtered.filter(customer =>
        customer.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.phone?.includes(searchTerm)
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(customer => customer.status === statusFilter);
    }

    setFilteredCustomers(filtered);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      good: { label: t('statusGood'), variant: 'default' as const, icon: CheckCircle },
      warning: { label: t('statusWarning'), variant: 'secondary' as const, icon: AlertTriangle },
      overdue: { label: t('statusOverdue'), variant: 'destructive' as const, icon: XCircle },
      blocked: { label: t('statusBlocked'), variant: 'outline' as const, icon: Clock }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-YE', {
      style: 'currency',
      currency: 'YER',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (!hasPermission) {
    return (
      <AuthenticatedLayout expectedRole="employee">
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-center text-destructive">
                {tCommon('accessDenied')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-muted-foreground">
                You don't have permission to manage customer balances.
              </p>
            </CardContent>
          </Card>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <CreditCard className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        {/* Balance Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('totalCustomers')}</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.totalCustomers}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('totalDebt')}</CardTitle>
              <TrendingDown className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">
                {formatCurrency(overview.totalDebt)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('totalCredit')}</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(overview.totalCredit)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('netBalance')}</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${overview.netBalance >= 0 ? 'text-green-600' : 'text-destructive'}`}>
                {formatCurrency(overview.netBalance)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle>{t('customerBalancesTitle')}</CardTitle>
            <CardDescription>{t('customerBalancesDesc')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('searchCustomerPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-[200px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder={t('filterByStatus')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('allStatuses')}</SelectItem>
                  <SelectItem value="good">{t('statusGood')}</SelectItem>
                  <SelectItem value="warning">{t('statusWarning')}</SelectItem>
                  <SelectItem value="overdue">{t('statusOverdue')}</SelectItem>
                  <SelectItem value="blocked">{t('statusBlocked')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {isLoading ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">{t('loadingBalances')}</p>
              </div>
            ) : filteredCustomers.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">{t('noCustomersFound')}</p>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t('customerName')}</TableHead>
                      <TableHead>{t('currentBalance')}</TableHead>
                      <TableHead>{t('creditLimit')}</TableHead>
                      <TableHead>{t('lastPayment')}</TableHead>
                      <TableHead>{t('balanceStatus')}</TableHead>
                      <TableHead>{tCommon('actions')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredCustomers.map((customer) => (
                      <TableRow key={customer.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{customer.customerName}</div>
                            {customer.email && (
                              <div className="text-sm text-muted-foreground">{customer.email}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className={customer.currentBalance >= 0 ? 'text-green-600' : 'text-destructive'}>
                            {formatCurrency(customer.currentBalance)}
                          </span>
                        </TableCell>
                        <TableCell>{formatCurrency(customer.creditLimit)}</TableCell>
                        <TableCell>
                          {customer.lastPaymentDate ? (
                            <div>
                              <div>{format(new Date(customer.lastPaymentDate), 'yyyy-MM-dd')}</div>
                              <div className="text-sm text-muted-foreground">
                                {formatCurrency(customer.lastPaymentAmount || 0)}
                              </div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">{tCommon('N_A')}</span>
                          )}
                        </TableCell>
                        <TableCell>{getStatusBadge(customer.status)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedCustomer(customer);
                                setShowDetailsDialog(true);
                              }}
                            >
                              <Eye className="h-4 w-4" />
                              {t('viewDetailsButton')}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedCustomer(customer);
                                setShowAdjustDialog(true);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                              {t('adjustBalanceButton')}
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Balance Adjustment Dialog */}
        <BalanceAdjustmentDialog
          customer={selectedCustomer}
          isOpen={showAdjustDialog}
          onClose={() => {
            setShowAdjustDialog(false);
            setSelectedCustomer(null);
          }}
          onAdjustmentComplete={() => {
            loadBalances();
          }}
        />

        {/* Customer Details Dialog */}
        <CustomerBalanceDetails
          customer={selectedCustomer}
          isOpen={showDetailsDialog}
          onClose={() => {
            setShowDetailsDialog(false);
            setSelectedCustomer(null);
          }}
        />
      </div>
    </AuthenticatedLayout>
  );
}
