# ميزة أرصدة العملاء - Customer Balances Feature

## نظرة عامة

تم تطوير ميزة "أرصدة العملاء" بشكل كامل لتوفير نظام شامل لإدارة الحسابات المالية للعملاء ومعلومات الائتمان. هذه الميزة متاحة للموظفين الذين لديهم صلاحية `canManageCustomerBalances`.

## الميزات الرئيسية

### 1. لوحة التحكم الرئيسية
- **إحصائيات سريعة**: عرض إجمالي العملاء، الديون، الائتمان، والرصيد الصافي
- **البحث والتصفية**: البحث بالاسم، رقم الهاتف، أو رقم العميل
- **جدول العملاء**: عرض تفصيلي لجميع العملاء وأرصدتهم

### 2. إدارة العملاء
- **عرض تفاصيل العميل**: معلومات شخصية ومالية شاملة
- **إدارة المعاملات**: إضافة ديون، ائتمان، أو مدفوعات
- **تعديل حدود الائتمان**: تحديث حدود الائتمان للعملاء
- **تتبع حالة العميل**: نظام تصنيف تلقائي (جيد، تحذير، متأخر)

### 3. الإحصائيات والتحليلات
- **توزيع حالات العملاء**: رسوم بيانية لحالات العملاء
- **المؤشرات المالية**: متوسط الديون والائتمان
- **تحليل المخاطر**: معدلات التأخير واستخدام الائتمان
- **أكبر المدينين**: قائمة بالعملاء ذوي الديون الأكبر
- **آخر المدفوعات**: تتبع المدفوعات الحديثة

### 4. التقارير
- **تقارير متنوعة**: ملخص عام، تفصيلي، المتأخرين فقط
- **خيارات الترتيب**: حسب الاسم، الرصيد، أو الحالة
- **تصدير البيانات**: تصدير بصيغة CSV أو JSON
- **إحصائيات مالية**: ملخص شامل للوضع المالي

## الواجهات والمكونات

### الصفحة الرئيسية
```
src/app/[locale]/employee/customer-balances/page.tsx
```

### المكونات المساعدة
```
src/components/employee/customer-balance-report.tsx
src/components/employee/customer-balance-stats.tsx
```

## هيكل البيانات

### CustomerBalance Interface
```typescript
interface CustomerBalance {
  id: string;
  customerName: string;
  customerId: string;
  totalDebt: number;
  totalCredit: number;
  netBalance: number;
  creditLimit: number;
  lastPayment: string;
  lastTransaction: string;
  status: 'good' | 'warning' | 'overdue';
  phone?: string;
  email?: string;
}
```

### Transaction Interface
```typescript
interface Transaction {
  id: string;
  customerId: string;
  type: 'debt' | 'credit' | 'payment';
  amount: number;
  description: string;
  date: string;
  employeeId: string;
  employeeName: string;
}
```

## نظام الصلاحيات

الميزة تتطلب صلاحية `canManageCustomerBalances` في كائن المستخدم:

```typescript
user?.permissions?.canManageCustomerBalances
```

إذا لم تكن الصلاحية متوفرة، يتم عرض رسالة "ليس لديك صلاحية للوصول إلى هذه الصفحة".

## نظام التصنيف التلقائي

يتم تصنيف العملاء تلقائياً حسب حالتهم المالية:

- **جيد (Good)**: الرصيد أقل من 80% من حد الائتمان
- **تحذير (Warning)**: الرصيد بين 80% و 100% من حد الائتمان
- **متأخر (Overdue)**: الرصيد يتجاوز حد الائتمان

## الوظائف المتاحة

### 1. إضافة معاملة جديدة
- **أنواع المعاملات**: دين، ائتمان، دفعة
- **تحديث تلقائي**: للأرصدة والحالات
- **تسجيل الموظف**: تتبع من قام بالمعاملة

### 2. تعديل حد الائتمان
- **تحديث فوري**: للحالة بناءً على الحد الجديد
- **تحذيرات أمان**: تأكيد قبل التحديث
- **سجل التغييرات**: تتبع جميع تعديلات حدود الائتمان
- **أسباب التغيير**: تسجيل مبرر لكل تعديل
- **التحديث الجماعي**: تطبيق تغييرات نسبية على مجموعة من العملاء
- **نقاط الائتمان**: عرض تقييم ائتماني للعملاء

## صفحات إدارة حدود الائتمان المخصصة

### صفحة الموظف - تحديد حدود الائتمان
**المسار**: `/employee/set-credit-limits`
**الصلاحية المطلوبة**: `canSetCreditLimits`

#### الميزات:
- **إحصائيات سريعة**: عرض توزيع العملاء حسب الحالة
- **البحث والتصفية**: بحث متقدم وتصفية حسب الحالة
- **جدول العملاء**: عرض شامل مع نقاط الائتمان والائتمان المتاح
- **تعديل فردي**: تعديل حد ائتمان عميل واحد مع تسجيل السبب
- **التحديث الجماعي**: تطبيق نسبة تغيير على مجموعة من العملاء
- **سجل التغييرات**: عرض تاريخ جميع التعديلات
- **التحقق من الصلاحيات**: منع الوصول للموظفين غير المخولين

#### واجهة التحديث الجماعي:
- **نسبة التغيير**: زيادة أو تقليل بنسبة مئوية
- **نطاق التطبيق**: تحديد حد أدنى وأعلى للعملاء المتأثرين
- **معاينة التحديث**: عرض عدد العملاء المتأثرين قبل التطبيق
- **أسباب محددة مسبقاً**: قائمة بأسباب التحديث الشائعة

### صفحة المالك - تقييم حدود ائتمان العملاء
**المسار**: `/owner/set-credit-limits`
**الصلاحية**: مالك المتجر

#### الميزات المتقدمة:
- **إعدادات السياسة**: تحديد القواعد العامة للائتمان
- **صلاحيات كاملة**: تعديل حدود الائتمان دون قيود
- **إحصائيات شاملة**: عرض إجمالي الائتمان الممنوح
- **ملخص السياسة**: عرض الإعدادات الحالية للائتمان

#### إعدادات سياسة الائتمان:
- **الحد الأقصى للائتمان**: أعلى حد يمكن منحه لأي عميل
- **الحد الافتراضي**: الحد المبدئي للعملاء الجدد
- **عتبة المخاطر**: النسبة التي تؤدي إلى تحذير
- **حد الموافقة التلقائية**: الحد الذي يمكن للموظفين الموافقة عليه

### 3. البحث والتصفية
- **بحث متقدم**: بالاسم، الهاتف، أو رقم العميل
- **تصفية فورية**: نتائج فورية أثناء الكتابة

### 4. تصدير البيانات
- **CSV**: للاستخدام في Excel
- **JSON**: للتكامل مع أنظمة أخرى
- **تسمية تلقائية**: بالتاريخ الحالي

## التحديثات المستقبلية المقترحة

1. **تكامل مع قاعدة البيانات**: استبدال البيانات التجريبية ببيانات حقيقية
2. **إشعارات تلقائية**: تنبيهات للمدفوعات المتأخرة
3. **تقارير مجدولة**: إرسال تقارير دورية
4. **تحليلات متقدمة**: رسوم بيانية وتوقعات
5. **تكامل مع الرسائل**: إرسال تذكيرات للعملاء
6. **سجل المعاملات**: تتبع تفصيلي لجميع التغييرات
7. **صلاحيات متدرجة**: مستويات مختلفة من الوصول
8. **نسخ احتياطية**: حفظ تلقائي للبيانات المهمة

## الاستخدام

1. **تسجيل الدخول**: كموظف بصلاحية إدارة أرصدة العملاء
2. **الوصول للصفحة**: من القائمة الجانبية > أرصدة العملاء
3. **استكشاف التبويبات**: إدارة العملاء، الإحصائيات، التقارير
4. **إدارة العملاء**: عرض التفاصيل وإضافة معاملات
5. **مراجعة الإحصائيات**: تحليل الوضع المالي العام
6. **إنشاء التقارير**: تصدير البيانات للمراجعة

## الأمان والخصوصية

- **فحص الصلاحيات**: في كل عملية
- **تسجيل العمليات**: تتبع جميع التغييرات
- **حماية البيانات**: عدم عرض معلومات حساسة
- **تشفير المعاملات**: حماية البيانات المالية

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من الصلاحيات المطلوبة
3. تواصل مع فريق الدعم الفني
4. قدم تفاصيل المشكلة والخطوات المتبعة

---

**تم التطوير**: يناير 2024  
**الحالة**: مكتمل وجاهز للاستخدام  
**المطور**: Augment Agent  
**النسخة**: 1.0.0
