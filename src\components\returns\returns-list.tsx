'use client';

import { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Eye, Search, Filter } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import type { Return } from '@/types';

interface ReturnsListProps {
  returns: Return[];
  onViewReturn: (returnId: string) => void;
  onProcessReturn?: (returnId: string) => void;
  showActions?: boolean;
}

export function ReturnsList({ returns, onViewReturn, onProcessReturn, showActions = true }: ReturnsListProps) {
  const t = useScopedI18n('employeeReturnsManagement');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [reasonFilter, setReasonFilter] = useState<string>('all');

  const filteredReturns = useMemo(() => {
    return returns.filter(returnItem => {
      const matchesSearch = 
        returnItem.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        returnItem.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        returnItem.orderId.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || returnItem.status === statusFilter;
      const matchesReason = reasonFilter === 'all' || returnItem.returnReason === reasonFilter;
      
      return matchesSearch && matchesStatus && matchesReason;
    });
  }, [returns, searchTerm, statusFilter, reasonFilter]);

  const getStatusBadgeVariant = (status: Return['status']) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'approved': return 'default';
      case 'processed': return 'outline';
      case 'refunded': return 'default';
      case 'rejected': return 'destructive';
      default: return 'secondary';
    }
  };

  const getStatusText = (status: Return['status']) => {
    switch (status) {
      case 'pending': return t('statusPending');
      case 'approved': return t('statusApproved');
      case 'processed': return t('statusProcessed');
      case 'refunded': return t('statusRefunded');
      case 'rejected': return t('statusRejected');
      default: return status;
    }
  };

  const getReasonText = (reason: Return['returnReason']) => {
    switch (reason) {
      case 'defective': return t('reasonDefective');
      case 'wrong_item': return t('reasonWrongItem');
      case 'not_satisfied': return t('reasonNotSatisfied');
      case 'damaged_shipping': return t('reasonDamagedShipping');
      case 'expired': return t('reasonExpired');
      case 'other': return t('reasonOther');
      default: return reason;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-YE', {
      style: 'currency',
      currency: 'YER',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-YE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="h-5 w-5" />
          {t('allReturns')}
        </CardTitle>
        <CardDescription>
          {t('description')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder={t('searchPlaceholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="flex gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t('filterByStatus')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('allStatuses')}</SelectItem>
                <SelectItem value="pending">{t('statusPending')}</SelectItem>
                <SelectItem value="approved">{t('statusApproved')}</SelectItem>
                <SelectItem value="processed">{t('statusProcessed')}</SelectItem>
                <SelectItem value="refunded">{t('statusRefunded')}</SelectItem>
                <SelectItem value="rejected">{t('statusRejected')}</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={reasonFilter} onValueChange={setReasonFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t('filterByReason')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('allReasons')}</SelectItem>
                <SelectItem value="defective">{t('reasonDefective')}</SelectItem>
                <SelectItem value="wrong_item">{t('reasonWrongItem')}</SelectItem>
                <SelectItem value="not_satisfied">{t('reasonNotSatisfied')}</SelectItem>
                <SelectItem value="damaged_shipping">{t('reasonDamagedShipping')}</SelectItem>
                <SelectItem value="expired">{t('reasonExpired')}</SelectItem>
                <SelectItem value="other">{t('reasonOther')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Returns Table */}
        {filteredReturns.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">{t('noReturnsMessage')}</p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('returnId')}</TableHead>
                  <TableHead>{t('customerName')}</TableHead>
                  <TableHead>{t('orderId')}</TableHead>
                  <TableHead>{t('returnDate')}</TableHead>
                  <TableHead>{t('totalAmount')}</TableHead>
                  <TableHead>{t('status')}</TableHead>
                  {showActions && <TableHead>{t('actions')}</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredReturns.map((returnItem) => (
                  <TableRow key={returnItem.id}>
                    <TableCell className="font-medium">{returnItem.id}</TableCell>
                    <TableCell>{returnItem.customerName}</TableCell>
                    <TableCell>{returnItem.orderId}</TableCell>
                    <TableCell>{formatDate(returnItem.createdAt)}</TableCell>
                    <TableCell>{formatCurrency(returnItem.totalReturnAmount)}</TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(returnItem.status)}>
                        {getStatusText(returnItem.status)}
                      </Badge>
                    </TableCell>
                    {showActions && (
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onViewReturn(returnItem.id)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            {t('view')}
                          </Button>
                          {onProcessReturn && returnItem.status === 'pending' && (
                            <Button
                              variant="default"
                              size="sm"
                              onClick={() => onProcessReturn(returnItem.id)}
                            >
                              {t('process')}
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
        
        <div className="text-sm text-muted-foreground">
          عرض {filteredReturns.length} من {returns.length} مرتجع
        </div>
      </CardContent>
    </Card>
  );
}
