
import type { locales } from "@/lib/i18n/config";

export type Role = 'admin' | 'owner' | 'employee' | 'customer' | 'wholesaler' | 'agent';
export type Locale = (typeof locales)[number];

export type BusinessType = 'grocery' | 'pharmacy' | 'building_materials' | 'electrical_tools' | 'supermarket';

export interface EmployeePermissions {
  canReceiveOrders?: boolean;
  canGrantCredit?: boolean;
  canManageCustomerBalances?: boolean;
  canProcessSalesReturns?: boolean;
  canManageProducts?: boolean; 
  canSetCreditLimits?: boolean; 
  canManageShifts?: boolean; 
  canViewPerformanceReports?: boolean; 
  canAccessCommunicationTools?: boolean; 
  canViewInventoryReports?: boolean; 
  canPrintReports?: boolean; 
  canAccessProductLookup?: boolean; 
  canManageTasks?: boolean; 
  canDispatchOrders?: boolean;
  canRecordTransactions?: boolean;
}

export interface User {
  id: string;
  username: string;
  phoneNumber: string;
  email?: string;
  role: Role;
  name?: string;
  subscriptionEndDate?: string; // ISO date string
  createdById?: string; // ID of the owner who created this user (customer or employee)
  permissions?: EmployeePermissions; // For employee role
  businessType?: BusinessType; // For owner and wholesaler roles
  biometricEnabled?: boolean; // Whether biometric authentication is enabled
  biometricCredentials?: BiometricCredential[]; // Stored biometric credentials
  lastBiometricLogin?: string; // Last biometric login timestamp
}

// Biometric Authentication Types
export interface BiometricCredential {
  id: string;
  credentialId: string; // WebAuthn credential ID
  publicKey: string; // Public key for verification
  deviceName?: string; // Device name for identification
  createdAt: string;
  lastUsed?: string;
  isActive: boolean;
}

export interface BiometricAuthRequest {
  userId: string;
  challenge: string;
  allowCredentials: PublicKeyCredentialDescriptor[];
}

export interface BiometricAuthResponse {
  credentialId: string;
  authenticatorData: string;
  signature: string;
  userHandle?: string;
  clientDataJSON: string;
}

export interface BiometricRegistrationRequest {
  userId: string;
  challenge: string;
  rp: {
    name: string;
    id: string;
  };
  user: {
    id: string;
    name: string;
    displayName: string;
  };
  pubKeyCredParams: PublicKeyCredentialParameters[];
  authenticatorSelection?: AuthenticatorSelectionCriteria;
  timeout?: number;
  attestation?: AttestationConveyancePreference;
}

export interface BiometricRegistrationResponse {
  credentialId: string;
  publicKey: string;
  attestationObject: string;
  clientDataJSON: string;
  deviceName?: string;
}

export interface Product {
  id: string;
  name: string;
  category: string;
  price: number;
  imageUrl?: string;
  stock?: number;
  minOrderQuantity?: number; // For wholesalers
  ownerId?: string; // For products belonging to a supermarket owner
  wholesalerId?: string; // For products belonging to a wholesaler
  dataAiHint?: string;
  isOnline?: boolean; // Added for e-commerce visibility
  supplier?: string; // Added for inventory page
  lastRestock?: string; // Added for inventory page
}

export interface CartItem {
  id: string; // Product ID
  name: string;
  price: number;
  quantity: number;
  imageUrl?: string;
  ownerId?: string; // The owner of the store this item belongs to
  dataAiHint?: string;
}

export interface Order {
  id: string;
  customerId: string;
  customerName?: string; 
  customerAddress?: string; 
  items: { productId: string; productName: string; quantity: number; price: number; dataAiHint?: string }[];
  totalAmount: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  createdAt: string; // ISO date string
  employeeId?: string; // Employee who processed/packed
  deliveryAgentId?: string; // Agent assigned for delivery
  approvedByEmployeeId?: string; // ID of employee who approved
  approvedByEmployeeName?: string; // Name of employee who approved
  approvedAt?: string; // ISO date string of approval time
}

export interface Subscription {
  userId: string;
  type: 'monthly' | 'quarterly' | 'yearly';
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  status: 'active' | 'expired' | 'cancelled';
}

export interface HistoricalSaleData {
  date: string; // YYYY-MM-DD
  quantitySold: number;
  price: number;
}

export interface InventoryPrediction {
  productCategory: string;
  predictedDemand: number;
  recommendedOrderQuantity: number;
  confidenceLevel: number;
  explanation: string;
}

// PredictDemandInput is specific to the AI flow, keep it if used by server actions
// but for general use, prefer more generic types like HistoricalSaleData.
export type PredictDemandInput = {
  productCategory: string;
  historicalSalesData: string; // JSON string
  currentStockLevel: number;
  leadTimeDays: number;
  seasonalTrends?: string; // JSON string
};

export interface DebtItem {
  productId: string;
  productName: string;
  quantity: number;
  price: number; // Price at the time of debt recording
  dataAiHint?: string; // Optional, from product
}

export interface Debt {
  id: string;
  partyName: string; // Debtor or Creditor
  type: 'debt' | 'credit'; // 'debt' (money owed to owner) or 'credit' (money owner owes)
  amount: number; // This will now be the total amount of items + any additional charges
  date: string; // ISO date string
  dueDate?: string; // ISO date string, optional
  status: 'unpaid' | 'paid' | 'partially_paid';
  notes?: string;
  items: DebtItem[]; 
  ownerId: string; // ID of the owner this debt record belongs to
}

// Mock Transaction type for simple cash sales, distinct from Debt.
export interface CashTransaction {
    id: string;
    orderId?: string; // Optional, could be a reference
    amount: number;
    paymentMethod: 'Cash' | 'AlKuraimiBank' | 'EWallet';
    timestamp: string; // ISO or simple time string
    employeeId: string; // Who recorded it
    ownerId: string; // Which owner's store
    notes?: string;
}

// Return Management Types
export interface ReturnItem {
    id: string;
    productId: string;
    productName: string;
    originalQuantity: number;
    returnQuantity: number;
    originalPrice: number;
    returnAmount: number;
    condition: 'excellent' | 'good' | 'damaged' | 'defective';
    reason: string;
    imageUrl?: string;
}

export interface Return {
    id: string;
    orderId: string;
    customerId: string;
    customerName: string;
    customerPhone?: string;
    items: ReturnItem[];
    totalReturnAmount: number;
    returnReason: 'defective' | 'wrong_item' | 'not_satisfied' | 'damaged_shipping' | 'expired' | 'other';
    returnReasonDetails?: string;
    status: 'pending' | 'approved' | 'rejected' | 'processed' | 'refunded';
    refundMethod: 'cash' | 'credit' | 'exchange' | 'store_credit';
    refundAmount?: number;
    exchangeItems?: { productId: string; productName: string; quantity: number; price: number }[];
    createdAt: string; // ISO date string
    processedAt?: string; // ISO date string
    processedBy?: string; // Employee ID who processed
    processedByName?: string; // Employee name who processed
    approvedBy?: string; // Manager/Owner ID who approved
    approvedByName?: string; // Manager/Owner name who approved
    notes?: string;
    attachments?: string[]; // URLs to receipt images, product photos, etc.
}

export interface ReturnPolicy {
    id: string;
    ownerId: string;
    maxReturnDays: number;
    allowedReasons: string[];
    requiresApproval: boolean;
    refundMethods: ('cash' | 'credit' | 'exchange' | 'store_credit')[];
    restockingFee?: number; // Percentage
    conditions: {
        defective: { allowedDays: number; requiresProof: boolean };
        wrongItem: { allowedDays: number; requiresProof: boolean };
        notSatisfied: { allowedDays: number; requiresProof: boolean };
        damagedShipping: { allowedDays: number; requiresProof: boolean };
        expired: { allowedDays: number; requiresProof: boolean };
    };
    createdAt: string;
    updatedAt: string;
}

// Customer Management Types
export interface Customer {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    address?: string;
    dateJoined: string;
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate?: string;
    status: 'active' | 'inactive' | 'blocked';
    loyaltyPoints?: number;
    preferredCategories?: string[];
    notes?: string;
    creditLimit?: number;
    currentBalance?: number;
    ownerId: string;
}

export interface CustomerSegment {
    id: string;
    name: string;
    description: string;
    criteria: {
        minTotalSpent?: number;
        maxTotalSpent?: number;
        minOrders?: number;
        maxOrders?: number;
        categories?: string[];
        lastOrderDays?: number;
    };
    customerCount: number;
    ownerId: string;
}

// Financial Management Types
export interface FinancialTransaction {
    id: string;
    type: 'income' | 'expense' | 'refund' | 'tax' | 'fee';
    category: string;
    amount: number;
    description: string;
    date: string;
    paymentMethod?: 'cash' | 'card' | 'bank_transfer' | 'digital_wallet';
    reference?: string; // Order ID, Invoice number, etc.
    employeeId?: string;
    customerId?: string;
    supplierId?: string;
    ownerId: string;
    attachments?: string[];
    status: 'pending' | 'completed' | 'cancelled';
}

// Supplier Management Types
export interface Supplier {
    id: string;
    name: string;
    companyName?: string;
    contactPerson: string;
    email?: string;
    phone: string;
    address?: string;
    city?: string;
    country?: string;
    taxId?: string;
    businessLicense?: string;
    category: string; // e.g., 'food', 'electronics', 'clothing'
    products: string[]; // Array of product categories they supply
    paymentTerms: string; // e.g., 'Net 30', 'Cash on delivery'
    creditLimit?: number;
    currentBalance: number; // Amount owed to supplier
    rating: number; // 1-5 star rating
    status: 'active' | 'inactive' | 'blocked' | 'pending_approval';
    dateAdded: string;
    lastOrderDate?: string;
    totalOrders: number;
    totalPurchaseAmount: number;
    averageDeliveryTime: number; // in days
    qualityRating: number; // 1-5
    reliabilityRating: number; // 1-5
    priceCompetitiveness: number; // 1-5
    notes?: string;
    ownerId: string;
    bankDetails?: {
        bankName: string;
        accountNumber: string;
        routingNumber?: string;
        swiftCode?: string;
    };
    certifications?: string[]; // ISO, Halal, Organic, etc.
    minimumOrderAmount?: number;
    leadTime: number; // days
    returnPolicy?: string;
    warrantyTerms?: string;
}

export interface PurchaseOrder {
    id: string;
    orderNumber: string;
    supplierId: string;
    supplierName: string;
    orderDate: string;
    expectedDeliveryDate?: string;
    actualDeliveryDate?: string;
    status: 'draft' | 'sent' | 'confirmed' | 'partially_received' | 'completed' | 'cancelled';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    items: PurchaseOrderItem[];
    subtotal: number;
    taxAmount: number;
    shippingCost: number;
    totalAmount: number;
    paymentTerms: string;
    paymentStatus: 'pending' | 'partial' | 'paid' | 'overdue';
    deliveryAddress: string;
    notes?: string;
    attachments?: string[];
    createdBy: string;
    approvedBy?: string;
    approvalDate?: string;
    ownerId: string;
    currency: string;
    exchangeRate?: number;
    discountAmount?: number;
    discountPercentage?: number;
}

export interface PurchaseOrderItem {
    id: string;
    productId?: string;
    productName: string;
    description?: string;
    sku?: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    receivedQuantity: number;
    pendingQuantity: number;
    unit: string; // kg, pieces, boxes, etc.
    specifications?: string;
    notes?: string;
}

export interface SupplierEvaluation {
    id: string;
    supplierId: string;
    supplierName: string;
    evaluationDate: string;
    evaluationPeriod: {
        startDate: string;
        endDate: string;
    };
    evaluatedBy: string;
    overallRating: number; // 1-5
    criteria: {
        qualityRating: number;
        deliveryRating: number;
        priceRating: number;
        serviceRating: number;
        communicationRating: number;
        reliabilityRating: number;
    };
    metrics: {
        onTimeDeliveryRate: number; // percentage
        qualityDefectRate: number; // percentage
        orderAccuracyRate: number; // percentage
        responseTime: number; // hours
        totalOrdersEvaluated: number;
        totalValueEvaluated: number;
    };
    strengths: string[];
    weaknesses: string[];
    improvementSuggestions: string[];
    actionItems: string[];
    nextEvaluationDate: string;
    status: 'draft' | 'completed' | 'approved';
    ownerId: string;
    comments?: string;
}

export interface SupplyDelivery {
    id: string;
    purchaseOrderId: string;
    purchaseOrderNumber: string;
    supplierId: string;
    supplierName: string;
    deliveryDate: string;
    expectedDate: string;
    status: 'scheduled' | 'in_transit' | 'delivered' | 'delayed' | 'cancelled';
    trackingNumber?: string;
    carrier?: string;
    items: DeliveryItem[];
    totalItems: number;
    receivedItems: number;
    damagedItems: number;
    missingItems: number;
    deliveryNotes?: string;
    receivedBy: string;
    inspectedBy?: string;
    inspectionNotes?: string;
    photos?: string[];
    signature?: string;
    ownerId: string;
}

export interface DeliveryItem {
    purchaseOrderItemId: string;
    productName: string;
    expectedQuantity: number;
    receivedQuantity: number;
    damagedQuantity: number;
    condition: 'good' | 'damaged' | 'expired' | 'defective';
    batchNumber?: string;
    expiryDate?: string;
    notes?: string;
}

export interface SupplierPayment {
    id: string;
    supplierId: string;
    supplierName: string;
    purchaseOrderId?: string;
    amount: number;
    paymentDate: string;
    dueDate: string;
    status: 'pending' | 'paid' | 'overdue' | 'cancelled';
    paymentMethod: 'cash' | 'bank_transfer' | 'check' | 'credit_card';
    reference: string;
    notes?: string;
    attachments?: string[];
    paidBy: string;
    ownerId: string;
}

// Notification Management Types
export interface Notification {
    id: string;
    title: string;
    message: string;
    type: 'info' | 'warning' | 'error' | 'success' | 'system' | 'order' | 'task' | 'alert';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    targetRole?: Role | 'all';
    targetUserId?: string; // For specific user notifications
    senderId?: string; // Who sent the notification (admin, system, etc.)
    senderName?: string;
    isRead: boolean;
    createdAt: string; // ISO date string
    readAt?: string; // ISO date string
    expiresAt?: string; // ISO date string for auto-expiring notifications
    actionUrl?: string; // URL to navigate when notification is clicked
    actionLabel?: string; // Label for action button
    metadata?: {
        orderId?: string;
        customerId?: string;
        taskId?: string;
        amount?: number;
        [key: string]: any;
    };
}

export interface NotificationPreferences {
    userId: string;
    emailNotifications: boolean;
    pushNotifications: boolean;
    inAppNotifications: boolean;
    notificationTypes: {
        orders: boolean;
        tasks: boolean;
        system: boolean;
        alerts: boolean;
        lowStock: boolean;
        newCustomers: boolean;
    };
}

export interface FinancialSummary {
    totalRevenue: number;
    totalExpenses: number;
    netProfit: number;
    grossMargin: number;
    operatingExpenses: number;
    taxesPaid: number;
    period: string; // e.g., "2024-01" for monthly, "2024-Q1" for quarterly
    ownerId: string;
}

export interface ExpenseCategory {
    id: string;
    name: string;
    description?: string;
    budgetLimit?: number;
    currentSpent: number;
    ownerId: string;
}

// Analytics Types
export interface SalesAnalytics {
    period: string;
    totalSales: number;
    totalOrders: number;
    averageOrderValue: number;
    topProducts: { productId: string; productName: string; quantity: number; revenue: number }[];
    salesByCategory: { category: string; amount: number; percentage: number }[];
    salesByHour: { hour: number; sales: number }[];
    salesByDay: { day: string; sales: number }[];
    customerMetrics: {
        newCustomers: number;
        returningCustomers: number;
        customerRetentionRate: number;
    };
}

export interface InventoryAnalytics {
    totalProducts: number;
    lowStockItems: number;
    outOfStockItems: number;
    totalInventoryValue: number;
    fastMovingItems: { productId: string; productName: string; turnoverRate: number }[];
    slowMovingItems: { productId: string; productName: string; daysInStock: number }[];
    categoryPerformance: { category: string; revenue: number; margin: number }[];
}

export interface CustomerAnalytics {
    totalCustomers: number;
    activeCustomers: number;
    newCustomersThisMonth: number;
    customerLifetimeValue: number;
    topCustomers: { customerId: string; customerName: string; totalSpent: number }[];
    customersBySegment: { segment: string; count: number; percentage: number }[];
    churnRate: number;
}
