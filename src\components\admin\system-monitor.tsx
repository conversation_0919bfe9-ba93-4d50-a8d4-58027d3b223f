"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  Wifi, 
  Database, 
  Users, 
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Server,
  Globe,
  Clock
} from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    temperature: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    status: 'online' | 'offline' | 'slow';
    latency: number;
    bandwidth: number;
  };
  database: {
    status: 'connected' | 'disconnected' | 'slow';
    connections: number;
    queryTime: number;
  };
  activeUsers: number;
  uptime: number;
  lastUpdated: string;
}

// Mock system metrics generator
const generateMockMetrics = (): SystemMetrics => {
  return {
    cpu: {
      usage: Math.random() * 100,
      cores: 4,
      temperature: 45 + Math.random() * 20
    },
    memory: {
      used: 6.2 + Math.random() * 2,
      total: 16,
      percentage: 0
    },
    disk: {
      used: 120 + Math.random() * 50,
      total: 500,
      percentage: 0
    },
    network: {
      status: Math.random() > 0.1 ? 'online' : 'slow',
      latency: 20 + Math.random() * 100,
      bandwidth: 50 + Math.random() * 50
    },
    database: {
      status: Math.random() > 0.05 ? 'connected' : 'slow',
      connections: Math.floor(Math.random() * 50) + 10,
      queryTime: Math.random() * 100 + 10
    },
    activeUsers: Math.floor(Math.random() * 25) + 5,
    uptime: Date.now() - (Math.random() * 7 * 24 * 60 * 60 * 1000), // Random uptime up to 7 days
    lastUpdated: new Date().toISOString()
  };
};

export function SystemMonitor() {
  const t = useScopedI18n('adminLogs');
  const tCommon = useScopedI18n('common');

  const [metrics, setMetrics] = useState<SystemMetrics>(() => {
    const initial = generateMockMetrics();
    initial.memory.percentage = (initial.memory.used / initial.memory.total) * 100;
    initial.disk.percentage = (initial.disk.used / initial.disk.total) * 100;
    return initial;
  });
  
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      const newMetrics = generateMockMetrics();
      newMetrics.memory.percentage = (newMetrics.memory.used / newMetrics.memory.total) * 100;
      newMetrics.disk.percentage = (newMetrics.disk.used / newMetrics.disk.total) * 100;
      setMetrics(newMetrics);
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newMetrics = generateMockMetrics();
    newMetrics.memory.percentage = (newMetrics.memory.used / newMetrics.memory.total) * 100;
    newMetrics.disk.percentage = (newMetrics.disk.used / newMetrics.disk.total) * 100;
    setMetrics(newMetrics);
    
    setIsRefreshing(false);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'online':
      case 'connected':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            {t('statusOnline')}
          </Badge>
        );
      case 'slow':
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
            <AlertTriangle className="h-3 w-3 mr-1" />
            {t('statusSlow')}
          </Badge>
        );
      case 'offline':
      case 'disconnected':
        return (
          <Badge variant="destructive">
            <AlertTriangle className="h-3 w-3 mr-1" />
            {t('statusOffline')}
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatUptime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Activity className="h-5 w-5 mr-2" />
                {t('systemMonitor')}
              </CardTitle>
              <CardDescription>
                {t('realTimeSystemMetrics')}
              </CardDescription>
            </div>
            <Button 
              onClick={handleRefresh} 
              disabled={isRefreshing}
              variant="outline"
              size="sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              {t('refresh')}
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* System Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('cpuUsage')}
            </CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.cpu.usage.toFixed(1)}%</div>
            <Progress value={metrics.cpu.usage} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {metrics.cpu.cores} {t('cores')} • {metrics.cpu.temperature.toFixed(1)}°C
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('memoryUsage')}
            </CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.memory.percentage.toFixed(1)}%</div>
            <Progress value={metrics.memory.percentage} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {metrics.memory.used.toFixed(1)} GB / {metrics.memory.total} GB
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('diskUsage')}
            </CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.disk.percentage.toFixed(1)}%</div>
            <Progress value={metrics.disk.percentage} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {formatBytes(metrics.disk.used * 1024 * 1024 * 1024)} / {formatBytes(metrics.disk.total * 1024 * 1024 * 1024)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('activeUsers')}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeUsers}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {t('currentlyOnline')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Status */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Globe className="h-5 w-5 mr-2" />
              {t('networkStatus')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{t('connectionStatus')}</span>
              {getStatusBadge(metrics.network.status)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">{t('latency')}</span>
              <span className="text-sm font-medium">{metrics.network.latency.toFixed(0)}ms</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">{t('bandwidth')}</span>
              <span className="text-sm font-medium">{metrics.network.bandwidth.toFixed(1)} Mbps</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              {t('databaseStatus')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{t('connectionStatus')}</span>
              {getStatusBadge(metrics.database.status)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">{t('activeConnections')}</span>
              <span className="text-sm font-medium">{metrics.database.connections}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">{t('avgQueryTime')}</span>
              <span className="text-sm font-medium">{metrics.database.queryTime.toFixed(1)}ms</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            {t('systemInformation')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div>
              <span className="text-sm font-medium">{t('systemUptime')}</span>
              <p className="text-2xl font-bold">{formatUptime(metrics.uptime)}</p>
            </div>
            <div>
              <span className="text-sm font-medium">{t('lastUpdated')}</span>
              <p className="text-sm text-muted-foreground">
                {new Date(metrics.lastUpdated).toLocaleTimeString()}
              </p>
            </div>
            <div>
              <span className="text-sm font-medium">{t('systemHealth')}</span>
              <div className="flex items-center mt-1">
                {metrics.cpu.usage < 80 && metrics.memory.percentage < 85 && metrics.disk.percentage < 90 ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-600 mr-1" />
                    <span className="text-sm text-green-600">{t('healthy')}</span>
                  </>
                ) : (
                  <>
                    <AlertTriangle className="h-4 w-4 text-yellow-600 mr-1" />
                    <span className="text-sm text-yellow-600">{t('warning')}</span>
                  </>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
