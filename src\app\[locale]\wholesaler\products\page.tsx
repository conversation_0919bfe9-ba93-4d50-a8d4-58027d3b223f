
"use client";
import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Image from 'next/image';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Package, PlusCircle, Edit3, Trash2, Filter, ArrowUpDown, Eye, BarChart, Loader2 } from 'lucide-react';
import type { Product } from '@/types';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { getMockProducts, addMockProduct, updateMockProduct, deleteMockProduct } from '@/lib/mock-product-data'; 
import { useToast } from '@/hooks/use-toast';
import { useScopedI18n } from '@/lib/i18n/client'; 
import { useAuth } from '@/contexts/auth-context';


type WholesalerProduct = Product & { 
  stockStatus?: 'Available' | 'Low Stock' | 'Unavailable', 
  visibility?: 'Public' | 'Private' 
};


export default function WholesalerProductsPage() {
    const { user } = useAuth();
    const [products, setProducts] = useState<WholesalerProduct[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [filterText, setFilterText] = useState('');
    const { toast } = useToast();
    const t = useScopedI18n('wholesalerProducts'); 
    const tCommon = useScopedI18n('common');
    

    useEffect(() => {
        if (user) {
            loadProducts();
        } else {
            setIsLoading(false);
        }
    }, [user]);

    const loadProducts = () => {
        if (!user) return;
        setIsLoading(true);
        if (typeof window !== 'undefined') {
            const allProducts = getMockProducts();
            const wholesalerViewProducts = allProducts
                .filter(p => p.wholesalerId === user.id)
                .map(p => ({
                    ...p,
                    stockStatus: p.stock === undefined ? 'Unavailable' : p.stock === 0 ? 'Unavailable' : p.stock < (p.minOrderQuantity || 5) * 2 ? 'Low Stock' : 'Available', 
                    visibility: p.isOnline ? 'Public' : 'Private', 
                }));
            setProducts(wholesalerViewProducts);
        }
        setIsLoading(false);
    };
    
    const getStockBadgeVariant = (status?: 'Available' | 'Low Stock' | 'Unavailable') => {
        switch (status) {
        case 'Available': return 'default';
        case 'Low Stock': return 'secondary';
        case 'Unavailable': return 'destructive';
        default: return 'outline';
        }
    };

    const handleAddProduct = () => {
        if (!user) {
            toast({title: tCommon('error'), description: t('userNotAuthenticated') });
            return;
        }
        const newProdData: Omit<Product, 'id'> = {
            name: `${t('newProductDefaultName')} ${products.length + 1}`, 
            category: t('defaultCategory'), 
            price: 100.00,
            stock: 1000,
            minOrderQuantity: 100,
            isOnline: false, 
            dataAiHint: "bulk item",
            imageUrl: `https://picsum.photos/100/100`,
            wholesalerId: user.id, 
        };
        addMockProduct(newProdData);
        loadProducts();
        toast({title: t('productAddedTitle'), description: t('productAddedDesc')}); 
    };

    const handleEditProduct = (productId: string) => {
        const productToEdit = products.find(p => p.id === productId);
        if (productToEdit) {
            toast({title: t('editProductTitle'), description: t('editProductDesc', { productName: productToEdit.name })});
        }
    };
    
    const handleDeleteProduct = (productId: string) => {
        if(window.confirm(tCommon('areYouSure'))) { 
            deleteMockProduct(productId);
            loadProducts();
            toast({title: t('productDeletedTitle'), description: t('productDeletedDesc'), variant: "destructive"}); 
        }
    };

    const toggleVisibility = (product: WholesalerProduct) => {
        const updatedProductData = { ...product, isOnline: product.visibility === 'Private' }; 
        updateMockProduct(updatedProductData as Product); 
        loadProducts();
        toast({ title: t('visibilityChangedTitle'), description: t('visibilityChangedDesc', {productName: product.name, status: updatedProductData.isOnline ? t('statusPublic') : t('statusPrivate')})}); 
    };


    const filteredProducts = products.filter(product =>
        (product.name.toLowerCase().includes(filterText.toLowerCase()) ||
        product.category.toLowerCase().includes(filterText.toLowerCase())) &&
        product.wholesalerId === user?.id
    );

    if (isLoading && typeof window !== 'undefined' && (!localStorage.getItem('marketSyncMockProducts') || !user )) {
        return (
        <AuthenticatedLayout expectedRole="wholesaler">
            <div className="flex flex-col justify-center items-center h-64 p-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">{tCommon('loading')}</p>
            </div>
        </AuthenticatedLayout>
        );
    }
    if (!user) {
      return (
          <AuthenticatedLayout expectedRole="wholesaler">
              <div className="flex flex-col justify-center items-center h-64 p-4">
                  <p className="text-muted-foreground">{tCommon('error')}: {t('userNotAuthenticated')}</p>
              </div>
          </AuthenticatedLayout>
      );
    }


  return (
    <AuthenticatedLayout expectedRole="wholesaler">
      <div className="space-y-6 p-1">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-center space-x-3">
            <Package className="h-8 w-8 text-primary" />
            <div>
                <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
                <p className="text-muted-foreground">
                {t('description')}
                </p>
            </div>
            </div>
            <Button onClick={handleAddProduct} className="bg-accent hover:bg-accent/90 text-accent-foreground">
                <PlusCircle className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('addNewProductButton')}
            </Button>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{t('productListingsTitle')}</CardTitle>
            <CardDescription>
              {t('productListingsDesc')}
            </CardDescription>
            <div className="flex flex-col md:flex-row gap-2 mt-4 items-center">
                <div className="relative flex-grow w-full md:w-auto">
                    <Filter className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground rtl:right-2.5 rtl:left-auto" />
                    <Input 
                        placeholder={t('filterPlaceholder')} 
                        className="pl-8 w-full rtl:pr-8 rtl:pl-3"
                        value={filterText}
                        onChange={(e) => setFilterText(e.target.value)}
                     />
                </div>
                <Button variant="outline">
                    <ArrowUpDown className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('sortByButton')}
                </Button>
            </div>
          </CardHeader>
          <CardContent>
            {filteredProducts.length === 0 ? (
              <p className="text-muted-foreground text-center py-4">{t('noProductsMessage')}</p>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[80px] hidden sm:table-cell">{t('headerImage')}</TableHead>
                      <TableHead>{t('headerName')}</TableHead>
                      <TableHead className="hidden md:table-cell">{t('headerCategory')}</TableHead>
                      <TableHead className="text-right rtl:text-left">{t('headerPricePerUnit')}</TableHead>
                      <TableHead className="text-center hidden lg:table-cell">{t('headerMinQty')}</TableHead>
                      <TableHead className="text-center">{t('headerStock')}</TableHead>
                      <TableHead>{t('headerAvailability')}</TableHead>
                      <TableHead>{t('headerVisibility')}</TableHead>
                      <TableHead className="text-right rtl:text-left">{tCommon('actions')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell className="hidden sm:table-cell">
                          <Image 
                            src={product.imageUrl || `https://picsum.photos/100/100`} 
                            alt={product.name} 
                            width={50} 
                            height={50} 
                            className="rounded-md object-cover"
                            data-ai-hint={product.dataAiHint || product.category.toLowerCase()}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{product.name}</TableCell>
                        <TableCell className="hidden md:table-cell">{product.category}</TableCell>
                        <TableCell className="text-right rtl:text-left">YER {(product.price || 0).toFixed(2)}</TableCell>
                        <TableCell className="text-center hidden lg:table-cell">{product.minOrderQuantity || tCommon('N_A')}</TableCell>
                        <TableCell className="text-center">{product.stock}</TableCell>
                        <TableCell>
                            <Badge variant={getStockBadgeVariant(product.stockStatus) as any}>
                                {product.stockStatus ? t(`stockStatus${product.stockStatus.replace(' ', '')}` as any) : tCommon('N_A')}
                            </Badge>
                        </TableCell>
                        <TableCell>
                            <Badge variant={product.visibility === 'Public' ? 'outline' : 'secondary'}>
                                {product.visibility ? t(`visibility${product.visibility}` as any) : tCommon('N_A')}
                            </Badge>
                        </TableCell>
                        <TableCell className="text-right rtl:text-left">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">{t('openMenu')}</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEditProduct(product.id)}>
                                <Edit3 className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('menuEditProduct')}
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => toggleVisibility(product)}>
                                <Eye className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {product.visibility === 'Public' ? t('menuMakePrivate') : t('menuMakePublic')}
                              </DropdownMenuItem>
                               <DropdownMenuItem onClick={() => toast({title: t('analyticsTitle'), description:t('analyticsDesc')})}>
                                <BarChart className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('menuViewAnalytics')}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleDeleteProduct(product.id)} className="text-destructive focus:text-destructive focus:bg-destructive/10">
                                <Trash2 className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('menuDeleteProduct')}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}

