/* Admin Interface Enhancements */

/* Smooth transitions for all interactive elements */
.admin-card {
  @apply transition-all duration-300 ease-in-out;
}

.admin-card:hover {
  @apply transform scale-[1.02] shadow-xl;
}

/* Enhanced button styles */
.admin-button {
  @apply transition-all duration-200 ease-in-out;
  @apply hover:shadow-md active:scale-95;
}

/* Gradient backgrounds for stats cards */
.stats-card-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stats-card-green {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stats-card-yellow {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stats-card-red {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stats-card-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

/* Enhanced table styles */
.admin-table {
  @apply border-separate border-spacing-0;
}

.admin-table th {
  @apply bg-gradient-to-r from-muted/50 to-muted/30;
  @apply border-b-2 border-border;
  @apply font-semibold text-foreground;
}

.admin-table tr:hover {
  @apply bg-muted/30 transition-colors duration-200;
}

.admin-table td {
  @apply border-b border-border/50;
}

/* Enhanced form styles */
.admin-form-field {
  @apply space-y-2;
}

.admin-form-field label {
  @apply text-sm font-medium text-foreground;
}

.admin-form-field input,
.admin-form-field select,
.admin-form-field textarea {
  @apply transition-all duration-200;
  @apply focus:ring-2 focus:ring-primary/20 focus:border-primary;
}

/* Loading animations */
.admin-loading {
  @apply animate-pulse;
}

.admin-skeleton {
  @apply bg-muted rounded animate-pulse;
}

/* Enhanced badges */
.admin-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  @apply transition-all duration-200;
}

.admin-badge-success {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.admin-badge-warning {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.admin-badge-error {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

.admin-badge-info {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
}

/* Enhanced navigation */
.admin-nav-item {
  @apply flex items-center space-x-2 px-3 py-2 rounded-md;
  @apply transition-all duration-200;
  @apply hover:bg-muted/50 hover:text-primary;
}

.admin-nav-item.active {
  @apply bg-primary/10 text-primary font-medium;
}

/* Enhanced cards */
.admin-card-enhanced {
  @apply bg-card border border-border rounded-lg shadow-sm;
  @apply transition-all duration-300;
  @apply hover:shadow-md hover:border-primary/20;
}

.admin-card-header {
  @apply p-6 pb-4;
}

.admin-card-content {
  @apply p-6 pt-0;
}

/* Enhanced progress bars */
.admin-progress {
  @apply w-full bg-muted rounded-full h-2;
}

.admin-progress-bar {
  @apply h-2 rounded-full transition-all duration-500 ease-out;
}

.admin-progress-bar-primary {
  @apply bg-primary;
}

.admin-progress-bar-success {
  @apply bg-green-500;
}

.admin-progress-bar-warning {
  @apply bg-yellow-500;
}

.admin-progress-bar-error {
  @apply bg-red-500;
}

/* Enhanced modals */
.admin-modal {
  @apply fixed inset-0 z-50 flex items-center justify-center;
  @apply bg-black/50 backdrop-blur-sm;
}

.admin-modal-content {
  @apply bg-card border border-border rounded-lg shadow-xl;
  @apply max-w-md w-full mx-4;
  @apply animate-in fade-in-0 zoom-in-95 duration-200;
}

/* Enhanced tooltips */
.admin-tooltip {
  @apply absolute z-50 px-2 py-1 text-xs;
  @apply bg-popover text-popover-foreground;
  @apply border border-border rounded shadow-md;
  @apply animate-in fade-in-0 zoom-in-95 duration-200;
}

/* Enhanced search */
.admin-search {
  @apply relative;
}

.admin-search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2;
  @apply text-muted-foreground;
}

.admin-search-input {
  @apply pl-10 pr-4 py-2 w-full;
  @apply border border-border rounded-md;
  @apply focus:ring-2 focus:ring-primary/20 focus:border-primary;
  @apply transition-all duration-200;
}

/* Enhanced filters */
.admin-filter-group {
  @apply flex flex-wrap gap-2;
}

.admin-filter-button {
  @apply px-3 py-1.5 text-sm rounded-md border;
  @apply transition-all duration-200;
  @apply hover:bg-muted/50;
}

.admin-filter-button.active {
  @apply bg-primary text-primary-foreground border-primary;
}

/* Enhanced status indicators */
.admin-status {
  @apply inline-flex items-center space-x-1;
}

.admin-status-dot {
  @apply w-2 h-2 rounded-full;
}

.admin-status-online {
  @apply bg-green-500;
}

.admin-status-offline {
  @apply bg-red-500;
}

.admin-status-idle {
  @apply bg-yellow-500;
}

/* Enhanced data visualization */
.admin-chart-container {
  @apply p-4 bg-card border border-border rounded-lg;
}

.admin-metric {
  @apply text-center p-4;
}

.admin-metric-value {
  @apply text-3xl font-bold text-foreground;
}

.admin-metric-label {
  @apply text-sm text-muted-foreground;
}

.admin-metric-change {
  @apply text-xs font-medium;
}

.admin-metric-change.positive {
  @apply text-green-600;
}

.admin-metric-change.negative {
  @apply text-red-600;
}

/* Enhanced responsive design */
@media (max-width: 768px) {
  .admin-card {
    @apply transform-none hover:scale-100;
  }
  
  .admin-table {
    @apply text-sm;
  }
  
  .admin-modal-content {
    @apply max-w-full mx-2;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .admin-card-enhanced {
    @apply shadow-lg shadow-black/20;
  }
  
  .admin-modal {
    @apply bg-black/70;
  }
}

/* RTL support */
[dir="rtl"] .admin-search-icon {
  @apply left-auto right-3;
}

[dir="rtl"] .admin-search-input {
  @apply pl-4 pr-10;
}

[dir="rtl"] .admin-nav-item {
  @apply space-x-reverse;
}

/* Print styles */
@media print {
  .admin-card {
    @apply shadow-none border border-gray-300;
  }
  
  .admin-button {
    @apply hidden;
  }
  
  .admin-nav-item {
    @apply hidden;
  }
}
