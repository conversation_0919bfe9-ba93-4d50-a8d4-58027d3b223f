
"use client";

import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { ShoppingCart, Search, Filter, Star, Heart, Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import type { Product, CartItem } from '@/types';
import { getMockProducts } from '@/lib/mock-product-data'; 
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { Label } from '@/components/ui/label'; 

// Adding rating and reviews to Product type for UI consistency (optional, could be derived)
type ProductWithUIDetails = Product & {
    rating?: number;
    reviews?: number;
    isFavorite?: boolean;
};

const CART_STORAGE_KEY_PREFIX = 'marketSyncCart_';

export default function CustomerShopPage() {
  const [allProducts, setAllProducts] = useState<ProductWithUIDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const t = useScopedI18n('customerShop');
  const tCommon = useScopedI18n('common');
  const { user: authUser } = useAuth();
  const { toast } = useToast();
  const [productQuantities, setProductQuantities] = useState<{ [productId: string]: number }>({});

  useEffect(() => {
    setIsLoading(true);
     if (typeof window !== 'undefined' && authUser) {
        let productsFromStorage = getMockProducts();
        let displayProducts: Product[] = [];

        // Customers associated with an owner see that owner's online products
        if (authUser.role === 'customer' && authUser.createdById) {
             displayProducts = productsFromStorage.filter(p => p.isOnline && p.ownerId === authUser.createdById);
        } 
        // Employees associated with an owner see that owner's online products
        else if (authUser.role === 'employee' && authUser.createdById) {
             displayProducts = productsFromStorage.filter(p => p.isOnline && p.ownerId === authUser.createdById);
        }
        // Other scenarios (e.g. general customer not tied to an owner, or other roles if needed)
        // For now, general customers or unassociated roles might not see specific owner products unless logic is added.
        // This path currently would result in an empty list if no products are "general" (no ownerId).
        else {
             displayProducts = productsFromStorage.filter(p => p.isOnline && !p.ownerId); // Or some other logic
        }


        const productsWithDetails = displayProducts.map((p, index) => ({
            ...p,
            rating: 4.0 + (index % 10) / 10, 
            reviews: 50 + (index % 10) * 10,
            isFavorite: index % 3 === 0, 
        }));
        setAllProducts(productsWithDetails);
    }
    setIsLoading(false);
  }, [authUser]);

  const handleQuantityChange = (productId: string, quantityValue: string) => {
    const newQuantity = parseInt(quantityValue, 10);
    const product = allProducts.find(p => p.id === productId);

    if (isNaN(newQuantity) || newQuantity < 1) {
      setProductQuantities(prev => ({ ...prev, [productId]: 1 }));
      return;
    }

    if (product && product.stock !== undefined && newQuantity > product.stock) {
      setProductQuantities(prev => ({ ...prev, [productId]: product.stock as number }));
      return;
    }
    setProductQuantities(prev => ({ ...prev, [productId]: newQuantity }));
  };

  const handleAddToCart = (product: ProductWithUIDetails) => {
    if (!authUser) {
      toast({
        title: tCommon('error'),
        description: t('userNotAuthenticatedError'),
        variant: 'destructive',
      });
      return;
    }
    
    const ownerIdForCart = product.ownerId || authUser.createdById;

    if (!ownerIdForCart) {
         toast({
            title: tCommon('error'),
            description: t('cannotDetermineStoreError'), 
            variant: 'destructive',
        });
        return;
    }

    const cartKey = `${CART_STORAGE_KEY_PREFIX}${ownerIdForCart}`;
    const quantityToAdd = productQuantities[product.id] || 1;

    if (product.stock !== undefined && quantityToAdd > product.stock) {
        toast({
            title: tCommon('error'),
            description: t('notEnoughStockError', { productName: product.name, availableStock: product.stock.toString() }),
            variant: 'destructive',
        });
        return;
    }


    try {
      let cart: CartItem[] = JSON.parse(localStorage.getItem(cartKey) || '[]');
      const existingItemIndex = cart.findIndex(item => item.id === product.id);

      if (existingItemIndex > -1) {
        cart[existingItemIndex].quantity += quantityToAdd;
      } else {
        cart.push({
          id: product.id,
          name: product.name,
          price: product.price,
          quantity: quantityToAdd,
          imageUrl: product.imageUrl,
          ownerId: product.ownerId, 
          dataAiHint: product.dataAiHint
        });
      }

      localStorage.setItem(cartKey, JSON.stringify(cart));
      toast({
        title: tCommon('success'),
        description: t('addedToCartToast', { productName: product.name, quantity: quantityToAdd.toString() }),
      });
      // Reset quantity for this product to 1 after adding to cart
      setProductQuantities(prev => ({ ...prev, [product.id]: 1 }));
    } catch (error) {
      console.error("Error adding to cart:", error);
      toast({
        title: tCommon('error'),
        description: t('addToCartErrorToast'),
        variant: 'destructive',
      });
    }
  };


  if (isLoading && typeof window !== 'undefined' && !localStorage.getItem('marketSyncMockProducts')) {
    return (
      <AuthenticatedLayout expectedRole={["customer", "employee"]}>
        <div className="flex flex-col justify-center items-center h-64 p-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">{t('loadingProducts')}</p>
        </div>
      </AuthenticatedLayout>
    );
  }
  
  return (
    <AuthenticatedLayout expectedRole={["customer", "employee"]}>
      <div className="space-y-6 p-1">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <ShoppingCart className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
              <p className="text-muted-foreground">
                {t('description')}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse w-full md:w-auto">
            <div className="relative flex-grow md:flex-grow-0 md:w-64">
              <Search className="absolute left-2.5 rtl:right-2.5 rtl:left-auto top-2.5 h-4 w-4 text-muted-foreground" />
              <Input type="search" placeholder={t('searchPlaceholder')} className="pl-8 rtl:pr-8 rtl:pl-3 w-full" />
            </div>
            <Button variant="outline">
              <Filter className="mr-2 rtl:ml-2 rtl:mr-0 h-4 w-4" /> {t('filtersButton')}
            </Button>
          </div>
        </div>
        
        <Card className="bg-secondary/50">
            <CardContent className="p-4">
                <p className="text-center text-sm text-muted-foreground">
                  {t('specialOffer')}
                </p>
            </CardContent>
        </Card>

        {allProducts.length === 0 && !isLoading ? ( 
          <Card>
            <CardContent className="p-6 text-center text-muted-foreground">
              <ShoppingCart className="mx-auto h-12 w-12 mb-4" />
              <p className="text-lg font-semibold">{t('storeUpdatingTitle')}</p>
              <p>{t('noProductsAvailable')}</p>
            </CardContent>
          </Card>
        ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {allProducts.map(product => (
            <Card key={product.id} className="shadow-md hover:shadow-lg transition-shadow flex flex-col overflow-hidden group">
              <div className="relative w-full h-56">
                <Image 
                  src={product.imageUrl || `https://picsum.photos/300/200`}
                  alt={product.name} 
                  fill
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                  data-ai-hint={product.dataAiHint}
                />
                <Button 
                    variant="ghost" 
                    size="icon" 
                    className={`absolute top-2 right-2 rtl:left-2 rtl:right-auto bg-background/70 hover:bg-background ${product.isFavorite ? 'text-red-500 hover:text-red-600' : 'text-muted-foreground hover:text-red-500'}`}
                    aria-label={t('toggleFavorite')}
                    // onClick={() => {/* Implement favorite toggle, updating allProducts and localStorage */}}
                >
                    <Heart className={`h-5 w-5 ${product.isFavorite ? 'fill-current' : ''}`} />
                </Button>
                {(product.rating || 0) > 4.5 && <Badge className="absolute top-2 left-2 rtl:right-2 rtl:left-auto bg-accent text-accent-foreground">{t('bestSellerBadge')}</Badge>}
              </div>
              <CardHeader className="pb-2 pt-4">
                <Link href={`/customer/product/${product.id}`} className="hover:underline">
                    <CardTitle className="text-lg truncate" title={product.name}>{product.name}</CardTitle>
                </Link>
                <CardDescription>{product.category}</CardDescription>
              </CardHeader>
              <CardContent className="flex-grow flex flex-col justify-between">
                <div>
                    <div className="flex items-center space-x-1 rtl:space-x-reverse text-sm text-muted-foreground mb-2">
                        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                        <span>{(product.rating || 0).toFixed(1)}</span>
                        <span>({t('reviewsCount', {count: product.reviews || 0})})</span>
                    </div>
                    <p className="text-xl font-semibold text-primary mb-3">YER {product.price.toFixed(2)}</p>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse mb-3">
                  <Label htmlFor={`quantity-${product.id}`} className="text-sm shrink-0">{t('quantityLabelShort') || 'Qty'}:</Label>
                  <Input
                    id={`quantity-${product.id}`}
                    type="number"
                    min="1"
                    max={product.stock !== undefined && product.stock > 0 ? product.stock : undefined}
                    value={productQuantities[product.id] || 1}
                    onChange={(e) => handleQuantityChange(product.id, e.target.value)}
                    className="w-20 h-9 text-center appearance-none [-moz-appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                    disabled={product.stock === undefined || product.stock === 0}
                  />
                </div>
                <Button 
                    className="w-full mt-auto bg-primary hover:bg-primary/90 text-primary-foreground" 
                    onClick={() => handleAddToCart(product)}
                    disabled={product.stock === undefined || product.stock === 0}
                >
                  <ShoppingCart className="mr-2 rtl:ml-2 rtl:mr-0 h-4 w-4" /> {product.stock !== undefined && product.stock > 0 ? t('addToCartButton') : t('outOfStockButton')}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
        )}
         {allProducts.length > 0 && ( 
            <div className="flex justify-center mt-8">
                <Button variant="outline">{t('loadMoreButton')}</Button>
            </div>
         )}
      </div>
    </AuthenticatedLayout>
  );
}

