
"use client";

import { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Undo2, Plus, AlertCircle } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { ReturnsList } from '@/components/returns/returns-list';
import { ReturnDetails } from '@/components/returns/return-details';
import { ProcessReturnForm } from '@/components/returns/process-return-form';
import { ReturnsStats } from '@/components/returns/returns-stats';
import { NewReturnForm } from '@/components/returns/new-return-form';
import { getMockReturns, updateMockReturn } from '@/lib/mock-returns-data';
import type { Return } from '@/types';

type ViewMode = 'list' | 'details' | 'process' | 'new';

export default function EmployeeReturnsManagementPage() {
  const t = useScopedI18n('common');
  const tSpecific = useScopedI18n('employeeReturnsManagement');
  const { user } = useAuth();
  const { toast } = useToast();

  const [returns, setReturns] = useState<Return[]>([]);
  const [selectedReturnId, setSelectedReturnId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [activeTab, setActiveTab] = useState('pending');
  const [isLoading, setIsLoading] = useState(true);

  // Check permissions
  const hasPermission = user?.role === 'employee' && user?.permissions?.canProcessSalesReturns;

  useEffect(() => {
    loadReturns();
  }, []);

  const loadReturns = async () => {
    try {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      const mockReturns = getMockReturns();
      setReturns(mockReturns);
    } catch (error) {
      toast({
        title: 'خطأ في تحميل البيانات',
        description: 'فشل في تحميل بيانات المرتجعات',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewReturn = (returnId: string) => {
    setSelectedReturnId(returnId);
    setViewMode('details');
  };

  const handleProcessReturn = (returnId: string) => {
    setSelectedReturnId(returnId);
    setViewMode('process');
  };

  const handleBackToList = () => {
    setSelectedReturnId(null);
    setViewMode('list');
  };

  const handleProcessSuccess = (action: 'approve' | 'reject' | 'process', updatedReturn: Return) => {
    // Update the return in mock data
    updateMockReturn(updatedReturn.id, updatedReturn);

    // Update local state
    setReturns(prev => prev.map(r => r.id === updatedReturn.id ? updatedReturn : r));

    // Go back to list
    handleBackToList();
  };

  const handleNewReturnSuccess = (newReturn: Return) => {
    // Add to local state
    setReturns(prev => [newReturn, ...prev]);

    // Go back to list
    handleBackToList();
  };

  const selectedReturn = selectedReturnId ? returns.find(r => r.id === selectedReturnId) : null;

  const filteredReturns = returns.filter(returnItem => {
    if (activeTab === 'pending') return returnItem.status === 'pending';
    if (activeTab === 'processed') return ['processed', 'refunded', 'approved'].includes(returnItem.status);
    return true; // 'all' tab
  });

  if (!hasPermission) {
    return (
      <AuthenticatedLayout expectedRole="employee">
        <div className="space-y-6 p-1">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <div>
              <h1 className="text-3xl font-bold text-foreground">{tSpecific('permissionDenied')}</h1>
              <p className="text-muted-foreground">
                {tSpecific('permissionDeniedMessage')}
              </p>
            </div>
          </div>
        </div>
      </AuthenticatedLayout>
    );
  }

  if (isLoading) {
    return (
      <AuthenticatedLayout expectedRole="employee">
        <div className="space-y-6 p-1">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">جارٍ تحميل المرتجعات...</p>
            </div>
          </div>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-6 p-1">
        {viewMode === 'list' && (
          <>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Undo2 className="h-8 w-8 text-primary" />
                <div>
                  <h1 className="text-3xl font-bold text-foreground">{tSpecific('title')}</h1>
                  <p className="text-muted-foreground">
                    {tSpecific('description')}
                  </p>
                </div>
              </div>
              <Button onClick={() => setViewMode('new')} className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                {tSpecific('newReturn')}
              </Button>
            </div>

            {/* Statistics */}
            <ReturnsStats returns={returns} />

            {/* Returns Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="pending" className="flex items-center gap-2">
                  {tSpecific('pendingReturns')}
                  <span className="bg-destructive text-destructive-foreground rounded-full px-2 py-0.5 text-xs">
                    {returns.filter(r => r.status === 'pending').length}
                  </span>
                </TabsTrigger>
                <TabsTrigger value="processed">{tSpecific('processedCount')}</TabsTrigger>
                <TabsTrigger value="all">{tSpecific('allReturns')}</TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-6">
                <ReturnsList
                  returns={filteredReturns}
                  onViewReturn={handleViewReturn}
                  onProcessReturn={handleProcessReturn}
                  showActions={true}
                />
              </TabsContent>
            </Tabs>
          </>
        )}

        {viewMode === 'details' && selectedReturn && (
          <ReturnDetails
            returnData={selectedReturn}
            onBack={handleBackToList}
            onProcess={() => setViewMode('process')}
            showProcessButton={selectedReturn.status === 'pending'}
          />
        )}

        {viewMode === 'process' && selectedReturn && (
          <ProcessReturnForm
            returnData={selectedReturn}
            onCancel={handleBackToList}
            onSuccess={handleProcessSuccess}
          />
        )}

        {viewMode === 'new' && (
          <NewReturnForm
            onCancel={handleBackToList}
            onSuccess={handleNewReturnSuccess}
          />
        )}
      </div>
    </AuthenticatedLayout>
  );
}

