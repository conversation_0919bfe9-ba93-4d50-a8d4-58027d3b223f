// src/lib/mock-returns-data.ts
import type { Return, ReturnPolicy } from '@/types';

const MOCK_RETURNS_KEY = 'marketSyncMockReturns';
const MOCK_RETURN_POLICIES_KEY = 'marketSyncMockReturnPolicies';

// Mock return policies
const mockReturnPolicies: ReturnPolicy[] = [
  {
    id: 'policy001',
    ownerId: 'owner001',
    maxReturnDays: 30,
    allowedReasons: ['defective', 'wrong_item', 'not_satisfied', 'damaged_shipping', 'expired'],
    requiresApproval: true,
    refundMethods: ['cash', 'credit', 'exchange', 'store_credit'],
    restockingFee: 5, // 5%
    conditions: {
      defective: { allowedDays: 30, requiresProof: true },
      wrongItem: { allowedDays: 15, requiresProof: false },
      notSatisfied: { allowedDays: 7, requiresProof: false },
      damagedShipping: { allowedDays: 3, requiresProof: true },
      expired: { allowedDays: 1, requiresProof: true }
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  }
];

// Mock returns data
const mockReturns: Return[] = [
  {
    id: 'RET001',
    orderId: 'ORD12345',
    customerId: 'customer002',
    customerName: 'علي (عميل الوفاء)',
    customerPhone: '770000005',
    items: [
      {
        id: 'ret_item_001',
        productId: 'prod001',
        productName: 'حليب طازج - 1 لتر',
        originalQuantity: 2,
        returnQuantity: 1,
        originalPrice: 800,
        returnAmount: 800,
        condition: 'defective',
        reason: 'منتج منتهي الصلاحية'
      }
    ],
    totalReturnAmount: 800,
    returnReason: 'expired',
    returnReasonDetails: 'تم اكتشاف أن المنتج منتهي الصلاحية عند الوصول للمنزل',
    status: 'pending',
    refundMethod: 'cash',
    createdAt: '2024-01-20T10:30:00Z',
    notes: 'العميل يطلب استرداد نقدي',
    attachments: []
  },
  {
    id: 'RET002',
    orderId: 'ORD12346',
    customerId: 'customer001',
    customerName: 'عميل عام',
    customerPhone: '770000004',
    items: [
      {
        id: 'ret_item_002',
        productId: 'prod002',
        productName: 'خبز أبيض طازج',
        originalQuantity: 3,
        returnQuantity: 2,
        originalPrice: 200,
        returnAmount: 400,
        condition: 'damaged',
        reason: 'تلف أثناء النقل'
      }
    ],
    totalReturnAmount: 400,
    returnReason: 'damaged_shipping',
    returnReasonDetails: 'الخبز وصل مكسور ومتضرر',
    status: 'approved',
    refundMethod: 'exchange',
    exchangeItems: [
      {
        productId: 'prod002',
        productName: 'خبز أبيض طازج',
        quantity: 2,
        price: 200
      }
    ],
    createdAt: '2024-01-19T14:15:00Z',
    processedAt: '2024-01-19T16:30:00Z',
    processedBy: 'employee001',
    processedByName: 'أحمد (موظف الوفاء)',
    approvedBy: 'owner001',
    approvedByName: 'مالك سوبر ماركت الوفاء',
    notes: 'تم الموافقة على الاستبدال',
    attachments: []
  },
  {
    id: 'RET003',
    orderId: 'ORD12347',
    customerId: 'customer002',
    customerName: 'علي (عميل الوفاء)',
    customerPhone: '770000005',
    items: [
      {
        id: 'ret_item_003',
        productId: 'prod003',
        productName: 'عصير برتقال طبيعي',
        originalQuantity: 1,
        returnQuantity: 1,
        originalPrice: 1500,
        returnAmount: 1500,
        condition: 'good',
        reason: 'لم يعجب العميل'
      }
    ],
    totalReturnAmount: 1500,
    returnReason: 'not_satisfied',
    returnReasonDetails: 'طعم العصير لم يعجب العائلة',
    status: 'processed',
    refundMethod: 'store_credit',
    refundAmount: 1425, // After 5% restocking fee
    createdAt: '2024-01-18T09:45:00Z',
    processedAt: '2024-01-18T11:20:00Z',
    processedBy: 'employee001',
    processedByName: 'أحمد (موظف الوفاء)',
    approvedBy: 'owner001',
    approvedByName: 'مالك سوبر ماركت الوفاء',
    notes: 'تم خصم رسوم إعادة التخزين 5%',
    attachments: []
  },
  {
    id: 'RET004',
    orderId: 'ORD12348',
    customerId: 'customer001',
    customerName: 'عميل عام',
    customerPhone: '770000004',
    items: [
      {
        id: 'ret_item_004',
        productId: 'prod004',
        productName: 'شامبو للشعر الجاف',
        originalQuantity: 1,
        returnQuantity: 1,
        originalPrice: 2500,
        returnAmount: 2500,
        condition: 'excellent',
        reason: 'طلب منتج خاطئ'
      }
    ],
    totalReturnAmount: 2500,
    returnReason: 'wrong_item',
    returnReasonDetails: 'طلب شامبو للشعر الدهني بدلاً من الجاف',
    status: 'refunded',
    refundMethod: 'cash',
    refundAmount: 2500,
    createdAt: '2024-01-17T13:20:00Z',
    processedAt: '2024-01-17T15:45:00Z',
    processedBy: 'employee001',
    processedByName: 'أحمد (موظف الوفاء)',
    approvedBy: 'owner001',
    approvedByName: 'مالك سوبر ماركت الوفاء',
    notes: 'استرداد كامل - منتج في حالة ممتازة',
    attachments: []
  },
  {
    id: 'RET005',
    orderId: 'ORD12349',
    customerId: 'customer002',
    customerName: 'علي (عميل الوفاء)',
    customerPhone: '770000005',
    items: [
      {
        id: 'ret_item_005',
        productId: 'prod005',
        productName: 'جهاز كهربائي صغير',
        originalQuantity: 1,
        returnQuantity: 1,
        originalPrice: 15000,
        returnAmount: 15000,
        condition: 'defective',
        reason: 'عطل في الجهاز'
      }
    ],
    totalReturnAmount: 15000,
    returnReason: 'defective',
    returnReasonDetails: 'الجهاز لا يعمل منذ اليوم الأول',
    status: 'rejected',
    refundMethod: 'cash',
    createdAt: '2024-01-16T16:10:00Z',
    processedAt: '2024-01-16T17:30:00Z',
    processedBy: 'employee001',
    processedByName: 'أحمد (موظف الوفاء)',
    notes: 'تم رفض المرتجع - تجاوز فترة الإرجاع المسموحة',
    attachments: []
  }
];

// Functions to manage mock returns data
export function getMockReturns(): Return[] {
  if (typeof window === 'undefined') return mockReturns;
  
  const stored = localStorage.getItem(MOCK_RETURNS_KEY);
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch {
      return mockReturns;
    }
  }
  
  localStorage.setItem(MOCK_RETURNS_KEY, JSON.stringify(mockReturns));
  return mockReturns;
}

export function getMockReturnPolicies(): ReturnPolicy[] {
  if (typeof window === 'undefined') return mockReturnPolicies;
  
  const stored = localStorage.getItem(MOCK_RETURN_POLICIES_KEY);
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch {
      return mockReturnPolicies;
    }
  }
  
  localStorage.setItem(MOCK_RETURN_POLICIES_KEY, JSON.stringify(mockReturnPolicies));
  return mockReturnPolicies;
}

export function addMockReturn(returnData: Return): void {
  if (typeof window === 'undefined') return;
  
  const returns = getMockReturns();
  returns.unshift(returnData);
  localStorage.setItem(MOCK_RETURNS_KEY, JSON.stringify(returns));
}

export function updateMockReturn(returnId: string, updates: Partial<Return>): void {
  if (typeof window === 'undefined') return;
  
  const returns = getMockReturns();
  const index = returns.findIndex(r => r.id === returnId);
  if (index !== -1) {
    returns[index] = { ...returns[index], ...updates };
    localStorage.setItem(MOCK_RETURNS_KEY, JSON.stringify(returns));
  }
}

export function getReturnById(returnId: string): Return | undefined {
  const returns = getMockReturns();
  return returns.find(r => r.id === returnId);
}

export function getReturnsByCustomer(customerId: string): Return[] {
  const returns = getMockReturns();
  return returns.filter(r => r.customerId === customerId);
}

export function getReturnsByStatus(status: Return['status']): Return[] {
  const returns = getMockReturns();
  return returns.filter(r => r.status === status);
}

export function generateReturnId(): string {
  const returns = getMockReturns();
  const maxId = returns.reduce((max, r) => {
    const num = parseInt(r.id.replace('RET', ''));
    return num > max ? num : max;
  }, 0);
  return `RET${String(maxId + 1).padStart(3, '0')}`;
}
