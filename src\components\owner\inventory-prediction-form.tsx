
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { predictDemandServerAction } from "@/app/actions/ai-actions";
import { useState } from "react";
import type { InventoryPrediction, PredictDemandInput } from "@/types";
import { Loader2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useScopedI18n, useI18n } from '@/lib/i18n/client';

export function InventoryPredictionForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [predictionResult, setPredictionResult] = useState<InventoryPrediction | null>(null);
  const { toast } = useToast();
  const t = useScopedI18n('inventoryPredictionForm');
  const tCommon = useScopedI18n('common');
  const tForm = useScopedI18n('form');

  const predictionFormSchema = z.object({
    productCategory: z.string().min(1, tForm('fieldRequired', {field: t('productCategoryLabel')})),
    historicalSalesData: z.string().min(1, tForm('fieldRequired', {field: t('historicalSalesDataLabel')}))
      .refine(data => {
        try {
          JSON.parse(data);
          return true;
        } catch {
          return false;
        }
      }, tForm('fieldInvalid', {field: t('historicalSalesDataLabel')})),
    currentStockLevel: z.coerce.number().min(0, tForm('fieldInvalid', {field: t('currentStockLevelLabel')})),
    leadTimeDays: z.coerce.number().min(1, tForm('fieldInvalid', {field: t('leadTimeDaysLabel')})),
    seasonalTrends: z.string().optional().refine(data => {
      if (!data) return true;
      try {
        JSON.parse(data);
        return true;
      } catch {
        return false;
      }
    }, tForm('fieldInvalid', {field: t('seasonalTrendsLabel')})),
  });

  type PredictionFormValues = z.infer<typeof predictionFormSchema>;

  const form = useForm<PredictionFormValues>({
    resolver: zodResolver(predictionFormSchema),
    defaultValues: {
      productCategory: "",
      historicalSalesData: JSON.stringify([{ date: "2023-01-01", quantitySold: 10, price: 5.99 }], null, 2),
      currentStockLevel: 100,
      leadTimeDays: 7,
      seasonalTrends: JSON.stringify([{ period: "Christmas", impact: "high sales" }], null, 2),
    },
  });

  async function onSubmit(values: PredictionFormValues) {
    setIsLoading(true);
    setPredictionResult(null);
    try {
      const input: PredictDemandInput = {
        ...values,
        seasonalTrends: values.seasonalTrends || undefined,
      };
      const result = await predictDemandServerAction(input);
      if(result) {
        setPredictionResult(result);
        toast({
          title: t('toastPredictionGeneratedTitle'),
          description: t('toastPredictionGeneratedDesc', { productCategory: result.productCategory }),
        });
      } else {
        toast({
          title: t('toastPredictionErrorTitle'),
          description: t('toastPredictionErrorDesc'),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Prediction error:", error);
      toast({
        title: t('toastPredictionErrorTitle'),
        description: (error as Error).message || t('toastUnexpectedError'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="productCategory"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('productCategoryLabel')}</FormLabel>
                <FormControl>
                  <Input placeholder={t('productCategoryPlaceholder')} {...field} />
                </FormControl>
                <FormDescription>{t('productCategoryDescription')}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="historicalSalesData"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('historicalSalesDataLabel')}</FormLabel>
                <FormControl>
                  <Textarea rows={5} placeholder={t('historicalSalesDataPlaceholder')} {...field} />
                </FormControl>
                <FormDescription>{t('historicalSalesDataDescription')}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <FormField
              control={form.control}
              name="currentStockLevel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('currentStockLevelLabel')}</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder={t('currentStockLevelPlaceholder')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="leadTimeDays"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('leadTimeDaysLabel')}</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder={t('leadTimeDaysPlaceholder')} {...field} />
                  </FormControl>
                  <FormDescription>{t('leadTimeDaysDescription')}</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="seasonalTrends"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('seasonalTrendsLabel')}</FormLabel>
                <FormControl>
                  <Textarea rows={3} placeholder={t('seasonalTrendsPlaceholder')} {...field} />
                </FormControl>
                <FormDescription>{t('seasonalTrendsDescription')}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" disabled={isLoading} className="bg-accent hover:bg-accent/90 text-accent-foreground">
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin rtl:ml-2 rtl:mr-0" />}
            {t('getPredictionButton')}
          </Button>
        </form>
      </Form>

      {predictionResult && (
        <Alert className="mt-8 shadow-md">
          <Terminal className="h-4 w-4" />
          <AlertTitle className="font-semibold text-lg">{t('predictionResultTitle', { productCategory: predictionResult.productCategory })}</AlertTitle>
          <AlertDescription className="mt-2 space-y-2">
            <p><strong>{t('predictedDemandLabel')}:</strong> {predictionResult.predictedDemand.toLocaleString()} {t('unitsLabel')}</p>
            <p><strong>{t('recommendedOrderQuantityLabel')}:</strong> {predictionResult.recommendedOrderQuantity.toLocaleString()} {t('unitsLabel')}</p>
            <p><strong>{t('confidenceLevelLabel')}:</strong> {(predictionResult.confidenceLevel * 100).toFixed(1)}%</p>
            <p><strong>{t('explanationLabel')}:</strong> {predictionResult.explanation}</p>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
