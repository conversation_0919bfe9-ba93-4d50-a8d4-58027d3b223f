"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { UserManagementTable } from '@/components/admin/user-management-table';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Users } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';

export default function UserManagementPage() {
  const t = useScopedI18n('userManagement');

  return (
    <AuthenticatedLayout expectedRole="admin">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <Users className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{t('allUsers')}</CardTitle>
            <CardDescription>
              {t('allUsersDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <UserManagementTable />
          </CardContent>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}
