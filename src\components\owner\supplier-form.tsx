'use client';

import { useState } from 'react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  User, 
  Building, 
  Phone, 
  Mail, 
  MapPin, 
  CreditCard, 
  Star,
  Plus,
  X
} from 'lucide-react';
import { Supplier } from '@/types';
import { addSupplier } from '@/lib/mock-supplier-data';

interface SupplierFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (supplier: Supplier) => void;
  editingSupplier?: Supplier | null;
}

export function SupplierForm({ isOpen, onClose, onSuccess, editingSupplier }: SupplierFormProps) {
  const t = useScopedI18n('ownerSupplierManagement');
  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [products, setProducts] = useState<string[]>([]);
  const [newProduct, setNewProduct] = useState('');
  const [certifications, setCertifications] = useState<string[]>([]);
  const [newCertification, setNewCertification] = useState('');

  const [formData, setFormData] = useState({
    name: editingSupplier?.name || '',
    companyName: editingSupplier?.companyName || '',
    contactPerson: editingSupplier?.contactPerson || '',
    email: editingSupplier?.email || '',
    phone: editingSupplier?.phone || '',
    address: editingSupplier?.address || '',
    city: editingSupplier?.city || '',
    country: editingSupplier?.country || 'اليمن',
    taxId: editingSupplier?.taxId || '',
    businessLicense: editingSupplier?.businessLicense || '',
    category: editingSupplier?.category || '',
    paymentTerms: editingSupplier?.paymentTerms || 'Net 30',
    creditLimit: editingSupplier?.creditLimit || 0,
    minimumOrderAmount: editingSupplier?.minimumOrderAmount || 0,
    leadTime: editingSupplier?.leadTime || 7,
    returnPolicy: editingSupplier?.returnPolicy || '',
    warrantyTerms: editingSupplier?.warrantyTerms || '',
    notes: editingSupplier?.notes || '',
    // Bank details
    bankName: editingSupplier?.bankDetails?.bankName || '',
    accountNumber: editingSupplier?.bankDetails?.accountNumber || '',
    routingNumber: editingSupplier?.bankDetails?.routingNumber || '',
    swiftCode: editingSupplier?.bankDetails?.swiftCode || ''
  });

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addProduct = () => {
    if (newProduct.trim() && !products.includes(newProduct.trim())) {
      setProducts([...products, newProduct.trim()]);
      setNewProduct('');
    }
  };

  const removeProduct = (product: string) => {
    setProducts(products.filter(p => p !== product));
  };

  const addCertification = () => {
    if (newCertification.trim() && !certifications.includes(newCertification.trim())) {
      setCertifications([...certifications, newCertification.trim()]);
      setNewCertification('');
    }
  };

  const removeCertification = (certification: string) => {
    setCertifications(certifications.filter(c => c !== certification));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.contactPerson || !formData.phone) {
      toast({
        title: 'خطأ في البيانات',
        description: 'يرجى ملء جميع الحقول المطلوبة',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    try {
      const supplierData: Omit<Supplier, 'id'> = {
        name: formData.name,
        companyName: formData.companyName,
        contactPerson: formData.contactPerson,
        email: formData.email,
        phone: formData.phone,
        address: formData.address,
        city: formData.city,
        country: formData.country,
        taxId: formData.taxId,
        businessLicense: formData.businessLicense,
        category: formData.category,
        products: products,
        paymentTerms: formData.paymentTerms,
        creditLimit: Number(formData.creditLimit),
        currentBalance: 0,
        rating: 0,
        status: 'pending_approval',
        dateAdded: new Date().toISOString().split('T')[0],
        totalOrders: 0,
        totalPurchaseAmount: 0,
        averageDeliveryTime: Number(formData.leadTime),
        qualityRating: 0,
        reliabilityRating: 0,
        priceCompetitiveness: 0,
        notes: formData.notes,
        ownerId: 'owner001', // This should come from auth context
        bankDetails: {
          bankName: formData.bankName,
          accountNumber: formData.accountNumber,
          routingNumber: formData.routingNumber,
          swiftCode: formData.swiftCode
        },
        certifications: certifications,
        minimumOrderAmount: Number(formData.minimumOrderAmount),
        leadTime: Number(formData.leadTime),
        returnPolicy: formData.returnPolicy,
        warrantyTerms: formData.warrantyTerms
      };

      const newSupplier = await addSupplier(supplierData);
      
      toast({
        title: t('supplierAdded'),
        description: `تم إضافة المورد ${newSupplier.name} بنجاح`
      });

      onSuccess(newSupplier);
      onClose();
      resetForm();
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في إضافة المورد',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      companyName: '',
      contactPerson: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      country: 'اليمن',
      taxId: '',
      businessLicense: '',
      category: '',
      paymentTerms: 'Net 30',
      creditLimit: 0,
      minimumOrderAmount: 0,
      leadTime: 7,
      returnPolicy: '',
      warrantyTerms: '',
      notes: '',
      bankName: '',
      accountNumber: '',
      routingNumber: '',
      swiftCode: ''
    });
    setProducts([]);
    setCertifications([]);
    setActiveTab('basic');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Building className="h-5 w-5" />
            <span>{editingSupplier ? 'تعديل المورد' : t('addSupplier')}</span>
          </DialogTitle>
          <DialogDescription>
            {editingSupplier ? 'تعديل معلومات المورد' : 'إضافة مورد جديد إلى قاعدة البيانات'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">المعلومات الأساسية</TabsTrigger>
              <TabsTrigger value="contact">معلومات الاتصال</TabsTrigger>
              <TabsTrigger value="business">معلومات العمل</TabsTrigger>
              <TabsTrigger value="financial">المعلومات المالية</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span>المعلومات الأساسية</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">{t('supplierName')} *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="اسم المورد"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="companyName">{t('companyName')}</Label>
                      <Input
                        id="companyName"
                        value={formData.companyName}
                        onChange={(e) => handleInputChange('companyName', e.target.value)}
                        placeholder="اسم الشركة"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="contactPerson">{t('contactPerson')} *</Label>
                      <Input
                        id="contactPerson"
                        value={formData.contactPerson}
                        onChange={(e) => handleInputChange('contactPerson', e.target.value)}
                        placeholder="الشخص المسؤول"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category">{t('category')}</Label>
                      <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر الفئة" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="grains">حبوب وبقوليات</SelectItem>
                          <SelectItem value="dairy">منتجات ألبان</SelectItem>
                          <SelectItem value="produce">خضار وفواكه</SelectItem>
                          <SelectItem value="canned_goods">معلبات</SelectItem>
                          <SelectItem value="cleaning">منتجات تنظيف</SelectItem>
                          <SelectItem value="beverages">مشروبات</SelectItem>
                          <SelectItem value="snacks">وجبات خفيفة</SelectItem>
                          <SelectItem value="frozen">مجمدات</SelectItem>
                          <SelectItem value="other">أخرى</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>المنتجات المتوفرة</Label>
                    <div className="flex space-x-2">
                      <Input
                        value={newProduct}
                        onChange={(e) => setNewProduct(e.target.value)}
                        placeholder="أضف منتج"
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addProduct())}
                      />
                      <Button type="button" onClick={addProduct} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {products.map((product, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                          <span>{product}</span>
                          <X 
                            className="h-3 w-3 cursor-pointer" 
                            onClick={() => removeProduct(product)}
                          />
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="contact" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Phone className="h-4 w-4" />
                    <span>معلومات الاتصال</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">{t('phone')} *</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="+967-1-234567"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">{t('email')}</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address">{t('address')}</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder="العنوان الكامل"
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city">{t('city')}</Label>
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        placeholder="المدينة"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="country">{t('country')}</Label>
                      <Input
                        id="country"
                        value={formData.country}
                        onChange={(e) => handleInputChange('country', e.target.value)}
                        placeholder="البلد"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="business" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Building className="h-4 w-4" />
                    <span>معلومات العمل</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="taxId">{t('taxId')}</Label>
                      <Input
                        id="taxId"
                        value={formData.taxId}
                        onChange={(e) => handleInputChange('taxId', e.target.value)}
                        placeholder="الرقم الضريبي"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="businessLicense">{t('businessLicense')}</Label>
                      <Input
                        id="businessLicense"
                        value={formData.businessLicense}
                        onChange={(e) => handleInputChange('businessLicense', e.target.value)}
                        placeholder="رقم رخصة العمل"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="leadTime">{t('leadTime')} ({t('days')})</Label>
                      <Input
                        id="leadTime"
                        type="number"
                        value={formData.leadTime}
                        onChange={(e) => handleInputChange('leadTime', Number(e.target.value))}
                        placeholder="7"
                        min="1"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="minimumOrderAmount">{t('minimumOrderAmount')}</Label>
                      <Input
                        id="minimumOrderAmount"
                        type="number"
                        value={formData.minimumOrderAmount}
                        onChange={(e) => handleInputChange('minimumOrderAmount', Number(e.target.value))}
                        placeholder="0"
                        min="0"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>الشهادات والتراخيص</Label>
                    <div className="flex space-x-2">
                      <Input
                        value={newCertification}
                        onChange={(e) => setNewCertification(e.target.value)}
                        placeholder="أضف شهادة"
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCertification())}
                      />
                      <Button type="button" onClick={addCertification} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {certifications.map((cert, index) => (
                        <Badge key={index} variant="outline" className="flex items-center space-x-1">
                          <span>{cert}</span>
                          <X 
                            className="h-3 w-3 cursor-pointer" 
                            onClick={() => removeCertification(cert)}
                          />
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="returnPolicy">{t('returnPolicy')}</Label>
                    <Textarea
                      id="returnPolicy"
                      value={formData.returnPolicy}
                      onChange={(e) => handleInputChange('returnPolicy', e.target.value)}
                      placeholder="سياسة الإرجاع والاستبدال"
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="warrantyTerms">{t('warrantyTerms')}</Label>
                    <Textarea
                      id="warrantyTerms"
                      value={formData.warrantyTerms}
                      onChange={(e) => handleInputChange('warrantyTerms', e.target.value)}
                      placeholder="شروط الضمان"
                      rows={2}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="financial" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CreditCard className="h-4 w-4" />
                    <span>المعلومات المالية</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="paymentTerms">{t('paymentTerms')}</Label>
                      <Select value={formData.paymentTerms} onValueChange={(value) => handleInputChange('paymentTerms', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Cash on Delivery">الدفع عند التسليم</SelectItem>
                          <SelectItem value="Net 15">صافي 15 يوم</SelectItem>
                          <SelectItem value="Net 30">صافي 30 يوم</SelectItem>
                          <SelectItem value="Net 45">صافي 45 يوم</SelectItem>
                          <SelectItem value="Net 60">صافي 60 يوم</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="creditLimit">{t('creditLimit')}</Label>
                      <Input
                        id="creditLimit"
                        type="number"
                        value={formData.creditLimit}
                        onChange={(e) => handleInputChange('creditLimit', Number(e.target.value))}
                        placeholder="0"
                        min="0"
                      />
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">{t('bankDetails')}</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="bankName">{t('bankName')}</Label>
                        <Input
                          id="bankName"
                          value={formData.bankName}
                          onChange={(e) => handleInputChange('bankName', e.target.value)}
                          placeholder="اسم البنك"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="accountNumber">{t('accountNumber')}</Label>
                        <Input
                          id="accountNumber"
                          value={formData.accountNumber}
                          onChange={(e) => handleInputChange('accountNumber', e.target.value)}
                          placeholder="رقم الحساب"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="routingNumber">{t('routingNumber')}</Label>
                        <Input
                          id="routingNumber"
                          value={formData.routingNumber}
                          onChange={(e) => handleInputChange('routingNumber', e.target.value)}
                          placeholder="رقم التوجيه"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="swiftCode">{t('swiftCode')}</Label>
                        <Input
                          id="swiftCode"
                          value={formData.swiftCode}
                          onChange={(e) => handleInputChange('swiftCode', e.target.value)}
                          placeholder="رمز SWIFT"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">{t('notes')}</Label>
                    <Textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      placeholder="ملاحظات إضافية"
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              {t('cancel')}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'جارٍ الحفظ...' : t('save')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
