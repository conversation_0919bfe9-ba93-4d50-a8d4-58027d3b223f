import { BiometricCredential, BiometricAuthRequest, BiometricAuthResponse, BiometricRegistrationRequest, BiometricRegistrationResponse } from '@/types';

// Check if WebAuthn is supported
export const isBiometricSupported = (): boolean => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false;
  }

  return !!(navigator.credentials && navigator.credentials.create && navigator.credentials.get && window.PublicKeyCredential);
};

// Check if platform authenticator (like Touch ID, Face ID, Windows Hello) is available
export const isPlatformAuthenticatorAvailable = async (): Promise<boolean> => {
  if (!isBiometricSupported()) return false;
  
  try {
    const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
    return available;
  } catch (error) {
    console.error('Error checking platform authenticator availability:', error);
    return false;
  }
};

// Generate a random challenge for authentication
const generateChallenge = (): Uint8Array => {
  const challenge = new Uint8Array(32);
  crypto.getRandomValues(challenge);
  return challenge;
};

// Convert ArrayBuffer to Base64URL
const arrayBufferToBase64URL = (buffer: ArrayBuffer): string => {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
};

// Convert Base64URL to ArrayBuffer
const base64URLToArrayBuffer = (base64url: string): ArrayBuffer => {
  const base64 = base64url.replace(/-/g, '+').replace(/_/g, '/');
  const padded = base64.padEnd(base64.length + (4 - base64.length % 4) % 4, '=');
  const binary = atob(padded);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
};

// Register a new biometric credential
export const registerBiometric = async (userId: string, userName: string, displayName: string): Promise<BiometricRegistrationResponse> => {
  if (!isBiometricSupported()) {
    throw new Error('Biometric authentication is not supported on this device');
  }

  const challenge = generateChallenge();
  
  const publicKeyCredentialCreationOptions: PublicKeyCredentialCreationOptions = {
    challenge,
    rp: {
      name: 'MarketSync',
      id: window.location.hostname,
    },
    user: {
      id: new TextEncoder().encode(userId),
      name: userName,
      displayName: displayName,
    },
    pubKeyCredParams: [
      { alg: -7, type: 'public-key' }, // ES256
      { alg: -257, type: 'public-key' }, // RS256
    ],
    authenticatorSelection: {
      authenticatorAttachment: 'platform',
      userVerification: 'required',
      requireResidentKey: false,
    },
    timeout: 60000,
    attestation: 'direct',
  };

  try {
    const credential = await navigator.credentials.create({
      publicKey: publicKeyCredentialCreationOptions,
    }) as PublicKeyCredential;

    if (!credential) {
      throw new Error('Failed to create credential');
    }

    const response = credential.response as AuthenticatorAttestationResponse;
    
    return {
      credentialId: arrayBufferToBase64URL(credential.rawId),
      publicKey: arrayBufferToBase64URL(response.getPublicKey()!),
      attestationObject: arrayBufferToBase64URL(response.attestationObject),
      clientDataJSON: arrayBufferToBase64URL(response.clientDataJSON),
      deviceName: await getDeviceName(),
    };
  } catch (error) {
    console.error('Biometric registration failed:', error);
    throw new Error('Failed to register biometric authentication');
  }
};

// Authenticate using biometric
export const authenticateBiometric = async (allowCredentials: PublicKeyCredentialDescriptor[]): Promise<BiometricAuthResponse> => {
  if (!isBiometricSupported()) {
    throw new Error('Biometric authentication is not supported on this device');
  }

  const challenge = generateChallenge();

  const publicKeyCredentialRequestOptions: PublicKeyCredentialRequestOptions = {
    challenge,
    allowCredentials: allowCredentials.map(cred => ({
      ...cred,
      id: base64URLToArrayBuffer(cred.id as string),
    })),
    timeout: 60000,
    userVerification: 'required',
  };

  try {
    const credential = await navigator.credentials.get({
      publicKey: publicKeyCredentialRequestOptions,
    }) as PublicKeyCredential;

    if (!credential) {
      throw new Error('Authentication failed');
    }

    const response = credential.response as AuthenticatorAssertionResponse;

    return {
      credentialId: arrayBufferToBase64URL(credential.rawId),
      authenticatorData: arrayBufferToBase64URL(response.authenticatorData),
      signature: arrayBufferToBase64URL(response.signature),
      userHandle: response.userHandle ? arrayBufferToBase64URL(response.userHandle) : undefined,
      clientDataJSON: arrayBufferToBase64URL(response.clientDataJSON),
    };
  } catch (error) {
    console.error('Biometric authentication failed:', error);
    throw new Error('Biometric authentication failed');
  }
};

// Get device name for identification
const getDeviceName = async (): Promise<string> => {
  // Check if we're in a browser environment
  if (typeof navigator === 'undefined') {
    return 'Server Environment';
  }

  const userAgent = navigator.userAgent;

  if (userAgent.includes('iPhone')) return 'iPhone';
  if (userAgent.includes('iPad')) return 'iPad';
  if (userAgent.includes('Mac')) return 'Mac';
  if (userAgent.includes('Windows')) return 'Windows PC';
  if (userAgent.includes('Android')) return 'Android Device';
  if (userAgent.includes('Linux')) return 'Linux Device';

  return 'Unknown Device';
};

// Mock functions for storing/retrieving biometric credentials
// In a real app, these would interact with your backend API

export const saveBiometricCredential = async (userId: string, credential: BiometricRegistrationResponse): Promise<BiometricCredential> => {
  // This would normally save to your backend
  const biometricCredential: BiometricCredential = {
    id: `biometric-${Date.now()}`,
    credentialId: credential.credentialId,
    publicKey: credential.publicKey,
    deviceName: credential.deviceName,
    createdAt: new Date().toISOString(),
    isActive: true,
  };

  // Store in localStorage for demo purposes
  if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
    const existingCredentials = getBiometricCredentials(userId);
    const updatedCredentials = [...existingCredentials, biometricCredential];
    localStorage.setItem(`biometric-${userId}`, JSON.stringify(updatedCredentials));
  }

  return biometricCredential;
};

export const getBiometricCredentials = (userId: string): BiometricCredential[] => {
  try {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return [];
    }

    const stored = localStorage.getItem(`biometric-${userId}`);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error retrieving biometric credentials:', error);
    return [];
  }
};

export const deleteBiometricCredential = async (userId: string, credentialId: string): Promise<boolean> => {
  try {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return false;
    }

    const credentials = getBiometricCredentials(userId);
    const updatedCredentials = credentials.filter(cred => cred.credentialId !== credentialId);
    localStorage.setItem(`biometric-${userId}`, JSON.stringify(updatedCredentials));
    return true;
  } catch (error) {
    console.error('Error deleting biometric credential:', error);
    return false;
  }
};

export const updateBiometricCredentialLastUsed = async (userId: string, credentialId: string): Promise<void> => {
  try {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return;
    }

    const credentials = getBiometricCredentials(userId);
    const updatedCredentials = credentials.map(cred =>
      cred.credentialId === credentialId
        ? { ...cred, lastUsed: new Date().toISOString() }
        : cred
    );
    localStorage.setItem(`biometric-${userId}`, JSON.stringify(updatedCredentials));
  } catch (error) {
    console.error('Error updating biometric credential:', error);
  }
};

// Check if user has biometric credentials
export const hasBiometricCredentials = (userId: string): boolean => {
  const credentials = getBiometricCredentials(userId);
  return credentials.length > 0 && credentials.some(cred => cred.isActive);
};

// Get biometric authentication options for a user
export const getBiometricAuthOptions = (userId: string): PublicKeyCredentialDescriptor[] => {
  const credentials = getBiometricCredentials(userId);
  return credentials
    .filter(cred => cred.isActive)
    .map(cred => ({
      id: cred.credentialId,
      type: 'public-key' as const,
      transports: ['internal'] as AuthenticatorTransport[],
    }));
};

// Verify biometric authentication (mock implementation)
export const verifyBiometricAuth = async (userId: string, authResponse: BiometricAuthResponse): Promise<boolean> => {
  // In a real implementation, this would verify the signature on the server
  // For demo purposes, we'll just check if the credential exists
  const credentials = getBiometricCredentials(userId);
  const credential = credentials.find(cred => cred.credentialId === authResponse.credentialId && cred.isActive);
  
  if (credential) {
    await updateBiometricCredentialLastUsed(userId, authResponse.credentialId);
    return true;
  }
  
  return false;
};
