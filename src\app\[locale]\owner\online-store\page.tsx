
"use client";

import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Image from 'next/image';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Store, Search, Save, Eye, EyeOff, Loader2 } from 'lucide-react';
import type { Product } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { getMockProducts, setProductOnlineStatus } from '@/lib/mock-product-data'; 
import { useScopedI18n, useI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';


export default function OwnerOnlineStorePage() {
  const { user } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [filter, setFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('all'); 
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const t = useI18n();
  const tCommon = useScopedI18n('common');
  const tOnlineStore = useScopedI18n('ownerOnlineStore');


  useEffect(() => {
    setIsLoading(true);
    if (user && typeof window !== 'undefined') {
      // Fetch products specific to this owner
      const allProducts = getMockProducts();
      setProducts(allProducts.filter(p => p.ownerId === user.id));
    }
    setIsLoading(false);
  }, [user]);

  const handleToggleOnlineStatus = (productId: string, newStatus: boolean) => {
    if (!user) return;
    const updatedProducts = setProductOnlineStatus(productId, newStatus, user.id); // Pass ownerId
    setProducts(updatedProducts.filter(p => p.ownerId === user.id)); 
    toast({
        title: tOnlineStore('toastVisibilityUpdatedTitle'),
        description: tOnlineStore('toastVisibilityUpdatedDesc', { productName: products.find(p=>p.id === productId)?.name || '', status: newStatus ? tOnlineStore('statusOnline') : tOnlineStore('statusOffline') }),
    });
  };

  const handleConfirmSettings = () => {
    if (!user) return;
    if (typeof window !== 'undefined') {
        const allProducts = getMockProducts();
        setProducts(allProducts.filter(p => p.ownerId === user.id)); 
    }
    toast({
      title: tOnlineStore('toastSettingsConfirmedTitle'),
      description: tOnlineStore('toastSettingsConfirmedDesc'),
    });
  };

  const filteredProducts = products.filter(product => {
    const nameMatch = product.name.toLowerCase().includes(filter.toLowerCase());
    const categoryMatch = product.category.toLowerCase().includes(filter.toLowerCase());
    const statusMatch = statusFilter === 'all' || 
                        (statusFilter === 'online' && product.isOnline) ||
                        (statusFilter === 'offline' && !product.isOnline);
    return (nameMatch || categoryMatch) && statusMatch && product.ownerId === user?.id;
  });
  
  const getStockStatus = (stock?: number): { text: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' } => {
    if (stock === undefined) return { text: tCommon('N_A'), variant: 'outline' };
    if (stock === 0) return { text: tOnlineStore('stockStatusOutOfStock'), variant: 'destructive' };
    if (stock < 10) return { text: tOnlineStore('stockStatusLowStock'), variant: 'secondary' };
    return { text: tOnlineStore('stockStatusInStock'), variant: 'default' };
  };

  if (isLoading && typeof window !== 'undefined' && (!localStorage.getItem('marketSyncMockProducts') || !user)) { 
    return (
      <AuthenticatedLayout expectedRole="owner">
        <div className="flex flex-col justify-center items-center h-64 p-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">{tCommon('loading')}</p>
        </div>
      </AuthenticatedLayout>
    );
  }
   if (!user) {
    return (
      <AuthenticatedLayout expectedRole="owner">
        <div className="flex flex-col justify-center items-center h-64 p-4">
          <p className="text-muted-foreground">{tCommon('error')}: User not authenticated.</p>
        </div>
      </AuthenticatedLayout>
    );
  }


  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center space-x-3">
            <Store className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold text-foreground">{tOnlineStore('title')}</h1>
              <p className="text-muted-foreground">
                {tOnlineStore('description')}
              </p>
            </div>
          </div>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{tOnlineStore('productVisibilityTitle')}</CardTitle>
            <CardDescription>
              {tOnlineStore('productVisibilityDesc')}
            </CardDescription>
            <div className="flex flex-col md:flex-row gap-2 mt-4 items-center">
                <div className="relative flex-grow w-full md:w-auto">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground rtl:right-2.5 rtl:left-auto" />
                    <Input 
                        placeholder={tOnlineStore('filterPlaceholder')}
                        className="pl-8 w-full rtl:pr-8 rtl:pl-3" 
                        value={filter}
                        onChange={(e) => setFilter(e.target.value)}
                    />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full md:w-[180px]">
                        <SelectValue placeholder={tOnlineStore('visibilityStatusPlaceholder')} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">{tOnlineStore('statusAllProducts')}</SelectItem>
                        <SelectItem value="online">{tOnlineStore('statusOnline')}</SelectItem>
                        <SelectItem value="offline">{tOnlineStore('statusOffline')}</SelectItem>
                    </SelectContent>
                </Select>
            </div>
          </CardHeader>
          <CardContent>
            {filteredProducts.length === 0 ? (
                <p className="text-muted-foreground text-center py-6">{tOnlineStore('noProductsMatchFilters')}</p>
            ) : (
            <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[60px] hidden sm:table-cell">{tOnlineStore('headerImage')}</TableHead>
                      <TableHead>{tOnlineStore('headerName')}</TableHead>
                      <TableHead className="hidden md:table-cell">{tOnlineStore('headerCategory')}</TableHead>
                      <TableHead className="text-center">{tOnlineStore('headerStock')}</TableHead>
                      <TableHead className="text-center w-[150px]">{tOnlineStore('headerOnlineVisibility')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.map((product) => {
                       const stockInfo = getStockStatus(product.stock);
                       const isOutOfStock = product.stock === undefined || product.stock === 0;
                       return (
                        <TableRow key={product.id}>
                            <TableCell className="hidden sm:table-cell">
                            <Image 
                                src={product.imageUrl || `https://picsum.photos/100/100`} 
                                alt={product.name} 
                                width={40} 
                                height={40} 
                                className="rounded object-cover"
                                data-ai-hint={product.dataAiHint || product.category.toLowerCase()}
                            />
                            </TableCell>
                            <TableCell className="font-medium">{product.name}</TableCell>
                            <TableCell className="hidden md:table-cell">{product.category}</TableCell>
                            <TableCell className="text-center">
                                <Badge variant={stockInfo.variant as any} className="text-xs whitespace-nowrap">
                                    {stockInfo.text} ({product.stock === undefined ? tCommon('N_A') : product.stock})
                                </Badge>
                            </TableCell>
                            <TableCell className="text-center">
                                <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                                    {product.isOnline ? <Eye className="h-4 w-4 text-green-500" /> : <EyeOff className="h-4 w-4 text-muted-foreground" />}
                                    <Switch
                                        id={`online-switch-${product.id}`}
                                        checked={!!product.isOnline}
                                        onCheckedChange={(checked) => handleToggleOnlineStatus(product.id, checked)}
                                        aria-label={tOnlineStore('toggleOnlineStatusAria', { productName: product.name })}
                                        disabled={isOutOfStock && !product.isOnline} 
                                    />
                                </div>
                                {isOutOfStock && !product.isOnline && <p className="text-xs text-muted-foreground mt-1">{tOnlineStore('stockStatusOutOfStock')}</p>}
                            </TableCell>
                        </TableRow>
                       );
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
           <CardFooter className="border-t px-6 py-4">
            <div className="flex justify-between items-center w-full">
                <p className="text-sm text-muted-foreground">
                    {tOnlineStore('showingProductsCount', { count: filteredProducts.length.toString(), total: products.length.toString() })}
                </p>
                <Button onClick={handleConfirmSettings} className="bg-accent hover:bg-accent/90 text-accent-foreground">
                    <Save className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {tOnlineStore('confirmSettingsButton')}
                </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}

