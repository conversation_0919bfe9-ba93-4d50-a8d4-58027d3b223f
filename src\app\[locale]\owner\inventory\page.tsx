
"use client";

import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Image from 'next/image';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Briefcase, Search, Edit, AlertTriangle, CheckCircle, Layers, TrendingUp, PackagePlus, Loader2 } from 'lucide-react';
import type { Product } from '@/types';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { getMockProducts, updateProductStock, addMockProduct } from '@/lib/mock-product-data';
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';


type InventoryProduct = Product & { 
  stockStatusResolvedKey?: 'statusInStock' | 'statusLowStock' | 'statusOutOfStock' | 'N_A';
  lastRestock?: string; 
  supplier?: string; 
};

export default function OwnerInventoryPage() {
  const { user } = useAuth();
  const [products, setProducts] = useState<InventoryProduct[]>([]);
  const [filter, setFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedProduct, setSelectedProduct] = useState<InventoryProduct | null>(null);
  const [isUpdateStockModalOpen, setIsUpdateStockModalOpen] = useState(false);
  const [newStockLevel, setNewStockLevel] = useState<number | string>('');
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const t = useScopedI18n('inventoryCheck');
  const tCommon = useScopedI18n('common');


  useEffect(() => {
    if (user) {
      setIsLoading(true);
      if (typeof window !== 'undefined') {
        loadProducts();
      }
      const timer = setTimeout(() => setIsLoading(false), 100); 
      return () => clearTimeout(timer);
    } else {
      setIsLoading(false); // No user, no loading
    }
  }, [user]);

  const loadProducts = () => {
    if (!user) return;
    const fetchedProducts = getMockProducts();
    const ownerProducts = fetchedProducts.filter(p => p.ownerId === user.id);
    const enhancedProducts = ownerProducts.map((p, index) => ({
      ...p,
      supplier: p.supplier || `${t('headerSupplier')} ${String.fromCharCode(65 + (index % 26))}`, 
      lastRestock: p.lastRestock || new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], 
      stockStatusResolvedKey: resolveStockStatusKey(p.stock),
    }));
    setProducts(enhancedProducts);
  };
  
  const resolveStockStatusKey = (stock?: number): 'statusInStock' | 'statusLowStock' | 'statusOutOfStock' | 'N_A' => {
    if (stock === undefined) return 'statusOutOfStock'; 
    if (stock === 0) return 'statusOutOfStock';
    if (stock < 10) return 'statusLowStock';
    return 'statusInStock';
  };


  const getStockBadgeVariant = (statusKey?: 'statusInStock' | 'statusLowStock' | 'statusOutOfStock' | 'N_A') => {
    switch (statusKey) {
      case 'statusInStock': return 'default';
      case 'statusLowStock': return 'secondary'; 
      case 'statusOutOfStock': return 'destructive';
      default: return 'outline';
    }
  };

  const handleOpenUpdateStockModal = (product: InventoryProduct) => {
    setSelectedProduct(product);
    setNewStockLevel(product.stock !== undefined ? product.stock : '');
    setIsUpdateStockModalOpen(true);
  };

  const handleUpdateStock = () => {
    if (selectedProduct && (typeof newStockLevel === 'number' || (typeof newStockLevel === 'string' && newStockLevel.trim() !== ''))) {
      const stockValue = Number(newStockLevel);
      if (isNaN(stockValue) || stockValue < 0) {
        toast({ title: t('toastInvalidStockTitle'), description: t('toastInvalidStockDesc'), variant: "destructive" });
        return;
      }

      updateProductStock(selectedProduct.id, stockValue);
      loadProducts(); 

      toast({ title: t('toastStockUpdatedTitle'), description: t('toastStockUpdatedDesc', {productName: selectedProduct.name, stockValue: stockValue.toString() }) });
      setIsUpdateStockModalOpen(false);
      setSelectedProduct(null);
      setNewStockLevel('');
    } else {
      toast({ title: tCommon('error'), description: t('updateErrorToastDesc'), variant: "destructive" });
    }
  };
  
  const handleRecordNewStock = () => {
    if (!user) {
      toast({ title: tCommon('error'), description: "User not authenticated.", variant: "destructive" });
      return;
    }
    const newProductData: Omit<Product, 'id'> = {
        name: t('newProductDefaultName') + " " + (products.length + 1),
        category: t('uncategorized'),
        price: 0.00,
        stock: 0,
        isOnline: false,
        dataAiHint: t('newProductDataAiHint'),
        imageUrl: `https://picsum.photos/100/100`,
        ownerId: user.id, // Assign current owner's ID
    };
    addMockProduct(newProductData);
    loadProducts(); 
    toast({title: t('toastNewProductAddedTitle'), description: t('toastNewProductAddedDesc', {productName: newProductData.name})});
  };


  const filteredProducts = products.filter(product => {
    const nameMatch = product.name.toLowerCase().includes(filter.toLowerCase());
    const categoryMatch = product.category.toLowerCase().includes(filter.toLowerCase());
    const supplierMatch = product.supplier && product.supplier.toLowerCase().includes(filter.toLowerCase());
    
    let statusKeyMatch = 'all';
    if (product.stockStatusResolvedKey === 'statusInStock') statusKeyMatch = 'in-stock';
    else if (product.stockStatusResolvedKey === 'statusLowStock') statusKeyMatch = 'low-stock';
    else if (product.stockStatusResolvedKey === 'statusOutOfStock') statusKeyMatch = 'out-of-stock';

    const statusCheck = statusFilter === 'all' || statusKeyMatch === statusFilter;
    
    return (nameMatch || categoryMatch || supplierMatch) && statusCheck && product.ownerId === user?.id;
  });

  const summaryStats = {
    totalProducts: products.length,
    inStock: products.filter(p => p.stockStatusResolvedKey === 'statusInStock').length,
    lowStock: products.filter(p => p.stockStatusResolvedKey === 'statusLowStock').length,
    outOfStock: products.filter(p => p.stockStatusResolvedKey === 'statusOutOfStock').length,
  };
  
  if (isLoading) { 
    return (
      <AuthenticatedLayout expectedRole="owner">
        <div className="flex flex-col justify-center items-center h-64 p-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">{tCommon('loading')}</p>
        </div>
      </AuthenticatedLayout>
    );
  }
   if (!user) {
    return (
      <AuthenticatedLayout expectedRole="owner">
        <div className="flex flex-col justify-center items-center h-64 p-4">
          <p className="text-muted-foreground">{tCommon('error')}: User not authenticated.</p>
        </div>
      </AuthenticatedLayout>
    );
  }


  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center space-x-3">
            <Briefcase className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
              <p className="text-muted-foreground">
                {t('description')}
              </p>
            </div>
          </div>
           <Button className="bg-accent hover:bg-accent/90 text-accent-foreground" onClick={handleRecordNewStock}>
            <PackagePlus className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('addNewProductButton')}
          </Button>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('totalProducts')}</CardTitle>
              <Layers className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.totalProducts}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('inStock')}</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.inStock}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('lowStock')}</CardTitle>
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.lowStock}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('outOfStock')}</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.outOfStock}</div>
            </CardContent>
          </Card>
        </div>


        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{t('currentStockLevelsTitle')}</CardTitle>
            <CardDescription>
              {t('currentStockLevelsDescription')}
            </CardDescription>
            <div className="flex flex-col md:flex-row gap-2 mt-4 items-center">
                <div className="relative flex-grow w-full md:w-auto">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground rtl:right-2.5 rtl:left-auto" />
                    <Input 
                        placeholder={t('filterPlaceholder')} 
                        className="pl-8 w-full rtl:pr-8 rtl:pl-3" 
                        value={filter}
                        onChange={(e) => setFilter(e.target.value)}
                    />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full md:w-[180px]">
                        <SelectValue placeholder={t('stockStatusSelectPlaceholder')} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">{t('statusAll')}</SelectItem>
                        <SelectItem value="in-stock">{t('statusInStock')}</SelectItem>
                        <SelectItem value="low-stock">{t('statusLowStock')}</SelectItem>
                        <SelectItem value="out-of-stock">{t('statusOutOfStock')}</SelectItem>
                    </SelectContent>
                </Select>
                 <Button variant="outline">
                    <TrendingUp className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('viewStockHistoryButton')}
                </Button>
            </div>
          </CardHeader>
          <CardContent>
            {filteredProducts.length === 0 ? (
                <p className="text-muted-foreground text-center py-6">{t('noProductsFound')}</p>
            ) : (
            <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[60px] hidden sm:table-cell">{t('headerImage')}</TableHead>
                      <TableHead>{t('headerName')}</TableHead>
                      <TableHead className="hidden md:table-cell">{t('headerCategory')}</TableHead>
                      <TableHead className="text-center">{t('headerStockQty')}</TableHead>
                      <TableHead>{t('headerStatus')}</TableHead>
                      <TableHead className="hidden lg:table-cell">{t('headerSupplier')}</TableHead>
                      <TableHead className="hidden lg:table-cell text-center">{t('headerLastRestock')}</TableHead>
                      <TableHead className="text-right rtl:text-left">{tCommon('actions')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.map((product) => (
                      <TableRow key={product.id} className={product.stockStatusResolvedKey === 'statusOutOfStock' ? 'bg-destructive/10' : product.stockStatusResolvedKey === 'statusLowStock' ? 'bg-yellow-500/10' : ''}>
                        <TableCell className="hidden sm:table-cell">
                          <Image 
                            src={product.imageUrl || `https://picsum.photos/50/50`} 
                            alt={product.name} 
                            width={40} 
                            height={40} 
                            className="rounded object-cover"
                            data-ai-hint={product.dataAiHint || product.category.toLowerCase()}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{product.name}</TableCell>
                        <TableCell className="hidden md:table-cell">{product.category}</TableCell>
                        <TableCell className="text-center">{product.stock}</TableCell>
                        <TableCell>
                            <Badge variant={getStockBadgeVariant(product.stockStatusResolvedKey) as any} className="text-xs whitespace-nowrap">
                                {product.stockStatusResolvedKey ? t(product.stockStatusResolvedKey) : tCommon('N_A')}
                            </Badge>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">{product.supplier || tCommon('N_A')}</TableCell>
                        <TableCell className="hidden lg:table-cell text-center text-xs">{product.lastRestock ? new Date(product.lastRestock).toLocaleDateString() : tCommon('N_A')}</TableCell>
                        <TableCell className="text-right rtl:text-left">
                          <Button variant="outline" size="sm" onClick={() => handleOpenUpdateStockModal(product)}>
                            <Edit className="mr-1 h-3 w-3 rtl:ml-1 rtl:mr-0" /> {t('updateButton')}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
            </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Dialog open={isUpdateStockModalOpen} onOpenChange={setIsUpdateStockModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t('updateStockModalTitle', {productName: selectedProduct?.name || ''})}</DialogTitle>
            <DialogDescription>
              {t('updateStockModalDescription', {currentStock: (selectedProduct?.stock || 0).toString()})}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="newStock" className="text-right rtl:text-left">
                {t('newStockLabel')}
              </Label>
              <Input
                id="newStock"
                type="number"
                value={newStockLevel}
                onChange={(e) => setNewStockLevel(e.target.value === '' ? '' : Number(e.target.value))}
                className="col-span-3"
                placeholder={t('newStockPlaceholder')}
              />
            </div>
             <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="reason" className="text-right rtl:text-left col-span-1">{t('reasonLabel')}</Label>
                <Input id="reason" placeholder={t('reasonPlaceholder')} className="col-span-3" />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline">{tCommon('cancel')}</Button>
            </DialogClose>
            <Button type="button" onClick={handleUpdateStock} className="bg-accent hover:bg-accent/90 text-accent-foreground">{tCommon('save')}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AuthenticatedLayout>
  );
}

