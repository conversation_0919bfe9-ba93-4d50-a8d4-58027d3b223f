
"use client";

import React, { useState } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Send, MessageCircle } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';
import type { Role } from '@/types';

const targetRoles: (Role | 'all')[] = ['all', 'owner', 'wholesaler', 'employee', 'customer', 'agent'];

export default function AdminAlertsPage() {
  const t = useScopedI18n('adminAlerts');
  const tCommon = useScopedI18n('common');
  const tUserManagement = useScopedI18n('userManagement');
  const { toast } = useToast();

  const [targetRole, setTargetRole] = useState<Role | 'all'>('all');
  const [notificationTitle, setNotificationTitle] = useState('');
  const [notificationMessage, setNotificationMessage] = useState('');
  const [isSending, setIsSending] = useState(false);

  const handleSendNotification = async () => {
    if (!notificationTitle.trim() || !notificationMessage.trim()) {
      toast({
        title: tCommon('error'),
        description: t('validationError'),
        variant: 'destructive',
      });
      return;
    }

    setIsSending(true);
    // Simulate sending notification
    await new Promise(resolve => setTimeout(resolve, 1000));

    toast({
      title: t('notificationSentTitle'),
      description: t('notificationSentDesc', {
        target: targetRole === 'all' ? t('targetRoleAll') : tUserManagement(`role_${targetRole}` as any, undefined, targetRole),
        title: notificationTitle,
      }),
    });

    setNotificationTitle('');
    setNotificationMessage('');
    setTargetRole('all');
    setIsSending(false);
  };

  return (
    <AuthenticatedLayout expectedRole="admin">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <MessageCircle className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{t('formTitle')}</CardTitle>
            <CardDescription>
              {t('formDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="targetRole">{t('targetRoleLabel')}</Label>
              <Select value={targetRole} onValueChange={(value: Role | 'all') => setTargetRole(value)}>
                <SelectTrigger id="targetRole">
                  <SelectValue placeholder={t('selectTargetRolePlaceholder')} />
                </SelectTrigger>
                <SelectContent>
                  {targetRoles.map(role => (
                    <SelectItem key={role} value={role}>
                      {role === 'all' ? t('targetRoleAll') : tUserManagement(`role_${role}` as any, undefined, role)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="notificationTitle">{t('notificationTitleLabel')}</Label>
              <Input
                id="notificationTitle"
                value={notificationTitle}
                onChange={(e) => setNotificationTitle(e.target.value)}
                placeholder={t('notificationTitlePlaceholder')}
              />
            </div>
            <div>
              <Label htmlFor="notificationMessage">{t('notificationMessageLabel')}</Label>
              <Textarea
                id="notificationMessage"
                value={notificationMessage}
                onChange={(e) => setNotificationMessage(e.target.value)}
                placeholder={t('notificationMessagePlaceholder')}
                rows={5}
              />
            </div>
            <Button onClick={handleSendNotification} disabled={isSending} className="w-full bg-accent hover:bg-accent/90 text-accent-foreground">
              {isSending ? (
                <Send className="mr-2 h-4 w-4 animate-pulse" />
              ) : (
                <Send className="mr-2 h-4 w-4" />
              )}
              {isSending ? t('sendingButton') : t('sendButton')}
            </Button>
          </CardContent>
        </Card>

        {/* Placeholder for displaying past notifications - Future enhancement */}
        {/*
        <Card>
          <CardHeader><CardTitle>Sent Notifications</CardTitle></CardHeader>
          <CardContent><p className="text-muted-foreground">Past notifications will be listed here.</p></CardContent>
        </Card>
        */}
      </div>
    </AuthenticatedLayout>
  );
}
