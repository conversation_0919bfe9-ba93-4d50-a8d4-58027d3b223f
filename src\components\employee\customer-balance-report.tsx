"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useState } from 'react';
import { Download, FileText, TrendingUp, TrendingDown, AlertTriangle, CheckCircle, Clock } from 'lucide-react';

interface CustomerBalance {
  id: string;
  customerName: string;
  customerId: string;
  totalDebt: number;
  totalCredit: number;
  netBalance: number;
  creditLimit: number;
  lastPayment: string;
  lastTransaction: string;
  status: 'good' | 'warning' | 'overdue';
  phone?: string;
  email?: string;
}

interface CustomerBalanceReportProps {
  customers: CustomerBalance[];
}

export function CustomerBalanceReport({ customers }: CustomerBalanceReportProps) {
  const [reportType, setReportType] = useState<'summary' | 'detailed' | 'overdue'>('summary');
  const [sortBy, setSortBy] = useState<'name' | 'balance' | 'status'>('name');

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'good':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />جيد</Badge>;
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-500"><Clock className="w-3 h-3 mr-1" />تحذير</Badge>;
      case 'overdue':
        return <Badge variant="destructive"><AlertTriangle className="w-3 h-3 mr-1" />متأخر</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getFilteredCustomers = () => {
    let filtered = [...customers];
    
    if (reportType === 'overdue') {
      filtered = filtered.filter(c => c.status === 'overdue');
    }
    
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.customerName.localeCompare(b.customerName);
        case 'balance':
          return b.netBalance - a.netBalance;
        case 'status':
          const statusOrder = { 'overdue': 3, 'warning': 2, 'good': 1 };
          return statusOrder[b.status] - statusOrder[a.status];
        default:
          return 0;
      }
    });
    
    return filtered;
  };

  const generateReport = () => {
    const filteredCustomers = getFilteredCustomers();
    const reportData = {
      reportType,
      generatedAt: new Date().toISOString(),
      totalCustomers: filteredCustomers.length,
      totalDebt: filteredCustomers.reduce((sum, c) => sum + c.totalDebt, 0),
      totalCredit: filteredCustomers.reduce((sum, c) => sum + c.totalCredit, 0),
      netBalance: filteredCustomers.reduce((sum, c) => sum + c.netBalance, 0),
      customers: filteredCustomers
    };

    // Create downloadable JSON file
    const dataStr = JSON.stringify(reportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `customer-balances-report-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const generateCSV = () => {
    const filteredCustomers = getFilteredCustomers();
    const headers = ['اسم العميل', 'رقم العميل', 'رقم الهاتف', 'إجمالي الديون', 'إجمالي الائتمان', 'الرصيد الصافي', 'حد الائتمان', 'الحالة', 'آخر دفعة'];
    
    const csvContent = [
      headers.join(','),
      ...filteredCustomers.map(customer => [
        customer.customerName,
        customer.customerId,
        customer.phone || '',
        customer.totalDebt,
        customer.totalCredit,
        customer.netBalance,
        customer.creditLimit,
        customer.status === 'good' ? 'جيد' : customer.status === 'warning' ? 'تحذير' : 'متأخر',
        customer.lastPayment
      ].join(','))
    ].join('\n');

    const dataUri = 'data:text/csv;charset=utf-8,\uFEFF' + encodeURIComponent(csvContent);
    const exportFileDefaultName = `customer-balances-${new Date().toISOString().split('T')[0]}.csv`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const filteredCustomers = getFilteredCustomers();
  const totalDebt = filteredCustomers.reduce((sum, c) => sum + c.totalDebt, 0);
  const totalCredit = filteredCustomers.reduce((sum, c) => sum + c.totalCredit, 0);
  const netBalance = filteredCustomers.reduce((sum, c) => sum + c.netBalance, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>تقرير أرصدة العملاء</span>
          <div className="flex gap-2">
            <Button onClick={generateCSV} variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              تصدير CSV
            </Button>
            <Button onClick={generateReport} variant="outline" size="sm">
              <FileText className="h-4 w-4 mr-2" />
              تصدير JSON
            </Button>
          </div>
        </CardTitle>
        <CardDescription>
          تقرير شامل لأرصدة العملاء ومعلومات الائتمان
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Report Controls */}
        <div className="flex gap-4">
          <div className="flex-1">
            <label className="text-sm font-medium">نوع التقرير</label>
            <Select value={reportType} onValueChange={(value: 'summary' | 'detailed' | 'overdue') => setReportType(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="summary">ملخص عام</SelectItem>
                <SelectItem value="detailed">تفصيلي</SelectItem>
                <SelectItem value="overdue">المتأخرين فقط</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex-1">
            <label className="text-sm font-medium">ترتيب حسب</label>
            <Select value={sortBy} onValueChange={(value: 'name' | 'balance' | 'status') => setSortBy(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">الاسم</SelectItem>
                <SelectItem value="balance">الرصيد</SelectItem>
                <SelectItem value="status">الحالة</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الديون</CardTitle>
              <TrendingUp className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {totalDebt.toLocaleString()} ر.ي
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الائتمان</CardTitle>
              <TrendingDown className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {totalCredit.toLocaleString()} ر.ي
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الرصيد الصافي</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${netBalance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {netBalance.toLocaleString()} ر.ي
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Customers Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>اسم العميل</TableHead>
                <TableHead>رقم الهاتف</TableHead>
                {reportType !== 'summary' && <TableHead>إجمالي الديون</TableHead>}
                {reportType !== 'summary' && <TableHead>إجمالي الائتمان</TableHead>}
                <TableHead>الرصيد الصافي</TableHead>
                {reportType !== 'summary' && <TableHead>حد الائتمان</TableHead>}
                <TableHead>الحالة</TableHead>
                {reportType !== 'summary' && <TableHead>آخر دفعة</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCustomers.map((customer) => (
                <TableRow key={customer.id}>
                  <TableCell className="font-medium">{customer.customerName}</TableCell>
                  <TableCell>{customer.phone}</TableCell>
                  {reportType !== 'summary' && (
                    <TableCell className="text-red-600">{customer.totalDebt.toLocaleString()} ر.ي</TableCell>
                  )}
                  {reportType !== 'summary' && (
                    <TableCell className="text-green-600">{customer.totalCredit.toLocaleString()} ر.ي</TableCell>
                  )}
                  <TableCell className={customer.netBalance > 0 ? "text-red-600" : "text-green-600"}>
                    {customer.netBalance.toLocaleString()} ر.ي
                  </TableCell>
                  {reportType !== 'summary' && (
                    <TableCell>{customer.creditLimit.toLocaleString()} ر.ي</TableCell>
                  )}
                  <TableCell>{getStatusBadge(customer.status)}</TableCell>
                  {reportType !== 'summary' && <TableCell>{customer.lastPayment}</TableCell>}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {filteredCustomers.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            لا توجد بيانات لعرضها حسب المعايير المحددة
          </div>
        )}
      </CardContent>
    </Card>
  );
}
