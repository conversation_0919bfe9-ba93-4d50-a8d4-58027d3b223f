
"use client";

import React, { useState } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from '@/components/ui/badge';
import { BookText, CalendarIcon, PlusCircle, Edit, Trash2, Filter, DollarSign } from 'lucide-react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import type { Debt } from '@/types';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Form, FormField, FormItem, FormControl, FormMessage } from '@/components/ui/form'; // Corrected import

const debtFormSchema = z.object({
  partyName: z.string().min(1, "Party name is required."),
  amount: z.coerce.number().positive("Amount must be positive."),
  type: z.enum(['debt', 'credit']),
  status: z.enum(['unpaid', 'paid', 'partially_paid']),
  date: z.date({ required_error: "Date is required." }),
  dueDate: z.date().optional(),
  notes: z.string().optional(),
});

type DebtFormValues = z.infer<typeof debtFormSchema>;

const mockDebtsData: Debt[] = [
  { id: 'debt001', partyName: 'John Customer', type: 'debt', amount: 150.75, date: '2024-07-10', dueDate: '2024-08-10', status: 'unpaid', notes: 'For groceries bought on credit.' },
  { id: 'debt002', partyName: 'Supplier GreenFarm', type: 'credit', amount: 500.00, date: '2024-07-15', status: 'unpaid', notes: 'Payment for last vegetable shipment.' },
  { id: 'debt003', partyName: 'Alice Neighbor', type: 'debt', amount: 25.50, date: '2024-06-20', status: 'paid', notes: 'Borrowed for milk & bread.' },
];

export default function OwnerDebtsPage() {
  const [debts, setDebts] = useState<Debt[]>(mockDebtsData);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingDebt, setEditingDebt] = useState<Debt | null>(null);
  const { toast } = useToast();

  const form = useForm<DebtFormValues>({
    resolver: zodResolver(debtFormSchema),
    defaultValues: {
      partyName: '',
      amount: 0,
      type: 'debt',
      status: 'unpaid',
      notes: '',
    },
  });

  const onSubmit = (data: DebtFormValues) => {
    const newDebt: Debt = {
      id: editingDebt ? editingDebt.id : `debt${Date.now()}`,
      ...data,
      date: format(data.date, 'yyyy-MM-dd'),
      dueDate: data.dueDate ? format(data.dueDate, 'yyyy-MM-dd') : undefined,
    };

    if (editingDebt) {
      setDebts(debts.map(d => d.id === newDebt.id ? newDebt : d));
      toast({ title: "Debt Updated", description: `Details for ${newDebt.partyName} updated.` });
    } else {
      setDebts([newDebt, ...debts]);
      toast({ title: "Debt Recorded", description: `${newDebt.type === 'debt' ? 'Debt' : 'Credit'} for ${newDebt.partyName} recorded.` });
    }
    form.reset();
    setIsFormOpen(false);
    setEditingDebt(null);
  };

  const handleEdit = (debt: Debt) => {
    setEditingDebt(debt);
    form.reset({
      ...debt,
      date: new Date(debt.date),
      dueDate: debt.dueDate ? new Date(debt.dueDate) : undefined,
    });
    setIsFormOpen(true);
  };

  const handleDelete = (debtId: string) => {
    setDebts(debts.filter(d => d.id !== debtId));
    toast({ title: "Debt Removed", description: "The debt record has been removed.", variant: "destructive" });
  };
  
  const getStatusBadgeVariant = (status: Debt['status']) => {
    if (status === 'paid') return 'default'; // default usually maps to primary/success-like
    if (status === 'partially_paid') return 'secondary';
    return 'destructive'; // unpaid
  };


  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center space-x-3">
            <BookText className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold text-foreground">Debts Management</h1>
              <p className="text-muted-foreground">
                Record and manage outstanding debts and credits.
              </p>
            </div>
          </div>
          <Button onClick={() => { setEditingDebt(null); form.reset(); setIsFormOpen(true);}} className="bg-accent hover:bg-accent/90 text-accent-foreground">
            <PlusCircle className="mr-2 h-4 w-4" /> Record New Debt/Credit
          </Button>
        </div>

        {isFormOpen && (
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>{editingDebt ? 'Edit' : 'Record New'} Debt/Credit</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="partyName"
                    render={({ field }) => (
                      <FormItem>
                        <Label htmlFor="partyName">Party Name</Label>
                        <FormControl>
                        <Input id="partyName" placeholder="e.g., John Customer or Supplier X" {...field} />
                        </FormControl>
                        <FormMessage>{form.formState.errors.partyName?.message}</FormMessage>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <Label htmlFor="amount">Amount</Label>
                        <div className="relative">
                           <DollarSign className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                           <FormControl>
                           <Input id="amount" type="number" step="0.01" placeholder="0.00" {...field} className="pl-8" />
                           </FormControl>
                        </div>
                        <FormMessage>{form.formState.errors.amount?.message}</FormMessage>
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                   <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <Label htmlFor="type">Type</Label>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                          <SelectTrigger id="type">
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="debt">Debt (Owed to you)</SelectItem>
                            <SelectItem value="credit">Credit (You Owe)</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage>{form.formState.errors.type?.message}</FormMessage>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <Label htmlFor="status">Status</Label>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                           <FormControl>
                          <SelectTrigger id="status">
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="unpaid">Unpaid</SelectItem>
                            <SelectItem value="paid">Paid</SelectItem>
                            <SelectItem value="partially_paid">Partially Paid</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage>{form.formState.errors.status?.message}</FormMessage>
                      </FormItem>
                    )}
                  />
                </div>
                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="date"
                        render={({ field }) => (
                        <FormItem className="flex flex-col">
                            <Label htmlFor="date">Date</Label>
                            <Popover>
                            <PopoverTrigger asChild>
                                <FormControl>
                                <Button
                                variant={"outline"}
                                className={`w-full justify-start text-left font-normal ${!field.value && "text-muted-foreground"}`}
                                >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {field.value ? format(field.value, "PPP") : <span>Pick a date</span>}
                                </Button>
                                </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                                <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                initialFocus
                                />
                            </PopoverContent>
                            </Popover>
                           <FormMessage>{form.formState.errors.date?.message}</FormMessage>
                        </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="dueDate"
                        render={({ field }) => (
                        <FormItem className="flex flex-col">
                            <Label htmlFor="dueDate">Due Date (Optional)</Label>
                            <Popover>
                            <PopoverTrigger asChild>
                                 <FormControl>
                                <Button
                                variant={"outline"}
                                className={`w-full justify-start text-left font-normal ${!field.value && "text-muted-foreground"}`}
                                >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {field.value ? format(field.value, "PPP") : <span>Pick a due date</span>}
                                </Button>
                                </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                                <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                />
                            </PopoverContent>
                            </Popover>
                             <FormMessage>{form.formState.errors.dueDate?.message}</FormMessage>
                        </FormItem>
                        )}
                    />
                </div>
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <Label htmlFor="notes">Notes (Optional)</Label>
                      <FormControl>
                      <Textarea id="notes" placeholder="Any additional details..." {...field} />
                      </FormControl>
                       <FormMessage>{form.formState.errors.notes?.message}</FormMessage>
                    </FormItem>
                  )}
                />
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => { setIsFormOpen(false); setEditingDebt(null); form.reset(); }}>Cancel</Button>
                  <Button type="submit" className="bg-accent hover:bg-accent/90 text-accent-foreground">{editingDebt ? 'Save Changes' : 'Record'}</Button>
                </div>
              </form>
              </Form>
            </CardContent>
          </Card>
        )}

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>Debt & Credit Records</CardTitle>
            <CardDescription>
              Overview of all recorded debts and credits.
            </CardDescription>
            <div className="mt-4">
                 <Input placeholder="Filter by party name or notes..." className="max-w-sm" />
            </div>
          </CardHeader>
          <CardContent>
            {debts.length === 0 ? (
              <p className="text-muted-foreground text-center py-6">No debts or credits recorded yet.</p>
            ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Party Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="hidden md:table-cell">Notes</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {debts.map((debt) => (
                    <TableRow key={debt.id}>
                      <TableCell className="font-medium">{debt.partyName}</TableCell>
                      <TableCell>
                        <Badge variant={debt.type === 'debt' ? 'outline' : 'secondary'} className="capitalize">
                            {debt.type}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">${debt.amount.toFixed(2)}</TableCell>
                      <TableCell>{format(new Date(debt.date), "PP")}</TableCell>
                      <TableCell>{debt.dueDate ? format(new Date(debt.dueDate), "PP") : 'N/A'}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(debt.status) as any} className="capitalize">
                            {debt.status.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell className="hidden md:table-cell text-xs truncate max-w-xs" title={debt.notes}>{debt.notes || 'N/A'}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" onClick={() => handleEdit(debt)} aria-label="Edit Debt">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="icon" className="text-destructive hover:text-destructive" aria-label="Delete Debt">
                                <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This action cannot be undone. This will permanently delete the debt record for {debt.partyName}.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleDelete(debt.id)} className="bg-destructive hover:bg-destructive/90 text-destructive-foreground">
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            )}
          </CardContent>
           <CardFooter className="text-sm text-muted-foreground">
                Showing {debts.length} of {debts.length} records.
            </CardFooter>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}
