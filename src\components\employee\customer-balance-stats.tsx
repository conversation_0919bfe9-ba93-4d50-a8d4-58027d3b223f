"use client";

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { AlertTriangle, CheckCircle, Clock, TrendingUp, TrendingDown, Users, CreditCard } from 'lucide-react';

interface CustomerBalance {
  id: string;
  customerName: string;
  customerId: string;
  totalDebt: number;
  totalCredit: number;
  netBalance: number;
  creditLimit: number;
  lastPayment: string;
  lastTransaction: string;
  status: 'good' | 'warning' | 'overdue';
  phone?: string;
  email?: string;
}

interface CustomerBalanceStatsProps {
  customers: CustomerBalance[];
}

export function CustomerBalanceStats({ customers }: CustomerBalanceStatsProps) {
  const totalCustomers = customers.length;
  const goodCustomers = customers.filter(c => c.status === 'good').length;
  const warningCustomers = customers.filter(c => c.status === 'warning').length;
  const overdueCustomers = customers.filter(c => c.status === 'overdue').length;

  const totalDebt = customers.reduce((sum, c) => sum + c.totalDebt, 0);
  const totalCredit = customers.reduce((sum, c) => sum + c.totalCredit, 0);
  const netBalance = customers.reduce((sum, c) => sum + c.netBalance, 0);
  const totalCreditLimit = customers.reduce((sum, c) => sum + c.creditLimit, 0);

  const averageDebt = totalCustomers > 0 ? totalDebt / totalCustomers : 0;
  const averageCredit = totalCustomers > 0 ? totalCredit / totalCustomers : 0;

  const creditUtilization = totalCreditLimit > 0 ? (netBalance / totalCreditLimit) * 100 : 0;

  const topDebtors = customers
    .filter(c => c.netBalance > 0)
    .sort((a, b) => b.netBalance - a.netBalance)
    .slice(0, 5);

  const recentPayments = customers
    .filter(c => c.lastPayment)
    .sort((a, b) => new Date(b.lastPayment).getTime() - new Date(a.lastPayment).getTime())
    .slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Status Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            توزيع حالات العملاء
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{goodCustomers}</div>
              <div className="text-sm text-muted-foreground">عملاء بحالة جيدة</div>
              <Progress value={(goodCustomers / totalCustomers) * 100} className="mt-2 h-2" />
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{warningCustomers}</div>
              <div className="text-sm text-muted-foreground">عملاء في حالة تحذير</div>
              <Progress value={(warningCustomers / totalCustomers) * 100} className="mt-2 h-2" />
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{overdueCustomers}</div>
              <div className="text-sm text-muted-foreground">عملاء متأخرين</div>
              <Progress value={(overdueCustomers / totalCustomers) * 100} className="mt-2 h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Financial Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط الديون</CardTitle>
            <TrendingUp className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {averageDebt.toLocaleString()} ر.ي
            </div>
            <p className="text-xs text-muted-foreground">
              لكل عميل
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط الائتمان</CardTitle>
            <TrendingDown className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {averageCredit.toLocaleString()} ر.ي
            </div>
            <p className="text-xs text-muted-foreground">
              لكل عميل
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">استخدام الائتمان</CardTitle>
            <CreditCard className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {creditUtilization.toFixed(1)}%
            </div>
            <Progress value={creditUtilization} className="mt-2 h-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي حدود الائتمان</CardTitle>
            <CheckCircle className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {totalCreditLimit.toLocaleString()} ر.ي
            </div>
            <p className="text-xs text-muted-foreground">
              مجموع الحدود
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Top Debtors and Recent Payments */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
              أكبر المدينين
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topDebtors.map((customer, index) => (
                <div key={customer.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Badge variant="outline">{index + 1}</Badge>
                    <div>
                      <div className="font-medium">{customer.customerName}</div>
                      <div className="text-sm text-muted-foreground">{customer.phone}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-red-600">
                      {customer.netBalance.toLocaleString()} ر.ي
                    </div>
                    <div className="text-xs text-muted-foreground">
                      من {customer.creditLimit.toLocaleString()} ر.ي
                    </div>
                  </div>
                </div>
              ))}
              {topDebtors.length === 0 && (
                <div className="text-center text-muted-foreground py-4">
                  لا توجد ديون مستحقة
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
              آخر المدفوعات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentPayments.map((customer, index) => (
                <div key={customer.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Badge variant="outline">{index + 1}</Badge>
                    <div>
                      <div className="font-medium">{customer.customerName}</div>
                      <div className="text-sm text-muted-foreground">{customer.phone}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-green-600">
                      {customer.lastPayment}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      رصيد: {customer.netBalance.toLocaleString()} ر.ي
                    </div>
                  </div>
                </div>
              ))}
              {recentPayments.length === 0 && (
                <div className="text-center text-muted-foreground py-4">
                  لا توجد مدفوعات حديثة
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Risk Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2 text-orange-500" />
            تحليل المخاطر
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-lg font-bold text-red-600">
                {((overdueCustomers / totalCustomers) * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">معدل التأخير</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-lg font-bold text-orange-600">
                {creditUtilization.toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">استخدام الائتمان</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-lg font-bold text-blue-600">
                {(netBalance / totalDebt * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">نسبة الديون الصافية</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
