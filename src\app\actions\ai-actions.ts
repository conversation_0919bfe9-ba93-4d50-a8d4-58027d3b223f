
"use server";

import { predictDemand } from "@/ai/flows/smart-inventory-predictions";
import type { PredictDemandInput, PredictDemandOutput as AiPredictDemandOutput } from "@/ai/flows/smart-inventory-predictions"; // Renamed to avoid conflict
import { generateOrEnhanceProductImage } from "@/ai/flows/enhance-product-image-flow";
import type { EnhanceProductImageInput, EnhanceProductImageOutput } from "@/ai/flows/enhance-product-image-flow";
import type { InventoryPrediction } from "@/types"; // Kept original type name for application use

export async function predictDemandServerAction(
  input: PredictDemandInput
): Promise<InventoryPrediction | null> {
  try {
    const result: AiPredictDemandOutput = await predictDemand(input);
    
    const mappedResult: InventoryPrediction = {
      productCategory: input.productCategory,
      predictedDemand: result.predictedDemand,
      recommendedOrderQuantity: result.recommendedOrderQuantity,
      confidenceLevel: result.confidenceLevel,
      explanation: result.explanation,
    };
    return mappedResult;

  } catch (error) {
    console.error("Error in predictDemandServerAction:", error);
    return null;
  }
}


export async function generateOrEnhanceProductImageAction(
  input: EnhanceProductImageInput
): Promise<EnhanceProductImageOutput | null> {
  try {
    const result = await generateOrEnhanceProductImage(input);
    return result;
  } catch (error) {
    console.error("Error in generateOrEnhanceProductImageAction:", error);
    // Propagate the error message to the client for better feedback
    if (error instanceof Error) {
        throw new Error(error.message || "Failed to generate or enhance image due to an internal error.");
    }
    throw new Error("Failed to generate or enhance image due to an unknown internal error.");
  }
}

