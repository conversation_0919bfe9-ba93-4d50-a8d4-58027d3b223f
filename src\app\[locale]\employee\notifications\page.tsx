
"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { NotificationList } from '@/components/employee/notification-list';
import { Bell } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';

export default function EmployeeNotificationsPage() {
  const t = useScopedI18n('employeeNotifications');

  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Bell className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        <NotificationList />
      </div>
    </AuthenticatedLayout>
  );
}
