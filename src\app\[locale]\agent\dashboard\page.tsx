"use client"; // Make this a client component

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Truck, PackageCheck, MessageSquare, Bell, MapPin } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';

export default function AgentDashboardPage() {
  const t = useScopedI18n('agentDashboard');
  const tCommon = useScopedI18n('common');

  const currentTasks = [
    { titleKey: "newDeliveryTasks", count: 3, icon: Truck, color: "text-blue-500", href: "/agent/orders?status=new" },
    { titleKey: "inProgressDeliveries", count: 2, icon: PackageCheck, color: "text-orange-500", href: "/agent/orders?status=in-progress" },
    { titleKey: "recentNotifications", count: 5, icon: Bell, color: "text-yellow-500", href: "/agent/notifications" },
  ];

  const quickActions = [
    { labelKey: "viewAssignedOrders", href: "/agent/orders", icon: Truck },
    { labelKey: "updateOrderStatus", href: "/agent/update-status", icon: PackageCheck }, 
    { labelKey: "communicationCenter", href: "/agent/communication", icon: MessageSquare },
    { labelKey: "myRouteForToday", href: "/agent/route", icon: MapPin }, 
  ];

  return (
    <AuthenticatedLayout expectedRole="agent">
      <div className="space-y-6 p-1">
        <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {currentTasks.map((task) => (
            <Card key={task.titleKey} className="shadow-md hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t(task.titleKey as any)}
                </CardTitle>
                <task.icon className={`h-5 w-5 ${task.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{task.count}</div>
                <Button variant="link" asChild className="p-0 h-auto text-xs text-muted-foreground">
                  <Link href={task.href}>{t('viewTasks')}</Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>{t('quickActions')}</CardTitle>
            <CardDescription>{t('quickActionsDescription')}</CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-4">
            {quickActions.map(action => (
              <Button key={action.labelKey} variant="outline" asChild className="justify-start">
                <Link href={action.href}>
                  <action.icon className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
                  {t(action.labelKey as any)}
                </Link>
              </Button>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('deliveryPerformance')}</CardTitle>
            <CardDescription>{t('deliveryPerformanceDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-1 text-sm text-muted-foreground">
              <li>{t('completedDeliveriesLast7Days', { count: 25 })}</li>
              <li>{t('onTimeDeliveryRate', { rate: 95 })}</li>
              <li>{t('averageDeliveryTime', { time: 35 })}</li>
            </ul>
          </CardContent>
        </Card>

      </div>
    </AuthenticatedLayout>
  );
}
