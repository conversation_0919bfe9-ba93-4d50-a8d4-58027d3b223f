"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/contexts/auth-context";
import { Loader2, Fingerprint, Settings, Store, Mail, Lock, Eye, EyeOff } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useScopedI18n } from "@/lib/i18n/client"; // For Client Components
import { BiometricSetup } from "./biometric-setup";
import {
  isBiometricSupported,
  hasBiometricCredentials,
  getBiometricAuthOptions,
  authenticateBiometric,
  verifyBiometricAuth
} from "@/lib/biometric-auth";
import { useState, useEffect } from "react";

export function LoginForm() {
  const { login, isLoading, biometricLogin } = useAuth();
  const t = useScopedI18n('login');
  const tAuth = useScopedI18n('auth'); // For auth related messages like "Login Failed"

  const [isBiometricSetupOpen, setIsBiometricSetupOpen] = useState(false);
  const [canUseBiometric, setCanUseBiometric] = useState(false);
  const [isBiometricLoading, setIsBiometricLoading] = useState(false);
  const [lastUsername, setLastUsername] = useState('');
  const [isBiometricSupportedState, setIsBiometricSupportedState] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const loginFormSchema = z.object({
    username: z.string().min(1, { message: t('usernameRequired') }),
    password: z.string().min(1, { message: t('passwordRequired') }),
  });
  
  type LoginFormValues = z.infer<typeof loginFormSchema>;

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  useEffect(() => {
    // Set client state to true after component mounts
    setIsClient(true);

    // Check if biometric is available and user has credentials
    const checkBiometric = async () => {
      const supported = isBiometricSupported();
      setIsBiometricSupportedState(supported);

      const storedUsername = localStorage.getItem('lastUsername');

      if (supported && storedUsername) {
        const hasCredentials = hasBiometricCredentials(storedUsername);
        setCanUseBiometric(hasCredentials);
        setLastUsername(storedUsername);

        if (hasCredentials) {
          form.setValue('username', storedUsername);
        }
      }
    };

    checkBiometric();
  }, [form]);

  async function onSubmit(values: LoginFormValues) {
    // Store username for biometric use
    localStorage.setItem('lastUsername', values.username);

    // Login function in useAuth already handles toast messages.
    // If specific login failed messages were to be shown here, use tAuth.
    await login(values.username, values.password);
  }

  const handleBiometricLogin = async () => {
    if (!lastUsername) return;

    setIsBiometricLoading(true);
    try {
      const allowCredentials = getBiometricAuthOptions(lastUsername);
      if (allowCredentials.length === 0) {
        throw new Error('لا توجد بصمات مسجلة');
      }

      const authResponse = await authenticateBiometric(allowCredentials);
      const isValid = await verifyBiometricAuth(lastUsername, authResponse);

      if (isValid && biometricLogin) {
        await biometricLogin(lastUsername);
      } else {
        throw new Error('فشل في التحقق من البصمة');
      }
    } catch (error) {
      console.error('Biometric login failed:', error);
      // Handle error silently or show a toast
    } finally {
      setIsBiometricLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-teal-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-4">
      <div className="w-full max-w-md">
        {/* Logo and Brand */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-2xl shadow-lg mb-4">
            <Store className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">MarketSync</h1>
          <p className="text-gray-600 dark:text-gray-400">نظام إدارة المتاجر المتطور</p>
        </div>

        <Card className="w-full shadow-2xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm login-form-container">
          <CardHeader className="space-y-1 pb-6">
            <CardTitle className="text-2xl font-bold text-center text-gray-900 dark:text-white">
              {t('welcome')}
            </CardTitle>
            <CardDescription className="text-center text-gray-600 dark:text-gray-400">
              {t('signInPrompt')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 dark:text-gray-300 font-medium">
                        {t('usernameLabel')}
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                          <Input
                            placeholder={t('usernamePlaceholder')}
                            className="pl-10 h-12 border-gray-200 dark:border-gray-600 focus:border-primary focus:ring-primary/20 transition-all duration-200"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 dark:text-gray-300 font-medium">
                        {t('passwordLabel')}
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                          <Input
                            type={showPassword ? "text" : "password"}
                            placeholder={t('passwordPlaceholder')}
                            className="pl-10 pr-10 h-12 border-gray-200 dark:border-gray-600 focus:border-primary focus:ring-primary/20 transition-all duration-200"
                            {...field}
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                          >
                            {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                          </button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="space-y-4">
                  <Button
                    type="submit"
                    className="w-full h-12 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] login-button"
                    disabled={isLoading || isBiometricLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        {t('signingIn')}
                      </>
                    ) : (
                      <>
                        <span>{t('signInButton')}</span>
                      </>
                    )}
                  </Button>

                  {/* Biometric Login Button */}
                  {canUseBiometric && (
                    <>
                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t border-gray-200 dark:border-gray-600" />
                        </div>
                        <div className="relative flex justify-center text-xs uppercase">
                          <span className="bg-white dark:bg-gray-800 px-2 text-gray-500 dark:text-gray-400">أو</span>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        className="w-full h-12 border-2 border-primary/20 hover:border-primary/40 hover:bg-primary/5 transition-all duration-200 transform hover:scale-[1.02]"
                        onClick={handleBiometricLogin}
                        disabled={isLoading || isBiometricLoading}
                      >
                        {isBiometricLoading ? (
                          <>
                            <Loader2 className="mr-2 h-5 w-5 animate-spin text-primary" />
                            <span className="text-primary font-medium">جارٍ التحقق من البصمة...</span>
                          </>
                        ) : (
                          <>
                            <Fingerprint className="mr-2 h-5 w-5 text-primary" />
                            <span className="text-primary font-medium">تسجيل الدخول بالبصمة</span>
                          </>
                        )}
                      </Button>
                    </>
                  )}

                  {/* Biometric Setup Button */}
                  {isClient && isBiometricSupportedState && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="w-full text-gray-600 dark:text-gray-400 hover:text-primary hover:bg-primary/5 transition-all duration-200"
                      onClick={() => setIsBiometricSetupOpen(true)}
                    >
                      <Settings className="mr-2 h-4 w-4" />
                      إعداد البصمة
                    </Button>
                  )}
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            © 2024 MarketSync. جميع الحقوق محفوظة
          </p>
        </div>

        {/* Biometric Setup Dialog */}
        <BiometricSetup
          isOpen={isBiometricSetupOpen}
          onClose={() => setIsBiometricSetupOpen(false)}
        />
      </div>
    </div>
  );
}
