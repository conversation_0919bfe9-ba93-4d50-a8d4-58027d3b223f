import 'package:flutter/material.dart';
import 'package:local_auth/local_auth.dart';
import '../../../core/services/biometric_service.dart';

class BiometricSettingsScreen extends StatefulWidget {
  @override
  _BiometricSettingsScreenState createState() => _BiometricSettingsScreenState();
}

class _BiometricSettingsScreenState extends State<BiometricSettingsScreen> {
  bool isLoading = true;
  bool biometricAvailable = false;
  bool biometricEnabled = false;
  List<BiometricType> availableTypes = [];
  Map<String, dynamic> biometricStatus = {};

  @override
  void initState() {
    super.initState();
    _loadBiometricStatus();
  }

  void _loadBiometricStatus() async {
    setState(() { isLoading = true; });
    
    try {
      final status = await BiometricService.getBiometricStatus();
      final types = await BiometricService.getAvailableBiometrics();
      
      if (mounted) {
        setState(() {
          biometricStatus = status;
          biometricAvailable = status['isAvailable'] ?? false;
          biometricEnabled = status['isEnabled'] ?? false;
          availableTypes = types;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() { isLoading = false; });
        _showErrorSnackBar('خطأ في تحميل إعدادات البصمة');
      }
    }
  }

  void _toggleBiometric() async {
    if (!biometricAvailable) {
      _showErrorSnackBar('البصمة غير متوفرة على هذا الجهاز');
      return;
    }

    setState(() { isLoading = true; });

    try {
      if (biometricEnabled) {
        // Disable biometric
        final success = await BiometricService.disableBiometric();
        if (success) {
          setState(() { biometricEnabled = false; });
          _showSuccessSnackBar('تم إلغاء تفعيل البصمة');
        } else {
          _showErrorSnackBar('فشل في إلغاء تفعيل البصمة');
        }
      } else {
        // Enable biometric
        final success = await BiometricService.enableBiometric('user123', 'user123');
        if (success) {
          setState(() { biometricEnabled = true; });
          _showSuccessSnackBar('تم تفعيل البصمة بنجاح');
        } else {
          _showErrorSnackBar('فشل في تفعيل البصمة');
        }
      }
    } catch (e) {
      _showErrorSnackBar(BiometricService.getLocalizedErrorMessage(e.toString()));
    } finally {
      setState(() { isLoading = false; });
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Widget _buildBiometricTypeChip(BiometricType type) {
    return Chip(
      label: Text(BiometricService.getBiometricTypeName(type)),
      avatar: Icon(
        type == BiometricType.face ? Icons.face : Icons.fingerprint,
        size: 18,
      ),
      backgroundColor: Colors.blue.shade50,
      labelStyle: TextStyle(color: Colors.blue.shade700),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات البصمة'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status Card
                  Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                biometricAvailable ? Icons.check_circle : Icons.error,
                                color: biometricAvailable ? Colors.green : Colors.red,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'حالة البصمة',
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          
                          _buildStatusRow('دعم الجهاز', biometricAvailable ? 'متوفر' : 'غير متوفر'),
                          _buildStatusRow('الحالة الحالية', biometricEnabled ? 'مفعل' : 'غير مفعل'),
                          
                          if (availableTypes.isNotEmpty) ...[
                            const SizedBox(height: 12),
                            const Text('الأنواع المتوفرة:', style: TextStyle(fontWeight: FontWeight.bold)),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8,
                              children: availableTypes.map(_buildBiometricTypeChip).toList(),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Control Card
                  Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'التحكم في البصمة',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 16),
                          
                          SwitchListTile(
                            title: const Text('تفعيل البصمة'),
                            subtitle: Text(
                              biometricEnabled 
                                  ? 'يمكنك تسجيل الدخول باستخدام البصمة'
                                  : 'قم بتفعيل البصمة لتسجيل دخول أسرع',
                            ),
                            value: biometricEnabled,
                            onChanged: biometricAvailable ? (value) => _toggleBiometric() : null,
                            secondary: Icon(
                              biometricEnabled ? Icons.fingerprint : Icons.fingerprint_outlined,
                              color: biometricEnabled ? Colors.green : Colors.grey,
                            ),
                          ),
                          
                          if (!biometricAvailable)
                            Container(
                              margin: const EdgeInsets.only(top: 8),
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.orange.shade50,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.orange.shade200),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.warning, color: Colors.orange.shade700),
                                  const SizedBox(width: 8),
                                  const Expanded(
                                    child: Text(
                                      'البصمة غير متوفرة على هذا الجهاز أو لم يتم إعدادها',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Security Info Card
                  Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.security, color: Colors.blue.shade600),
                              const SizedBox(width: 8),
                              Text(
                                'معلومات الأمان',
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          
                          _buildSecurityPoint('🔒', 'بياناتك البيومترية محفوظة بأمان على جهازك فقط'),
                          _buildSecurityPoint('🚫', 'لا نقوم بإرسال أو تخزين بصماتك على خوادمنا'),
                          _buildSecurityPoint('🔑', 'نستخدم مفاتيح التشفير فقط للتحقق من هويتك'),
                          _buildSecurityPoint('🗑️', 'يمكنك حذف بياناتك البيومترية في أي وقت'),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Test Button
                  if (biometricEnabled)
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () async {
                          try {
                            final userId = await BiometricService.authenticateWithBiometric();
                            if (userId != null) {
                              _showSuccessSnackBar('تم التحقق من البصمة بنجاح!');
                            } else {
                              _showErrorSnackBar('فشل في التحقق من البصمة');
                            }
                          } catch (e) {
                            _showErrorSnackBar(BiometricService.getLocalizedErrorMessage(e.toString()));
                          }
                        },
                        icon: const Icon(Icons.fingerprint),
                        label: const Text('اختبار البصمة'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          backgroundColor: Colors.blue.shade600,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
            ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: TextStyle(color: Colors.grey.shade600)),
        ],
      ),
    );
  }

  Widget _buildSecurityPoint(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
