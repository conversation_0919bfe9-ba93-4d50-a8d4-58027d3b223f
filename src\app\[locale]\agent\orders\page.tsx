"use client"; // Make this a client component

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Truck, PackageCheck, MapPin, Phone, MessageSquare, CheckCircle, Edit, ListFilter, ExternalLink } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useScopedI18n } from '@/lib/i18n/client';


const mockAgentOrders = [
  { id: 'DLV001', customerName: 'Alice Smith', address: '123 Main St, Anytown, AT 12345', status: 'Assigned', items: 3, deliveryTimeSlot: '10:00 AM - 12:00 PM', contact: '555-1234', notes: 'Leave at front porch if no answer.' },
  { id: 'DLV002', customerName: '<PERSON>', address: '456 Oak Ave, Anytown, AT 12345', status: 'Out for Delivery', items: 2, deliveryTimeSlot: '01:00 PM - 03:00 PM', contact: '555-5678', notes: '' },
  { id: 'DLV003', customerName: 'Carol White', address: '789 Pine Ln, Anytown, AT 12345', status: 'Delivered', items: 5, deliveryTimeSlot: 'Completed 02:30 PM', contact: '555-9012', notes: 'Customer requested contactless delivery.'},
  { id: 'DLV004', customerName: 'David Brown', address: '101 Maple Dr, Anytown, AT 12345', status: 'Assigned', items: 1, deliveryTimeSlot: '04:00 PM - 06:00 PM', contact: '555-3456', notes: 'Beware of dog.' },
  { id: 'DLV005', customerName: 'Emily Green', address: '222 Birch Rd, Otherville, OV 67890', status: 'Failed Attempt', items: 4, deliveryTimeSlot: '09:00 AM - 11:00 AM', contact: '555-7890', notes: 'Customer not home. Will re-attempt tomorrow.' },
];

type OrderStatusKey = 'Assigned' | 'Out for Delivery' | 'Delivered' | 'Failed Attempt' | 'All';


export default function AgentOrdersPage() {
  const t = useScopedI18n('agentOrders');
  const tCommon = useScopedI18n('common');

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'assigned': return 'secondary';
      case 'out for delivery': return 'default'; 
      case 'delivered': return 'outline'; 
      case 'failed attempt': return 'destructive';
      default: return 'secondary';
    }
  };

  const filterOrdersByStatus = (statusFilter: string) => {
    if (statusFilter.toLowerCase() === 'all') return mockAgentOrders;
    return mockAgentOrders.filter(order => order.status.toLowerCase().replace(/\s+/g, '') === statusFilter.toLowerCase().replace(/\s+/g, ''));
  };

  const tabStatuses: { key: OrderStatusKey, translationKey: keyof typeof t}[] = [
    { key: 'Assigned', translationKey: 'statusAssigned'},
    { key: 'Out for Delivery', translationKey: 'statusOutForDelivery'},
    { key: 'Delivered', translationKey: 'statusDelivered'},
    { key: 'Failed Attempt', translationKey: 'statusFailedAttempt'},
    { key: 'All', translationKey: 'statusAll'},
  ];


  return (
    <AuthenticatedLayout expectedRole="agent">
      <div className="space-y-6 p-1">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <div className="flex items-center space-x-3">
            <Truck className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
              <p className="text-muted-foreground">
                {t('description')}
              </p>
            </div>
          </div>
          <Button variant="outline">
            <ListFilter className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('filterSortOrders')}
          </Button>
        </div>

        <Tabs defaultValue="assigned" className="w-full">
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-5 mb-4">
            {tabStatuses.map(statusInfo => {
              const count = filterOrdersByStatus(statusInfo.key).length;
              const label = t(statusInfo.translationKey);
              return (
                <TabsTrigger 
                  key={statusInfo.key} 
                  value={statusInfo.key.toLowerCase().replace(/\s+/g, '')}
                  className={statusInfo.key === 'All' && count === 0 && tabStatuses.findIndex(ts => ts.key === statusInfo.key) > 2 ? 'hidden md:inline-flex' : ''} 
                >
                  {label} ({count})
                </TabsTrigger>
              )
            })}
          </TabsList>

          {tabStatuses.map(statusInfo => (
            <TabsContent key={statusInfo.key} value={statusInfo.key.toLowerCase().replace(/\s+/g, '')}>
              {filterOrdersByStatus(statusInfo.key).length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <p className="text-muted-foreground text-center">{t('noOrdersWithStatus', {status: t(statusInfo.translationKey) })}</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {filterOrdersByStatus(statusInfo.key).map(order => (
                    <Card key={order.id} className="shadow-sm hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                          <div>
                            <CardTitle className="text-xl">{t('orderTitle', {id: order.id, customerName: order.customerName})}</CardTitle>
                            <CardDescription className="flex items-center mt-1">
                              <MapPin className="mr-1 h-4 w-4 text-muted-foreground flex-shrink-0 rtl:ml-1 rtl:mr-0" /> 
                              <span className="truncate" title={order.address}>{order.address}</span>
                                <Button variant="ghost" size="icon" className="h-6 w-6 ml-1 rtl:mr-1 rtl:ml-0" asChild>
                                    <a href={`https://maps.google.com/?q=${encodeURIComponent(order.address)}`} target="_blank" rel="noopener noreferrer" aria-label={t('openInMaps')}>
                                        <ExternalLink className="h-3 w-3 text-primary" />
                                    </a>
                                </Button>
                            </CardDescription>
                          </div>
                          <Badge variant={getStatusBadgeVariant(order.status) as any} className="text-sm whitespace-nowrap mt-1 sm:mt-0 self-start sm:self-auto">
                            {t(`status${order.status.replace(/\s+/g, '')}` as any, undefined, order.status)}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <p className="text-sm font-medium">{t('itemsCountSlot', {count: order.items, slot: order.deliveryTimeSlot})}</p>
                        {order.notes && <p className="text-xs text-muted-foreground italic">{t('notesLabel', {notes: order.notes})}</p>}
                        <div className="flex flex-wrap gap-2 items-center">
                          <Button variant="outline" size="sm" asChild>
                            <a href={`tel:${order.contact}`}>
                              <Phone className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('callCustomer')}
                            </a>
                          </Button>
                          <Button variant="outline" size="sm" asChild>
                             <Link href={`/agent/communication?order=${order.id}&customer=${order.customerName}`}>
                                <MessageSquare className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('messageCustomer')}
                             </Link>
                          </Button>
                          {order.status !== 'Delivered' && order.status !== 'Failed Attempt' && (
                             <Button size="sm" asChild className="bg-accent hover:bg-accent/90 text-accent-foreground">
                                <Link href={`/agent/update-status?order=${order.id}`}>
                                    {order.status === 'Assigned' ? <Truck className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> : <CheckCircle className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />}
                                    {order.status === 'Assigned' ? t('startDelivery') : t('updateStatus')}
                                </Link>
                            </Button>
                          )}
                           <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-primary">
                                <Edit className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('updateNotes')}
                            </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </AuthenticatedLayout>
  );
}
