"use client";

import { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  MessageSquare, 
  Send, 
  Phone, 
  Mail, 
  Search,
  Clock,
  CheckCircle,
  User,
  Users,
  MessageCircle
} from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';

// Mock data types
interface Message {
  id: string;
  senderId: string;
  senderName: string;
  receiverId: string;
  receiverName: string;
  content: string;
  timestamp: string;
  type: 'text' | 'order_inquiry' | 'complaint' | 'support';
  status: 'sent' | 'delivered' | 'read';
  orderId?: string;
}

interface Contact {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  type: 'customer' | 'employee' | 'supplier';
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount: number;
  isOnline: boolean;
}

interface Conversation {
  contactId: string;
  messages: Message[];
}

export default function EmployeeCommunicationPage() {
  const t = useScopedI18n('employeeCommunication');
  const tCommon = useScopedI18n('common');
  const { user } = useAuth();
  const { toast } = useToast();

  const [contacts, setContacts] = useState<Contact[]>([]);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [isLoading, setIsLoading] = useState(true);

  // Check permissions
  const hasPermission = user?.role === 'employee' && user?.permissions?.canAccessCommunicationTools;

  useEffect(() => {
    if (hasPermission) {
      loadContacts();
      loadConversations();
    }
  }, [hasPermission]);

  const loadContacts = async () => {
    try {
      // Mock data - replace with actual API call
      const mockContacts: Contact[] = [
        {
          id: '1',
          name: 'أحمد محمد',
          email: '<EMAIL>',
          phone: '+967771234567',
          type: 'customer',
          lastMessage: 'متى سيصل طلبي؟',
          lastMessageTime: '2024-01-20T10:30:00Z',
          unreadCount: 2,
          isOnline: true
        },
        {
          id: '2',
          name: 'فاطمة علي',
          email: '<EMAIL>',
          phone: '+967771234568',
          type: 'customer',
          lastMessage: 'شكراً لكم على الخدمة الممتازة',
          lastMessageTime: '2024-01-20T09:15:00Z',
          unreadCount: 0,
          isOnline: false
        },
        {
          id: '3',
          name: 'محمد الموظف',
          email: '<EMAIL>',
          phone: '+967771234569',
          type: 'employee',
          lastMessage: 'هل يمكنك مساعدتي في هذا الطلب؟',
          lastMessageTime: '2024-01-20T08:45:00Z',
          unreadCount: 1,
          isOnline: true
        }
      ];

      setContacts(mockContacts);
    } catch (error) {
      console.error('Error loading contacts:', error);
    }
  };

  const loadConversations = async () => {
    setIsLoading(true);
    try {
      // Mock data - replace with actual API call
      const mockConversations: Conversation[] = [
        {
          contactId: '1',
          messages: [
            {
              id: '1',
              senderId: '1',
              senderName: 'أحمد محمد',
              receiverId: user?.id || '',
              receiverName: user?.name || '',
              content: 'السلام عليكم، أريد الاستفسار عن طلبي',
              timestamp: '2024-01-20T10:00:00Z',
              type: 'order_inquiry',
              status: 'read',
              orderId: 'ORD-001'
            },
            {
              id: '2',
              senderId: user?.id || '',
              senderName: user?.name || '',
              receiverId: '1',
              receiverName: 'أحمد محمد',
              content: 'وعليكم السلام، طلبكم قيد التحضير وسيصل غداً إن شاء الله',
              timestamp: '2024-01-20T10:15:00Z',
              type: 'text',
              status: 'delivered'
            },
            {
              id: '3',
              senderId: '1',
              senderName: 'أحمد محمد',
              receiverId: user?.id || '',
              receiverName: user?.name || '',
              content: 'متى سيصل طلبي؟',
              timestamp: '2024-01-20T10:30:00Z',
              type: 'text',
              status: 'sent'
            }
          ]
        }
      ];

      setConversations(mockConversations);
    } catch (error) {
      console.error('Error loading conversations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const sendMessage = async () => {
    if (!selectedContact || !newMessage.trim()) return;

    try {
      const message: Message = {
        id: Date.now().toString(),
        senderId: user?.id || '',
        senderName: user?.name || '',
        receiverId: selectedContact.id,
        receiverName: selectedContact.name,
        content: newMessage.trim(),
        timestamp: new Date().toISOString(),
        type: 'text',
        status: 'sent'
      };

      // Update conversations
      setConversations(prev => {
        const existingConv = prev.find(c => c.contactId === selectedContact.id);
        if (existingConv) {
          return prev.map(c => 
            c.contactId === selectedContact.id 
              ? { ...c, messages: [...c.messages, message] }
              : c
          );
        } else {
          return [...prev, { contactId: selectedContact.id, messages: [message] }];
        }
      });

      // Update contact's last message
      setContacts(prev => prev.map(c => 
        c.id === selectedContact.id 
          ? { ...c, lastMessage: message.content, lastMessageTime: message.timestamp }
          : c
      ));

      setNewMessage('');

      toast({
        title: 'Message Sent',
        description: `Message sent to ${selectedContact.name}`,
      });

    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive',
      });
    }
  };

  const getFilteredContacts = () => {
    let filtered = contacts;

    if (searchTerm) {
      filtered = filtered.filter(contact =>
        contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.phone?.includes(searchTerm)
      );
    }

    if (activeTab !== 'all') {
      filtered = filtered.filter(contact => contact.type === activeTab);
    }

    return filtered.sort((a, b) => {
      // Sort by unread count first, then by last message time
      if (a.unreadCount !== b.unreadCount) {
        return b.unreadCount - a.unreadCount;
      }
      if (a.lastMessageTime && b.lastMessageTime) {
        return new Date(b.lastMessageTime).getTime() - new Date(a.lastMessageTime).getTime();
      }
      return 0;
    });
  };

  const getCurrentConversation = () => {
    if (!selectedContact) return null;
    return conversations.find(c => c.contactId === selectedContact.id);
  };

  const getContactTypeIcon = (type: string) => {
    switch (type) {
      case 'customer':
        return <User className="h-4 w-4" />;
      case 'employee':
        return <Users className="h-4 w-4" />;
      case 'supplier':
        return <Users className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getContactTypeBadge = (type: string) => {
    const typeConfig = {
      customer: { label: 'عميل', variant: 'default' as const },
      employee: { label: 'موظف', variant: 'secondary' as const },
      supplier: { label: 'مورد', variant: 'outline' as const }
    };

    const config = typeConfig[type as keyof typeof typeConfig] || { 
      label: type, 
      variant: 'outline' as const 
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return format(date, 'HH:mm');
    } else {
      return format(date, 'MMM dd');
    }
  };

  if (!hasPermission) {
    return (
      <AuthenticatedLayout expectedRole="employee">
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-center text-destructive">
                {tCommon('accessDenied')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-muted-foreground">
                You don't have permission to access communication tools.
              </p>
            </CardContent>
          </Card>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <MessageSquare className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]">
          {/* Contacts List */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Contacts
              </CardTitle>
              <div className="space-y-4">
                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search contacts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Contact Type Tabs */}
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="all">All</TabsTrigger>
                    <TabsTrigger value="customer">Customers</TabsTrigger>
                    <TabsTrigger value="employee">Staff</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-[400px] overflow-y-auto">
                {getFilteredContacts().map((contact) => (
                  <div
                    key={contact.id}
                    className={`p-4 border-b cursor-pointer hover:bg-muted/50 transition-colors ${
                      selectedContact?.id === contact.id ? 'bg-muted' : ''
                    }`}
                    onClick={() => setSelectedContact(contact)}
                  >
                    <div className="flex items-start gap-3">
                      <div className="relative">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src="" />
                          <AvatarFallback>
                            {contact.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        {contact.isOnline && (
                          <div className="absolute -bottom-1 -right-1 h-3 w-3 bg-green-500 rounded-full border-2 border-background"></div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm truncate">
                              {contact.name}
                            </span>
                            {getContactTypeIcon(contact.type)}
                          </div>
                          {contact.unreadCount > 0 && (
                            <Badge variant="destructive" className="h-5 w-5 p-0 text-xs">
                              {contact.unreadCount}
                            </Badge>
                          )}
                        </div>
                        {contact.lastMessage && (
                          <p className="text-xs text-muted-foreground truncate mt-1">
                            {contact.lastMessage}
                          </p>
                        )}
                        <div className="flex items-center justify-between mt-2">
                          {getContactTypeBadge(contact.type)}
                          {contact.lastMessageTime && (
                            <span className="text-xs text-muted-foreground">
                              {formatMessageTime(contact.lastMessageTime)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Chat Area */}
          <Card className="lg:col-span-2">
            {selectedContact ? (
              <>
                <CardHeader className="border-b">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src="" />
                        <AvatarFallback>
                          {selectedContact.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-lg">{selectedContact.name}</CardTitle>
                        <div className="flex items-center gap-2">
                          {getContactTypeBadge(selectedContact.type)}
                          <span className={`text-xs ${selectedContact.isOnline ? 'text-green-600' : 'text-muted-foreground'}`}>
                            {selectedContact.isOnline ? 'Online' : 'Offline'}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {selectedContact.phone && (
                        <Button variant="outline" size="sm">
                          <Phone className="h-4 w-4" />
                        </Button>
                      )}
                      {selectedContact.email && (
                        <Button variant="outline" size="sm">
                          <Mail className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-0 flex flex-col h-[400px]">
                  {/* Messages */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-4">
                    {getCurrentConversation()?.messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.senderId === user?.id ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-[70%] rounded-lg p-3 ${
                            message.senderId === user?.id
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-muted'
                          }`}
                        >
                          <p className="text-sm">{message.content}</p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs opacity-70">
                              {formatMessageTime(message.timestamp)}
                            </span>
                            {message.senderId === user?.id && (
                              <div className="flex items-center gap-1">
                                {message.status === 'sent' && <Clock className="h-3 w-3" />}
                                {message.status === 'delivered' && <CheckCircle className="h-3 w-3" />}
                                {message.status === 'read' && <CheckCircle className="h-3 w-3 text-blue-400" />}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Message Input */}
                  <div className="border-t p-4">
                    <div className="flex items-center gap-2">
                      <Textarea
                        placeholder="Type your message..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        className="flex-1 min-h-[40px] max-h-[100px]"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            sendMessage();
                          }
                        }}
                      />
                      <Button 
                        onClick={sendMessage}
                        disabled={!newMessage.trim()}
                        size="sm"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </>
            ) : (
              <CardContent className="flex items-center justify-center h-full">
                <div className="text-center">
                  <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    Select a contact to start messaging
                  </p>
                </div>
              </CardContent>
            )}
          </Card>
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
