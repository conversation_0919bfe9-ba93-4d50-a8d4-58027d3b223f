
"use client";

import React, { useEffect, useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Edit, Trash2, UserPlus, Filter, CalendarDays, Upload, Briefcase, Phone, Shield } from "lucide-react";
import { getAllUsers, deleteUser as deleteUserAPI, User, updateUser as updateUserAPI, createUser as createUserAPI } from '@/lib/auth';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import type { Role, BusinessType } from '@/types';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form";
import { useScopedI18n } from '@/lib/i18n/client';

const roleOptions: Role[] = ['admin', 'owner', 'employee', 'customer', 'wholesaler', 'agent'];
const subscriptionRoles: Role[] = ['owner', 'wholesaler'];
const businessTypeRoles: Role[] = ['owner', 'wholesaler'];
const businessTypeOptions: BusinessType[] = ['grocery', 'pharmacy', 'building_materials', 'electrical_tools', 'supermarket'];

export function UserManagementTable() {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState('');
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const { toast } = useToast();
  const t = useScopedI18n('userManagement');
  const tCommon = useScopedI18n('common');
  const tForm = useScopedI18n('form');

  const userFormSchema = z.object({
    username: z.string().min(3, tForm('usernameMin')).optional().or(z.literal('')), // Username is now optional
    phoneNumber: z.string()
      .min(9, tForm('phoneNumberInvalid'))
      .max(10, tForm('phoneNumberInvalid'))
      .regex(/^[0-9]+$/, tForm('phoneNumberDigitsOnly')),
    email: z.string().email(tForm('emailInvalid')).optional().or(z.literal('')),
    role: z.enum(['admin', 'owner', 'employee', 'customer', 'wholesaler', 'agent']),
    name: z.string().optional(),
    password: z.string().min(6, tForm('passwordMin')).optional(),
    subscriptionEndDate: z.date().optional(),
    businessType: z.enum(['grocery', 'pharmacy', 'building_materials', 'electrical_tools', 'supermarket']).optional(),
  });

  type UserFormValues = z.infer<typeof userFormSchema>;

  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
        username: '',
        phoneNumber: '',
        email: '',
        role: 'customer',
        name: '',
        password: '',
        subscriptionEndDate: undefined,
        businessType: undefined,
    }
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const data = await getAllUsers();
      setUsers(data);
    } catch (error) {
      toast({ title: tCommon('error'), description: t('errorFetchUsers'), variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    form.reset({
      username: user.username || '',
      phoneNumber: user.phoneNumber || '',
      email: user.email || '',
      role: user.role,
      name: user.name || '',
      password: '',
      subscriptionEndDate: user.subscriptionEndDate ? new Date(user.subscriptionEndDate) : undefined,
      businessType: user.businessType || undefined,
    });
    setIsUserModalOpen(true);
  };

  const handleAddUser = () => {
    setEditingUser(null);
    form.reset({
        username: '',
        phoneNumber: '',
        email: '',
        role: 'customer',
        name: '',
        password: '',
        subscriptionEndDate: undefined,
        businessType: undefined,
    });
    setIsUserModalOpen(true);
  };

  const handleDeleteUser = async (userId: string) => {
    if (!window.confirm(t('confirmDeleteUser'))) return;
    try {
      await deleteUserAPI(userId);
      toast({ title: tCommon('success'), description: t('userDeletedSuccess') });
      fetchUsers();
    } catch (error) {
      toast({ title: tCommon('error'), description: (error as Error).message || t('errorDeleteUser'), variant: "destructive" });
    }
  };

  const onUserFormSubmit = async (values: UserFormValues) => {
    try {
      const payload: Partial<UserFormValues> & { id?: string; businessType?: BusinessType; phoneNumber: string } = { 
        ...values,
        phoneNumber: values.phoneNumber, // Ensure phoneNumber is always included
      };
      if (!subscriptionRoles.includes(values.role)) {
        payload.subscriptionEndDate = undefined;
      } else {
        payload.subscriptionEndDate = values.subscriptionEndDate ? values.subscriptionEndDate : undefined;
      }

      if (!businessTypeRoles.includes(values.role)) {
        payload.businessType = undefined;
      } else {
        payload.businessType = values.businessType || undefined;
      }
      
      // Username is now optional, if not provided, use phoneNumber or a generated one
      if (!payload.username) {
        payload.username = values.phoneNumber; // Or generate a unique username
      }


      if (editingUser) {
        if (!values.password) delete payload.password;
        await updateUserAPI({
          ...editingUser,
          ...payload,
          email: payload.email || undefined, 
          subscriptionEndDate: payload.subscriptionEndDate ? payload.subscriptionEndDate.toISOString().split('T')[0] : undefined
        });
        toast({ title: tCommon('success'), description: t('userUpdatedSuccess') });
      } else {
        if (!values.password) {
            toast({ title: tCommon('error'), description: t('passwordRequiredForNew'), variant: "destructive" });
            return;
        }
        if (values.role === 'admin' && users.some(u => u.role === 'admin')) {
             toast({ title: tCommon('error'), description: t('adminRoleExistsError'), variant: "destructive" });
             return;
        }
        await createUserAPI({
          ...(payload as Required<Omit<UserFormValues, "id" | "subscriptionEndDate" | "businessType" | "username">> & 
            { username: string; subscriptionEndDate?: string, businessType?: BusinessType, phoneNumber: string }),
          email: payload.email || undefined,
          username: payload.username, // Ensure username is passed
          subscriptionEndDate: payload.subscriptionEndDate ? payload.subscriptionEndDate.toISOString().split('T')[0] : undefined,
        });
        toast({ title: tCommon('success'), description: t('userCreatedSuccess') });
      }
      setIsUserModalOpen(false);
      fetchUsers();
    } catch (error) {
      toast({ title: tCommon('error'), description: (error as Error).message || t(editingUser ? 'errorUpdateUser' : 'errorCreateUser'), variant: "destructive" });
    }
  };

  const handleExportUsers = () => {
    toast({
      title: t('exportUsersToastTitle'),
      description: t('exportUsersToastDesc'),
    });
    console.log("Exporting users...", filteredUsers);
  };


  const filteredUsers = users.filter(user =>
    (user.username && user.username.toLowerCase().includes(filter.toLowerCase())) ||
    (user.phoneNumber && user.phoneNumber.includes(filter)) || // Filter by phone number
    (user.email && user.email.toLowerCase().includes(filter.toLowerCase())) ||
    user.role.toLowerCase().includes(filter.toLowerCase()) ||
    (user.businessType && t(`businessType_${user.businessType}` as any, undefined, user.businessType).toLowerCase().includes(filter.toLowerCase()))
  );

  const watchedRole = form.watch('role');


  if (isLoading) return <p>{tCommon('loading')}</p>;

  return (
    <>
      <div className="flex items-center justify-between mb-4 gap-2 flex-wrap">
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Filter className="h-5 w-5 text-muted-foreground" />
          <Input
            placeholder={t('filterPlaceholder')}
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <div className="flex gap-2 flex-wrap">
          <Button onClick={handleExportUsers} variant="outline">
            <Upload className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('exportUsersButton')}
          </Button>
          <Button onClick={handleAddUser} className="bg-accent hover:bg-accent/90 text-accent-foreground">
            <UserPlus className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('addUser')}
          </Button>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('phoneNumber')}</TableHead>
              <TableHead>{t('username')}</TableHead>
              <TableHead>{t('email')}</TableHead>
              <TableHead>{t('role')}</TableHead>
              <TableHead>{t('name')}</TableHead>
              <TableHead>{t('businessTypeLabel')}</TableHead>
              <TableHead>{t('subscriptionEndDate')}</TableHead>
              <TableHead className="text-right rtl:text-left">{tCommon('actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">{user.phoneNumber || tCommon('N_A')}</TableCell>
                <TableCell>{user.username || tCommon('N_A')}</TableCell>
                <TableCell>{user.email || tCommon('N_A')}</TableCell>
                <TableCell><Badge variant={user.role === 'admin' ? 'destructive' : 'secondary'} className="capitalize">{t(`role_${user.role}` as any, undefined, user.role)}</Badge></TableCell>
                <TableCell>{user.name || tCommon('N_A')}</TableCell>
                <TableCell>
                  {user.businessType ? (
                    <Badge variant="outline" className="capitalize">
                      <Briefcase className="mr-1 h-3 w-3 rtl:ml-1 rtl:mr-0" />
                      {t(`businessType_${user.businessType}` as any, undefined, user.businessType)}
                    </Badge>
                  ) : tCommon('N_A')}
                </TableCell>
                <TableCell>{user.subscriptionEndDate ? format(new Date(user.subscriptionEndDate), "PPP") : tCommon('N_A')}</TableCell>
                <TableCell className="text-right rtl:text-left">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>{tCommon('actions')}</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleEditUser(user)}>
                        <Edit className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {tCommon('edit')}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleDeleteUser(user.id)} className="text-destructive focus:text-destructive focus:bg-destructive/10">
                        <Trash2 className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {tCommon('delete')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {filteredUsers.length === 0 && !isLoading && (
        <p className="text-center text-muted-foreground mt-4">{tCommon('noResults')}</p>
      )}

      <Dialog open={isUserModalOpen} onOpenChange={setIsUserModalOpen}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>{editingUser ? t('editUser') : t('addNewUser')}</DialogTitle>
            <DialogDescription>
              {editingUser ? t('editUserDesc') : t('addNewUserDesc')}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onUserFormSubmit)} className="space-y-4 py-4 max-h-[70vh] overflow-y-auto pr-2">
              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="phoneNumber">{t('phoneNumber')}</FormLabel>
                    <FormControl>
                      <Input id="phoneNumber" type="tel" {...field} placeholder={tForm('phoneNumberPlaceholder')} />
                    </FormControl>
                    <FormDescription>{tForm('phoneNumberHelpNoPrefix')}</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="username">{t('username')} ({tCommon('optional')})</FormLabel>
                    <FormControl>
                      <Input id="username" {...field} />
                    </FormControl>
                     <FormDescription>{tForm('usernameOptionalHelp')}</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="email">{t('email')} ({tCommon('optional')})</FormLabel>
                    <FormControl>
                      <Input id="email" type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="name">{t('name')} ({tCommon('optional')})</FormLabel>
                    <FormControl>
                      <Input id="name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="password">{t('password')}</FormLabel>
                    <FormControl>
                      <Input id="password" type="password" {...field} placeholder={editingUser ? t('passwordPlaceholderEdit') : t('passwordPlaceholderCreate')}/>
                    </FormControl>
                    {!editingUser && <FormDescription>{t('passwordRequiredForNew')}</FormDescription>}
                    {editingUser && <FormDescription>{t('passwordLeaveBlankDescription')}</FormDescription>}
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                      <FormItem>
                          <FormLabel htmlFor="role">{t('role')}</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                              <SelectTrigger id="role">
                                  <SelectValue placeholder={t('selectRole')} />
                              </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                              {roleOptions.map(roleName => (
                                  <SelectItem
                                    key={roleName}
                                    value={roleName}
                                    disabled={
                                      (!editingUser && roleName === 'admin' && users.some(u => u.role === 'admin')) ||
                                      (editingUser && editingUser.role !== 'admin' && roleName === 'admin' && users.some(u => u.role === 'admin' && u.id !== editingUser.id))
                                    }
                                  >
                                    {t(`role_${roleName}` as any, undefined, roleName)}
                                  </SelectItem>
                              ))}
                              </SelectContent>
                          </Select>
                          <FormMessage />
                      </FormItem>
                  )}
              />
              {businessTypeRoles.includes(watchedRole) && (
                 <FormField
                  control={form.control}
                  name="businessType"
                  render={({ field }) => (
                      <FormItem>
                          <FormLabel htmlFor="businessType">{t('businessTypeLabel')}</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                              <SelectTrigger id="businessType">
                                  <SelectValue placeholder={t('businessTypePlaceholder')} />
                              </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                              {businessTypeOptions.map(type => (
                                  <SelectItem key={type} value={type}>
                                    {t(`businessType_${type}` as any, undefined, type)}
                                  </SelectItem>
                              ))}
                              </SelectContent>
                          </Select>
                          <FormMessage />
                          <FormDescription>{t('businessTypeHelp')}</FormDescription>
                      </FormItem>
                  )}
                />
              )}
              {subscriptionRoles.includes(watchedRole) && (
                <FormField
                  control={form.control}
                  name="subscriptionEndDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>{t('subscriptionEndDate')}</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>{tCommon('pickADate')}</span>
                              )}
                              <CalendarDays className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date("1900-01-01")}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>
                        {t('subscriptionEndDateHelp')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
              <DialogFooter>
                <DialogClose asChild>
                  <Button type="button" variant="outline">{tCommon('cancel')}</Button>
                </DialogClose>
                <Button type="submit" disabled={form.formState.isSubmitting} className="bg-accent hover:bg-accent/90 text-accent-foreground">
                  {form.formState.isSubmitting ? t('saving') : (editingUser ? tCommon('save') : tCommon('add'))}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
