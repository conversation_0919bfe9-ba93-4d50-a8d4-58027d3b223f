"use client"; // Make this a client component

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { NotificationIconWithBadge } from '@/components/ui/notification-badge';
import Link from 'next/link';
import { CheckSquare, CreditCard, Truck, Bell, FileText, ListChecks } from 'lucide-react';
import { useScopedI18n, useI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context'; // Import useAuth
import { useState, useEffect } from 'react';
import { getUnreadNotificationsCount } from '@/lib/mock-notifications-data';

export default function EmployeeDashboardPage() {
  const t = useI18n();
  const tCommon = useScopedI18n('common');
  const { user } = useAuth(); // Get the authenticated user
  const [unreadNotificationsCount, setUnreadNotificationsCount] = useState(0);

  // Load unread notifications count
  useEffect(() => {
    const count = getUnreadNotificationsCount(user?.id);
    setUnreadNotificationsCount(count);
  }, [user?.id]);

  const tasks = [
    { titleKey: "employeeDashboard.taskNewOrders", count: 5, icon: ListChecks, color: "text-blue-500", href: "/employee/approve-orders", permissionKey: "canReceiveOrders" },
    { titleKey: "employeeDashboard.taskOrdersToDispatch", count: 3, icon: Truck, color: "text-orange-500", href: "/employee/dispatch" },
    { titleKey: "employeeDashboard.taskPendingTransactions", count: 2, icon: CreditCard, color: "text-green-500", href: "/employee/transactions" },
  ];

  const quickActionsBase = [
    { labelKey: "nav_recordTransactions", href: "/employee/transactions", icon: CreditCard },
    { labelKey: "nav_approveOrders", href: "/employee/approve-orders", icon: ListChecks, permissionKey: "canReceiveOrders" },
    { labelKey: "nav_dispatchOrders", href: "/employee/dispatch", icon: Truck },
    { labelKey: "nav_notifications", href: "/employee/notifications", icon: Bell, showBadge: true, badgeCount: unreadNotificationsCount },
    { labelKey: "nav_tasks", href: "/employee/tasks", icon: FileText },
  ];

  // Filter quick actions based on permissions
  const quickActions = quickActionsBase.filter(action => {
    if (action.permissionKey) {
      return user?.permissions?.[action.permissionKey as keyof typeof user.permissions] === true;
    }
    return true; // No specific permission needed for this action
  });


  const recentActivities = [
    { textKey: "employeeDashboard.activityItemApprovedOrder", params: {orderId: "12345"} },
    { textKey: "employeeDashboard.activityItemRecordedPayment", params: {orderId: "12344"} },
    { textKey: "employeeDashboard.activityItemDispatchedOrder", params: {orderId: "12342"} },
    { textKey: "employeeDashboard.activityItemNewOrderNotification" },
  ];


  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-6 p-1">
        <h1 className="text-3xl font-bold text-foreground">{t('employeeDashboard.title')}</h1>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {tasks.filter(task => !task.permissionKey || (user?.permissions?.[task.permissionKey as keyof typeof user.permissions] === true)).map((task) => (
            <Card key={task.titleKey} className="shadow-md hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t(task.titleKey as any)}
                </CardTitle>
                <task.icon className={`h-5 w-5 ${task.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{task.count}</div>
                <Button variant="link" asChild className="p-0 h-auto text-xs text-muted-foreground">
                  <Link href={task.href}>{t('employeeDashboard.viewTaskAction')}</Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>{t('employeeDashboard.quickActionsTitle')}</CardTitle>
            <CardDescription>{t('employeeDashboard.quickActionsDesc')}</CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {quickActions.map(action => (
              <Button key={action.labelKey} variant="outline" asChild className="justify-start">
                <Link href={action.href}>
                  {action.showBadge && action.badgeCount > 0 ? (
                    <NotificationIconWithBadge
                      icon={<action.icon className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />}
                      count={action.badgeCount}
                      badgeProps={{ size: 'sm' }}
                    />
                  ) : (
                    <action.icon className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
                  )}
                  {tCommon(action.labelKey as any)}
                </Link>
              </Button>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('employeeDashboard.recentActivityTitle')}</CardTitle>
            <CardDescription>{t('employeeDashboard.recentActivityDesc')}</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm text-muted-foreground">
              {recentActivities.map((activity, index) => (
                 <li key={index}>{t(activity.textKey as any, activity.params)}</li>
              ))}
            </ul>
          </CardContent>
        </Card>

      </div>
    </AuthenticatedLayout>
  );
}