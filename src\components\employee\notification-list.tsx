"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Bell, Inbox, RefreshCw } from 'lucide-react';
import { NotificationItem } from './notification-item';
import { NotificationFilters, type NotificationFilters as FilterType } from './notification-filters';
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import type { Notification } from '@/types';
import {
  getStoredNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  getUnreadNotificationsCount
} from '@/lib/mock-notifications-data';

const defaultFilters: FilterType = {
  search: '',
  type: 'all',
  priority: 'all',
  isRead: 'all',
  sortBy: 'date',
  sortOrder: 'desc'
};

export function NotificationList() {
  const { user } = useAuth();
  const { toast } = useToast();
  const t = useScopedI18n('employeeNotifications');
  
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filters, setFilters] = useState<FilterType>(defaultFilters);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Load notifications
  const loadNotifications = async (showRefreshToast = false) => {
    try {
      setIsRefreshing(true);
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const userNotifications = getStoredNotifications(user?.id);
      setNotifications(userNotifications);
      
      if (showRefreshToast) {
        toast({
          title: t('refreshSuccess'),
          description: t('notificationsUpdated'),
        });
      }
    } catch (error) {
      toast({
        title: t('loadError'),
        description: t('failedToLoadNotifications'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    loadNotifications();
  }, [user?.id]);

  // Filter and sort notifications
  const filteredNotifications = useMemo(() => {
    let filtered = [...notifications];

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(notification =>
        notification.title.toLowerCase().includes(searchLower) ||
        notification.message.toLowerCase().includes(searchLower) ||
        notification.senderName?.toLowerCase().includes(searchLower)
      );
    }

    // Type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(notification => notification.type === filters.type);
    }

    // Priority filter
    if (filters.priority !== 'all') {
      filtered = filtered.filter(notification => notification.priority === filters.priority);
    }

    // Read status filter
    if (filters.isRead !== 'all') {
      const isRead = filters.isRead === 'read';
      filtered = filtered.filter(notification => notification.isRead === isRead);
    }

    // Sort
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (filters.sortBy) {
        case 'date':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'priority':
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
          comparison = priorityOrder[a.priority] - priorityOrder[b.priority];
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
      }

      return filters.sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [notifications, filters]);

  const handleMarkAsRead = (notificationId: string) => {
    markNotificationAsRead(notificationId, user?.id);
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, isRead: true, readAt: new Date().toISOString() }
          : notification
      )
    );
    toast({
      title: t('markedAsRead'),
      description: t('notificationMarkedAsRead'),
    });
  };

  const handleMarkAllAsRead = () => {
    markAllNotificationsAsRead(user?.id);
    setNotifications(prev => 
      prev.map(notification => 
        !notification.isRead 
          ? { ...notification, isRead: true, readAt: new Date().toISOString() }
          : notification
      )
    );
    toast({
      title: t('allMarkedAsRead'),
      description: t('allNotificationsMarkedAsRead'),
    });
  };

  const handleDelete = (notificationId: string) => {
    deleteNotification(notificationId);
    setNotifications(prev => prev.filter(notification => notification.id !== notificationId));
    toast({
      title: t('notificationDeleted'),
      description: t('notificationDeletedSuccess'),
    });
  };

  const handleClearAll = () => {
    // Delete all filtered notifications
    filteredNotifications.forEach(notification => {
      deleteNotification(notification.id);
    });
    
    // Update state to remove deleted notifications
    const deletedIds = new Set(filteredNotifications.map(n => n.id));
    setNotifications(prev => prev.filter(notification => !deletedIds.has(notification.id)));
    
    toast({
      title: t('notificationsCleared'),
      description: t('notificationsClearedSuccess', { count: filteredNotifications.length }),
    });
  };

  const handleRefresh = () => {
    loadNotifications(true);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-24 w-full" />
        <Skeleton className="h-24 w-full" />
        <Skeleton className="h-24 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Bell className="h-6 w-6 text-primary" />
              <div>
                <CardTitle>{t('title')}</CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {t('description')}
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              {t('refresh')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <NotificationFilters
            filters={filters}
            onFiltersChange={setFilters}
            notifications={notifications}
            onMarkAllAsRead={handleMarkAllAsRead}
            onClearAll={handleClearAll}
          />
        </CardContent>
      </Card>

      {/* Notifications List */}
      <div className="space-y-3">
        {filteredNotifications.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Inbox className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold text-muted-foreground mb-2">
                {notifications.length === 0 ? t('noNotifications') : t('noFilteredNotifications')}
              </h3>
              <p className="text-sm text-muted-foreground text-center max-w-md">
                {notifications.length === 0 
                  ? t('noNotificationsMessage')
                  : t('noFilteredNotificationsMessage')
                }
              </p>
              {notifications.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilters(defaultFilters)}
                  className="mt-4"
                >
                  {t('clearFilters')}
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          filteredNotifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onMarkAsRead={handleMarkAsRead}
              onDelete={handleDelete}
            />
          ))
        )}
      </div>

      {/* Load More (for future pagination) */}
      {filteredNotifications.length > 0 && (
        <div className="text-center py-4">
          <p className="text-sm text-muted-foreground">
            {t('showingNotifications', { 
              count: filteredNotifications.length,
              total: notifications.length 
            })}
          </p>
        </div>
      )}
    </div>
  );
}
