
'use server';
/**
 * @fileOverview A Genkit flow for generating or enhancing product images.
 *
 * - generateOrEnhanceProductImage - Generates a new product image or enhances an existing one using AI.
 * - EnhanceProductImageInput - The input type for the flow.
 * - EnhanceProductImageOutput - The output type for the flow.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const EnhanceProductImageInputSchema = z.object({
  productName: z.string().describe('The name of the product.'),
  productCategory: z.string().describe('The category of the product.'),
  dataAiHint: z.string().optional().describe('Optional user-provided hint for image generation (e.g., "on a white background", "lifestyle shot").'),
  baseImageDataUri: z
    .string()
    .optional()
    .describe(
      "Optional. An existing image of the product as a data URI that must include a MIME type and use Base64 encoding. Expected format: 'data:<mimetype>;base64,<encoded_data>'. If provided, the AI will try to enhance or re-imagine this image."
    ),
});
export type EnhanceProductImageInput = z.infer<typeof EnhanceProductImageInputSchema>;

const EnhanceProductImageOutputSchema = z.object({
  generatedImageDataUri: z.string().describe("The generated or enhanced product image as a data URI. Format: 'data:image/png;base64,<encoded_data>'."),
  promptUsed: z.string().describe("The actual prompt text used to generate the image, for debugging or informational purposes."),
});
export type EnhanceProductImageOutput = z.infer<typeof EnhanceProductImageOutputSchema>;

export async function generateOrEnhanceProductImage(input: EnhanceProductImageInput): Promise<EnhanceProductImageOutput> {
  return enhanceProductImageFlow(input);
}

const systemPromptBase = `You are a professional product photographer and AI image generator. Your goal is to create high-quality, clean, and appealing product images suitable for an e-commerce store.
Focus on clear lighting, good composition, and accurately representing the product based on the details provided.`;

const promptTemplate = ai.definePrompt<{
  productName: string;
  productCategory: string;
  dataAiHint?: string;
  baseImageDataUri?: string;
  finalPromptText: string; // This will be constructed in the flow
}>({
  name: 'enhanceProductImagePrompt',
  // We don't define input schema here as the final prompt text and media usage are dynamic
  // The flow will prepare the final prompt parts.
  // Output schema is for the overall flow, not this specific prompt object structure
  // as we are directly calling ai.generate.
});


const enhanceProductImageFlow = ai.defineFlow(
  {
    name: 'enhanceProductImageFlow',
    inputSchema: EnhanceProductImageInputSchema,
    outputSchema: EnhanceProductImageOutputSchema,
  },
  async (input: EnhanceProductImageInput) => {
    let constructedPromptText = systemPromptBase + "\n\n";
    const promptParts: (string | { media: { url: string } })[] = [];

    if (input.baseImageDataUri) {
      constructedPromptText += `Re-imagine and generate an improved, high-quality product image based on the provided image.`;
      promptParts.push({ media: { url: input.baseImageDataUri } });
    } else {
      constructedPromptText += `Generate a new, high-quality product image.`;
    }

    constructedPromptText += `\nProduct Name: "${input.productName}"`;
    constructedPromptText += `\nCategory: "${input.productCategory}"`;
    if (input.dataAiHint) {
      constructedPromptText += `\nAdditional guidance from user: "${input.dataAiHint}"`;
    } else {
      constructedPromptText += `\nEnsure the product is clearly visible and well-lit. Consider a neutral or complementary background.`;
    }
    constructedPromptText += `\n\nThe output must be only the image.`; // Gemini might sometimes add text if not explicitly told.

    promptParts.push({text: constructedPromptText});
    
    const {media, finishReason, usage, error} = await ai.generate({
      model: 'googleai/gemini-2.0-flash-exp',
      prompt: promptParts as any, // Cast as any because the type for prompt array can be complex
      config: {
        responseModalities: ['TEXT', 'IMAGE'],
        // Safety settings can be adjusted if needed
        // safetySettings: [{ category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' }],
      },
    });

    if (error || finishReason !== 'stop' || !media?.url) {
        console.error("AI image generation failed:", {error, finishReason, media});
        throw new Error(`Failed to generate image. Reason: ${finishReason || 'Unknown error'}. ${error ? `Details: ${error.message || error}` : '' }`);
    }
    
    return {
      generatedImageDataUri: media.url,
      promptUsed: constructedPromptText, 
    };
  }
);

