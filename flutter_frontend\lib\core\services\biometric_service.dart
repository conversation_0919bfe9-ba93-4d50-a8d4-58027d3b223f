import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'dart:typed_data';

class BiometricService {
  static final LocalAuthentication _localAuth = LocalAuthentication();
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _userIdKey = 'biometric_user_id';
  static const String _biometricHashKey = 'biometric_hash';

  /// Check if biometric authentication is available on the device
  static Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await _localAuth.isDeviceSupported();
      if (!isAvailable) return false;

      final bool canCheckBiometrics = await _localAuth.canCheckBiometrics;
      if (!canCheckBiometrics) return false;

      final List<BiometricType> availableBiometrics = 
          await _localAuth.getAvailableBiometrics();
      
      return availableBiometrics.isNotEmpty;
    } catch (e) {
      print('Error checking biometric availability: $e');
      return false;
    }
  }

  /// Get available biometric types
  static Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      print('Error getting available biometrics: $e');
      return [];
    }
  }

  /// Get biometric type name in Arabic
  static String getBiometricTypeName(BiometricType type) {
    switch (type) {
      case BiometricType.face:
        return 'التعرف على الوجه';
      case BiometricType.fingerprint:
        return 'بصمة الإصبع';
      case BiometricType.iris:
        return 'بصمة العين';
      case BiometricType.weak:
        return 'مصادقة ضعيفة';
      case BiometricType.strong:
        return 'مصادقة قوية';
      default:
        return 'مصادقة بيومترية';
    }
  }

  /// Check if biometric authentication is enabled for the current user
  static Future<bool> isBiometricEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_biometricEnabledKey) ?? false;
    } catch (e) {
      print('Error checking biometric enabled status: $e');
      return false;
    }
  }

  /// Enable biometric authentication for a user
  static Future<bool> enableBiometric(String userId, String username) async {
    try {
      // First check if biometric is available
      final bool isAvailable = await isBiometricAvailable();
      if (!isAvailable) {
        throw Exception('البصمة غير متوفرة على هذا الجهاز');
      }

      // Authenticate to enable biometric
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'قم بتأكيد هويتك لتفعيل البصمة',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (!didAuthenticate) {
        return false;
      }

      // Generate a hash for the user credentials
      final String credentialHash = _generateCredentialHash(userId, username);

      // Save biometric settings
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_biometricEnabledKey, true);
      await prefs.setString(_userIdKey, userId);
      await prefs.setString(_biometricHashKey, credentialHash);

      return true;
    } catch (e) {
      print('Error enabling biometric: $e');
      return false;
    }
  }

  /// Disable biometric authentication
  static Future<bool> disableBiometric() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_biometricEnabledKey);
      await prefs.remove(_userIdKey);
      await prefs.remove(_biometricHashKey);
      return true;
    } catch (e) {
      print('Error disabling biometric: $e');
      return false;
    }
  }

  /// Authenticate using biometric
  static Future<String?> authenticateWithBiometric() async {
    try {
      // Check if biometric is enabled
      final bool isEnabled = await isBiometricEnabled();
      if (!isEnabled) {
        throw Exception('البصمة غير مفعلة');
      }

      // Check if biometric is available
      final bool isAvailable = await isBiometricAvailable();
      if (!isAvailable) {
        throw Exception('البصمة غير متوفرة');
      }

      // Get available biometric types for localized reason
      final List<BiometricType> availableBiometrics = await getAvailableBiometrics();
      String localizedReason = 'استخدم البصمة لتسجيل الدخول';
      
      if (availableBiometrics.contains(BiometricType.face)) {
        localizedReason = 'استخدم التعرف على الوجه لتسجيل الدخول';
      } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
        localizedReason = 'استخدم بصمة الإصبع لتسجيل الدخول';
      }

      // Authenticate
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: localizedReason,
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (!didAuthenticate) {
        return null;
      }

      // Return the stored user ID
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userIdKey);
    } catch (e) {
      print('Error authenticating with biometric: $e');
      return null;
    }
  }

  /// Get stored user ID for biometric authentication
  static Future<String?> getBiometricUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userIdKey);
    } catch (e) {
      print('Error getting biometric user ID: $e');
      return null;
    }
  }

  /// Verify if the stored credentials match the provided ones
  static Future<bool> verifyStoredCredentials(String userId, String username) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? storedHash = prefs.getString(_biometricHashKey);
      final String? storedUserId = prefs.getString(_userIdKey);

      if (storedHash == null || storedUserId == null) {
        return false;
      }

      if (storedUserId != userId) {
        return false;
      }

      final String currentHash = _generateCredentialHash(userId, username);
      return storedHash == currentHash;
    } catch (e) {
      print('Error verifying stored credentials: $e');
      return false;
    }
  }

  /// Generate a hash for user credentials
  static String _generateCredentialHash(String userId, String username) {
    final String combined = '$userId:$username';
    final List<int> bytes = utf8.encode(combined);
    final Digest digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Get biometric authentication status info
  static Future<Map<String, dynamic>> getBiometricStatus() async {
    try {
      final bool isAvailable = await isBiometricAvailable();
      final bool isEnabled = await isBiometricEnabled();
      final List<BiometricType> availableTypes = await getAvailableBiometrics();
      final String? userId = await getBiometricUserId();

      return {
        'isAvailable': isAvailable,
        'isEnabled': isEnabled,
        'availableTypes': availableTypes.map((type) => type.toString()).toList(),
        'availableTypeNames': availableTypes.map((type) => getBiometricTypeName(type)).toList(),
        'userId': userId,
        'hasStoredCredentials': userId != null,
      };
    } catch (e) {
      print('Error getting biometric status: $e');
      return {
        'isAvailable': false,
        'isEnabled': false,
        'availableTypes': [],
        'availableTypeNames': [],
        'userId': null,
        'hasStoredCredentials': false,
      };
    }
  }

  /// Cancel any ongoing authentication
  static Future<void> cancelAuthentication() async {
    try {
      await _localAuth.stopAuthentication();
    } catch (e) {
      print('Error canceling authentication: $e');
    }
  }

  /// Check if device supports strong biometric authentication
  static Future<bool> supportsStrongBiometric() async {
    try {
      final List<BiometricType> availableBiometrics = await getAvailableBiometrics();
      return availableBiometrics.contains(BiometricType.strong) ||
             availableBiometrics.contains(BiometricType.fingerprint) ||
             availableBiometrics.contains(BiometricType.face);
    } catch (e) {
      print('Error checking strong biometric support: $e');
      return false;
    }
  }

  /// Get localized error message
  static String getLocalizedErrorMessage(String error) {
    if (error.contains('UserCancel')) {
      return 'تم إلغاء المصادقة من قبل المستخدم';
    } else if (error.contains('NotAvailable')) {
      return 'البصمة غير متوفرة على هذا الجهاز';
    } else if (error.contains('NotEnrolled')) {
      return 'لم يتم تسجيل أي بصمة على هذا الجهاز';
    } else if (error.contains('LockedOut')) {
      return 'تم قفل البصمة مؤقتاً بسبب المحاولات الفاشلة';
    } else if (error.contains('PermanentlyLockedOut')) {
      return 'تم قفل البصمة نهائياً';
    } else {
      return 'حدث خطأ في المصادقة البيومترية';
    }
  }
}
