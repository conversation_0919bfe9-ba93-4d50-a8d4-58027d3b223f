
"use client";

import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { DatabaseBackup, Loader2, CheckCircle, RotateCcw, Download, Upload, Calendar, Clock, HardDrive, Shield } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';
import { getMockProducts } from '@/lib/mock-product-data';
import { BackupScheduler } from '@/components/admin/backup-scheduler';
import { BackupHistory } from '@/components/admin/backup-history';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const DEBTS_STORAGE_KEY = 'marketSyncOwnerDebts'; 
const LAST_BACKUP_TIMESTAMP_KEY = 'marketSyncLastBackupTimestamp';
const LAST_BACKUP_SUMMARY_KEY = 'marketSyncLastBackupSummary';

export default function BackupManagementPage() {
  const t = useScopedI18n('backupManagement');
  const tCommon = useScopedI18n('common');
  const { toast } = useToast();
  const [isLoadingBackup, setIsLoadingBackup] = useState(false);
  const [isLoadingRestore, setIsLoadingRestore] = useState(false);
  const [lastBackup, setLastBackup] = useState<string | null>(null);
  const [backupSummary, setBackupSummary] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedTimestamp = localStorage.getItem(LAST_BACKUP_TIMESTAMP_KEY);
      if (storedTimestamp) {
        setLastBackup(new Date(storedTimestamp).toLocaleString());
      }
      const storedSummary = localStorage.getItem(LAST_BACKUP_SUMMARY_KEY);
      if (storedSummary) {
        setBackupSummary(storedSummary);
      }
    }
  }, []);

  const handleCreateBackup = () => {
    if (typeof window === 'undefined') {
      toast({
        title: tCommon('error'),
        description: "Cannot perform backup outside browser environment.",
        variant: 'destructive',
      });
      return;
    }

    setIsLoadingBackup(true);
    toast({ title: t('backupInProgress') });

    setTimeout(() => {
      try {
        // Simulate fetching data
        const allProducts = getMockProducts();
        const ownerProducts = allProducts.filter(p => p.ownerId);
        const wholesalerProducts = allProducts.filter(p => p.wholesalerId);
        
        let ownerDebtsCount = 0;
        const storedDebts = localStorage.getItem(DEBTS_STORAGE_KEY);
        if (storedDebts) {
            const allDebts = JSON.parse(storedDebts);
            ownerDebtsCount = allDebts.filter((d: any) => d.ownerId).length;
        }

        const totalProductsBackedUp = ownerProducts.length + wholesalerProducts.length;
        
        const currentTimestamp = new Date();
        localStorage.setItem(LAST_BACKUP_TIMESTAMP_KEY, currentTimestamp.toISOString());
        
        const summaryMsg = t('backupSummary', {productCount: totalProductsBackedUp.toString(), debtCount: ownerDebtsCount.toString() });
        localStorage.setItem(LAST_BACKUP_SUMMARY_KEY, summaryMsg);

        setLastBackup(currentTimestamp.toLocaleString());
        setBackupSummary(summaryMsg);

        toast({
          title: tCommon('success'),
          description: t('backupSuccessful', { timestamp: currentTimestamp.toLocaleString() }),
          action: <CheckCircle className="h-5 w-5 text-green-500" />,
        });
      } catch (error) {
        console.error("Backup error:", error);
        toast({
          title: tCommon('error'),
          description: t('backupFailed'),
          variant: 'destructive',
        });
      } finally {
        setIsLoadingBackup(false);
      }
    }, 1500); 
  };

  const handleRestoreBackup = () => {
     if (typeof window === 'undefined') {
      toast({
        title: tCommon('error'),
        description: "Cannot perform restore outside browser environment.",
        variant: 'destructive',
      });
      return;
    }
    setIsLoadingRestore(true);
    toast({ title: t('restoreInProgress')});

    setTimeout(() => {
        const storedTimestamp = localStorage.getItem(LAST_BACKUP_TIMESTAMP_KEY);
        if (storedTimestamp) {
            // Simulate restore logic - e.g., re-initializing some mock data or just showing success
            toast({
                title: t('restoreSuccessfulTitle'),
                description: t('restoreSuccessfulDesc', { timestamp: new Date(storedTimestamp).toLocaleString() }),
                action: <CheckCircle className="h-5 w-5 text-green-500" />,
            });
        } else {
             toast({
                title: t('restoreFailedTitle'),
                description: t('noBackupToRestore'),
                variant: 'destructive',
            });
        }
        setIsLoadingRestore(false);
    }, 1500);
  }

  return (
    <AuthenticatedLayout expectedRole="admin">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <DatabaseBackup className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        {/* Backup Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="p-4 rounded-lg border bg-card">
            <div className="flex items-center space-x-2">
              <DatabaseBackup className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">إجمالي النسخ</p>
                <p className="text-2xl font-bold">24</p>
              </div>
            </div>
          </div>
          <div className="p-4 rounded-lg border bg-card">
            <div className="flex items-center space-x-2">
              <HardDrive className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">حجم البيانات</p>
                <p className="text-2xl font-bold">2.4 GB</p>
              </div>
            </div>
          </div>
          <div className="p-4 rounded-lg border bg-card">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">آخر نسخة</p>
                <p className="text-2xl font-bold">اليوم</p>
              </div>
            </div>
          </div>
          <div className="p-4 rounded-lg border bg-card">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">حالة النظام</p>
                <p className="text-2xl font-bold text-green-600">آمن</p>
              </div>
            </div>
          </div>
        </div>

        {/* Backup Management Tabs */}
        <Tabs defaultValue="manual" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="manual" className="flex items-center space-x-2">
              <DatabaseBackup className="h-4 w-4" />
              <span>{t('manualBackup')}</span>
            </TabsTrigger>
            <TabsTrigger value="scheduler" className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>{t('scheduler')}</span>
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>{t('history')}</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="manual" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="shadow-lg border-0">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-2">
                    <DatabaseBackup className="h-6 w-6 text-primary" />
                    <CardTitle className="text-lg">{t('createBackup')}</CardTitle>
                  </div>
                  <CardDescription>
                    {t('backupDataFor')}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="p-4 rounded-lg border bg-muted/30">
                    <div className="flex items-center justify-between mb-3">
                      <span className="font-medium">حالة النسخ الاحتياطي</span>
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    </div>
                    <p className="text-sm text-muted-foreground">النظام جاهز لإنشاء نسخة احتياطية جديدة</p>
                  </div>

                  <Button
                    onClick={handleCreateBackup}
                    disabled={isLoadingBackup}
                    className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
                    size="lg"
                  >
                    {isLoadingBackup ? (
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    ) : (
                        <DatabaseBackup className="mr-2 h-5 w-5" />
                    )}
                    {isLoadingBackup ? t('backupInProgress') : t('backupNow')}
                  </Button>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 rounded-lg border">
                      <span className="text-sm font-medium">{t('lastBackupTime')}</span>
                      <span className="text-sm text-primary">
                        {lastBackup || t('noBackupPerformed')}
                      </span>
                    </div>
                    {backupSummary && (
                      <div className="p-3 rounded-lg border bg-blue-50 dark:bg-blue-950">
                        <p className="text-sm text-blue-700 dark:text-blue-300">{backupSummary}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                  <CardHeader className="pb-4">
                    <div className="flex items-center space-x-2">
                      <RotateCcw className="h-6 w-6 text-primary" />
                      <CardTitle className="text-lg">{t('restoreOperationsTitle')}</CardTitle>
                    </div>
                      <CardDescription>{t('restoreOperationsDesc')}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="p-4 rounded-lg border bg-yellow-50 dark:bg-yellow-950">
                      <div className="flex items-center space-x-2 mb-2">
                        <Shield className="h-5 w-5 text-yellow-600" />
                        <span className="font-medium text-yellow-800 dark:text-yellow-200">تحذير مهم</span>
                      </div>
                      <p className="text-sm text-yellow-700 dark:text-yellow-300">
                        استعادة النسخة الاحتياطية ستحل محل البيانات الحالية
                      </p>
                    </div>

                    <Button
                      onClick={handleRestoreBackup}
                      disabled={isLoadingRestore || !lastBackup}
                      variant="outline"
                      className="w-full"
                      size="lg"
                    >
                        {isLoadingRestore ? (
                            <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        ) : (
                            <RotateCcw className="mr-2 h-5 w-5" />
                        )}
                        {isLoadingRestore ? t('restoreInProgress') : t('restoreButton')}
                    </Button>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between p-3 rounded-lg border">
                        <span className="text-sm font-medium">النسخة المتاحة</span>
                        <span className="text-sm text-muted-foreground">
                          {lastBackup ? 'متوفرة' : 'غير متوفرة'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between p-3 rounded-lg border">
                        <span className="text-sm font-medium">حجم النسخة</span>
                        <span className="text-sm text-muted-foreground">2.4 GB</span>
                      </div>
                    </div>
                  </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="scheduler">
            <BackupScheduler />
          </TabsContent>

          <TabsContent value="history">
            <BackupHistory />
          </TabsContent>
        </Tabs>

      </div>
    </AuthenticatedLayout>
  );
}

