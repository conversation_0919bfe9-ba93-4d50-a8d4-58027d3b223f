
"use client"; 

import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ShoppingCart, PackageSearch, Truck, Info, Printer, Filter } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import type { Order } from '@/types'; 
import { useToast } from '@/hooks/use-toast';
import { useScopedI18n } from '@/lib/i18n/client';
import { getMockOrders } from '@/lib/mock-order-data';


export default function OwnerPurchasesPage() {
  const { toast } = useToast();
  const t = useScopedI18n('ownerPurchases'); 
  const tCommon = useScopedI18n('common');
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(true);
    // Simulate fetching orders for the current owner.
    // In a real app, you'd filter by ownerId if orders are global.
    // For this mock, we'll assume getMockOrders() returns relevant orders.
    setOrders(getMockOrders());
    setIsLoading(false);
  }, []);


  const handlePrintInvoice = (order: Order) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      let invoiceHTML = '<html><head><title>' + t('invoiceTitle') + '</title>';
      invoiceHTML += '<style>';
      invoiceHTML += `
        body { font-family: Arial, sans-serif; margin: 20px; direction: ${tCommon('language') === 'Arabic' ? 'rtl' : 'ltr'}; } /* Basic LTR/RTL from common */
        .invoice-box { max-width: 800px; margin: auto; padding: 30px; border: 1px solid #eee; box-shadow: 0 0 10px rgba(0, 0, 0, 0.15); font-size: 16px; line-height: 24px; }
        .invoice-header { text-align: center; margin-bottom: 20px; }
        .invoice-header h1 { margin: 0; font-size: 2em; color: hsl(var(--primary)); }
        .item-table { width: 100%; line-height: inherit; text-align: ${tCommon('language') === 'Arabic' ? 'right' : 'left'}; border-collapse: collapse; margin-top: 20px; }
        .item-table td, .item-table th { padding: 8px; border-bottom: 1px solid #eee; }
        .item-table tr:last-child td { border-bottom: none; }
        .item-table th { background: hsl(var(--muted)); font-weight: bold; color: hsl(var(--foreground)); }
        .total-section { text-align: ${tCommon('language') === 'Arabic' ? 'left' : 'right'}; margin-top: 20px; font-size: 1.2em; font-weight: bold; }
      `;
      invoiceHTML += '</style></head><body>';
      invoiceHTML += `<div class="invoice-box">`;
      invoiceHTML += `<div class="invoice-header"><h1>${t('invoiceTitle')}</h1></div>`;
      invoiceHTML += `<h2>${t('orderIdLabel')}: ${order.id}</h2>`;
      invoiceHTML += `<p>${t('dateLabel')}: ${new Date(order.createdAt).toLocaleDateString()}</p>`;
      invoiceHTML += `<p>${t('customerLabel')}: ${order.customerName || order.customerId}</p>`;
      if(order.customerAddress) invoiceHTML += `<p>${t('addressLabel')}: ${order.customerAddress}</p>`;
      invoiceHTML += `<h3>${t('itemsLabel')}:</h3>`;
      invoiceHTML += '<table class="item-table"><thead><tr>';
      invoiceHTML += `<th>${t('productHeader')}</th><th>${t('quantityHeader')}</th><th>${t('priceHeader')}</th><th>${t('totalHeader')}</th>`;
      invoiceHTML += '</tr></thead><tbody>';
      order.items.forEach(item => {
        invoiceHTML += `<tr><td>${item.productName}</td><td>${item.quantity}</td><td>YER ${item.price.toFixed(2)}</td><td>YER ${(item.quantity * item.price).toFixed(2)}</td></tr>`;
      });
      invoiceHTML += '</tbody></table>';
      invoiceHTML += `<div class="total-section"><h3>${t('totalAmountLabel')}: YER ${order.totalAmount.toFixed(2)}</h3></div>`;
      invoiceHTML += `</div>`;
      invoiceHTML += '</body></html>';
      printWindow.document.write(invoiceHTML);
      printWindow.document.close();
      printWindow.print();
    } else {
      toast({
        title: tCommon('error'),
        description: t('printError'),
        variant: "destructive",
      });
    }
  };

  const getStatusBadgeVariant = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'processing': return 'default'; 
      case 'shipped': return 'outline'; 
      case 'delivered': return 'default'; 
      case 'cancelled': return 'destructive';
      default: return 'secondary';
    }
  };
  
  const filterOrdersByStatus = (statusFilter: Order['status'] | 'all' | 'active') => {
    if (statusFilter === 'all') return orders;
    if (statusFilter === 'active') return orders.filter(o => o.status === 'pending' || o.status === 'processing');
    return orders.filter(o => o.status === statusFilter);
  }

  const TABS_CONFIG = [
    { value: 'active', labelKey: 'tabActive' }, 
    { value: 'pending', labelKey: 'tabPending' },
    { value: 'processing', labelKey: 'tabProcessing' },
    { value: 'shipped', labelKey: 'tabShipped' },
    { value: 'delivered', labelKey: 'tabDelivered' },
    { value: 'cancelled', labelKey: 'tabCancelled' },
    { value: 'all', labelKey: 'tabAll' },
  ];
  
  if (isLoading) {
    return (
      <AuthenticatedLayout expectedRole="owner">
        <div className="p-4 text-center text-muted-foreground">{tCommon('loading')}</div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-center space-x-3">
            <ShoppingCart className="h-8 w-8 text-primary" />
            <div>
                <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
                <p className="text-muted-foreground">
                {t('description')}
                </p>
            </div>
            </div>
            <Button variant="outline">
                <Filter className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('filterOrdersButton')}
            </Button>
        </div>

        <Tabs defaultValue="active" className="w-full">
          <TabsList className="grid w-full grid-cols-3 md:grid-cols-4 lg:grid-cols-7 mb-4">
            {TABS_CONFIG.map(tab => (
              <TabsTrigger key={tab.value} value={tab.value}>
                {t(tab.labelKey as any)} ({filterOrdersByStatus(tab.value as Order['status'] | 'all' | 'active').length})
              </TabsTrigger>
            ))}
          </TabsList>

          {TABS_CONFIG.map(tab => (
            <TabsContent key={tab.value} value={tab.value}>
              {filterOrdersByStatus(tab.value as Order['status'] | 'all' | 'active').length === 0 ? (
                 <Card className="shadow-md">
                    <CardHeader>
                        <CardTitle>{t('noOrdersFoundTitle')}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-muted-foreground">{t('noOrdersInStatus', {status: t(tab.labelKey as any)})}</p>
                    </CardContent>
                 </Card>
              ) : (
                <div className="space-y-4">
                  {filterOrdersByStatus(tab.value as Order['status'] | 'all' | 'active').map((order) => (
                    <Card key={order.id} className="shadow-sm hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                          <div>
                            <CardTitle className="text-xl hover:underline">
                                <Link href={`/owner/purchases/${order.id}`}>{order.id}</Link>
                            </CardTitle>
                            <CardDescription>
                              {t('customerLabel')}: {order.customerName || order.customerId} | {t('dateLabel')}: {new Date(order.createdAt).toLocaleDateString()}
                            </CardDescription>
                          </div>
                          <Badge variant={getStatusBadgeVariant(order.status)} className="text-sm whitespace-nowrap mt-1 sm:mt-0 self-start sm:self-auto capitalize">
                            {t(`status_${order.status}` as any, undefined, order.status)}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <p className="text-sm font-medium">
                          {t('itemsCountLabel', {count: order.items.reduce((sum, item) => sum + item.quantity, 0)})} | {t('totalLabel')}: YER {order.totalAmount.toFixed(2)}
                        </p>
                        <div className="text-xs text-muted-foreground">
                           {order.items.slice(0,2).map(i => `${i.productName} (x${i.quantity})`).join(', ')}
                           {order.items.length > 2 && t('andMoreItems', {count: order.items.length - 2})}
                        </div>
                         {order.approvedByEmployeeName && (
                          <p className="text-xs text-muted-foreground">
                            {t('approvedByLabel', { name: order.approvedByEmployeeName, time: order.approvedAt ? format(new Date(order.approvedAt), 'Pp') : '' })}
                          </p>
                        )}
                        <div className="flex flex-wrap gap-2 pt-2">
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/owner/purchases/${order.id}`}>
                              <PackageSearch className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('viewDetailsButton')}
                            </Link>
                          </Button>
                          {order.status === 'pending' && (
                            <Button size="sm" className="bg-accent hover:bg-accent/90 text-accent-foreground">
                              <Info className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('processOrderButton')}
                            </Button>
                          )}
                          {order.status === 'processing' && (
                            <Button size="sm" variant="secondary">
                              <Truck className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('markAsShippedButton')}
                            </Button>
                          )}
                           <Button variant="outline" size="sm" onClick={() => handlePrintInvoice(order)}>
                            <Printer className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('printInvoiceButton')}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </AuthenticatedLayout>
  );
}

