"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Shield, 
  Search, 
  Filter, 
  Save, 
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Lock,
  Unlock,
  Users,
  Eye,
  Edit,
  Plus,
  Trash2
} from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';
import type { Role } from '@/types';

interface Permission {
  feature: string;
  view: boolean;
  edit: boolean;
  create: boolean;
  delete: boolean;
}

interface RolePermission {
  role: Role;
  permissions: Permission[];
}

const initialRolePermissionsData: RolePermission[] = [
  {
    role: 'admin',
    permissions: [
      { feature: 'إدارة المستخدمين', view: true, edit: true, create: true, delete: true },
      { feature: 'التحكم في الاشتراكات', view: true, edit: true, create: true, delete: true },
      { feature: 'سجلات النظام', view: true, edit: false, create: false, delete: false },
      { feature: 'التحكم في الوصول', view: true, edit: true, create: true, delete: true },
      { feature: 'إدارة النسخ الاحتياطي', view: true, edit: true, create: true, delete: true },
    ],
  },
  {
    role: 'owner',
    permissions: [
      { feature: 'إدارة المنتجات', view: true, edit: true, create: true, delete: true },
      { feature: 'إدارة الموظفين', view: true, edit: true, create: true, delete: true },
      { feature: 'تقارير المبيعات', view: true, edit: false, create: false, delete: false },
      { feature: 'توقعات المخزون', view: true, edit: false, create: false, delete: false },
    ],
  },
  {
    role: 'employee',
    permissions: [
      { feature: 'تسجيل المعاملات', view: true, edit: true, create: true, delete: false },
      { feature: 'الموافقة على الطلبات', view: true, edit: true, create: false, delete: false },
      { feature: 'إرسال الطلبات', view: true, edit: true, create: false, delete: false },
    ],
  },
  {
    role: 'customer',
    permissions: [
      { feature: 'تصفح المنتجات', view: true, edit: false, create: false, delete: false },
      { feature: 'عرض الطلبات', view: true, edit: false, create: false, delete: false },
    ],
  },
];

export function EnhancedPermissionMatrix() {
  const [rolePermissions, setRolePermissions] = useState<RolePermission[]>(initialRolePermissionsData);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<Role | 'all'>('all');
  const [hasChanges, setHasChanges] = useState(false);
  const t = useScopedI18n('accessControl');
  const tCommon = useScopedI18n('common');
  const { toast } = useToast();

  const handlePermissionChange = (roleIndex: number, permissionIndex: number, field: keyof Permission, value: boolean) => {
    const newRolePermissions = [...rolePermissions];
    if (field !== 'feature') {
      newRolePermissions[roleIndex].permissions[permissionIndex][field] = value;
      setRolePermissions(newRolePermissions);
      setHasChanges(true);
    }
  };

  const handleSaveChanges = () => {
    toast({
      title: 'تم حفظ التغييرات',
      description: 'تم حفظ إعدادات الصلاحيات بنجاح',
    });
    setHasChanges(false);
  };

  const handleResetChanges = () => {
    setRolePermissions(initialRolePermissionsData);
    setHasChanges(false);
    toast({
      title: 'تم إعادة تعيين التغييرات',
      description: 'تم إعادة تعيين جميع الصلاحيات إلى القيم الافتراضية',
    });
  };

  const filteredRolePermissions = rolePermissions.filter(rp => {
    const matchesRole = selectedRole === 'all' || rp.role === selectedRole;
    const matchesSearch = searchTerm === '' || 
      rp.permissions.some(p => p.feature.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesRole && matchesSearch;
  });

  const getRoleColor = (role: Role) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'owner': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'employee': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'customer': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getRoleIcon = (role: Role) => {
    switch (role) {
      case 'admin': return Shield;
      case 'owner': return Users;
      case 'employee': return Users;
      case 'customer': return Users;
      default: return Users;
    }
  };

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="p-4 rounded-lg border bg-card">
          <div className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-red-500" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">المسؤولين</p>
              <p className="text-2xl font-bold">1</p>
            </div>
          </div>
        </div>
        <div className="p-4 rounded-lg border bg-card">
          <div className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-blue-500" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">الأدوار النشطة</p>
              <p className="text-2xl font-bold">{rolePermissions.length}</p>
            </div>
          </div>
        </div>
        <div className="p-4 rounded-lg border bg-card">
          <div className="flex items-center space-x-2">
            <Lock className="h-5 w-5 text-yellow-500" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">الصلاحيات المحظورة</p>
              <p className="text-2xl font-bold">12</p>
            </div>
          </div>
        </div>
        <div className="p-4 rounded-lg border bg-card">
          <div className="flex items-center space-x-2">
            <Unlock className="h-5 w-5 text-green-500" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">الصلاحيات المفعلة</p>
              <p className="text-2xl font-bold">28</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="flex items-center justify-between gap-4 flex-wrap">
        <div className="flex items-center space-x-2 rtl:space-x-reverse flex-1 max-w-md">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="البحث في الصلاحيات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant={selectedRole === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedRole('all')}
          >
            جميع الأدوار
          </Button>
          {rolePermissions.map((rp) => {
            const RoleIcon = getRoleIcon(rp.role);
            return (
              <Button
                key={rp.role}
                variant={selectedRole === rp.role ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedRole(rp.role)}
                className="flex items-center space-x-1"
              >
                <RoleIcon className="h-4 w-4" />
                <span className="capitalize">{rp.role}</span>
              </Button>
            );
          })}
        </div>
      </div>

      {/* Permission Matrix */}
      <Card className="shadow-lg border-0">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Shield className="h-6 w-6 text-primary" />
              <div>
                <CardTitle className="text-xl">مصفوفة الصلاحيات</CardTitle>
                <CardDescription>إدارة صلاحيات المستخدمين حسب الأدوار</CardDescription>
              </div>
            </div>
            {hasChanges && (
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <span className="text-sm text-yellow-600">يوجد تغييرات غير محفوظة</span>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border shadow-sm bg-card overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="border-b bg-muted/50">
                  <TableHead className="font-semibold">الدور</TableHead>
                  <TableHead className="font-semibold">الميزة</TableHead>
                  <TableHead className="text-center font-semibold">
                    <div className="flex items-center justify-center space-x-1">
                      <Eye className="h-4 w-4" />
                      <span>عرض</span>
                    </div>
                  </TableHead>
                  <TableHead className="text-center font-semibold">
                    <div className="flex items-center justify-center space-x-1">
                      <Edit className="h-4 w-4" />
                      <span>تعديل</span>
                    </div>
                  </TableHead>
                  <TableHead className="text-center font-semibold">
                    <div className="flex items-center justify-center space-x-1">
                      <Plus className="h-4 w-4" />
                      <span>إنشاء</span>
                    </div>
                  </TableHead>
                  <TableHead className="text-center font-semibold">
                    <div className="flex items-center justify-center space-x-1">
                      <Trash2 className="h-4 w-4" />
                      <span>حذف</span>
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRolePermissions.map((rolePermission, roleIndex) => {
                  const RoleIcon = getRoleIcon(rolePermission.role);
                  return rolePermission.permissions.map((permission, permissionIndex) => (
                    <TableRow key={`${rolePermission.role}-${permissionIndex}`} className="hover:bg-muted/30 transition-colors">
                      {permissionIndex === 0 && (
                        <TableCell rowSpan={rolePermission.permissions.length} className="border-r">
                          <Badge className={`${getRoleColor(rolePermission.role)} font-medium`}>
                            <RoleIcon className="mr-1 h-3 w-3" />
                            {rolePermission.role}
                          </Badge>
                        </TableCell>
                      )}
                      <TableCell className="font-medium">{permission.feature}</TableCell>
                      <TableCell className="text-center">
                        <Switch
                          checked={permission.view}
                          onCheckedChange={(value) => handlePermissionChange(roleIndex, permissionIndex, 'view', value)}
                        />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch
                          checked={permission.edit}
                          onCheckedChange={(value) => handlePermissionChange(roleIndex, permissionIndex, 'edit', value)}
                          disabled={!permission.view}
                        />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch
                          checked={permission.create}
                          onCheckedChange={(value) => handlePermissionChange(roleIndex, permissionIndex, 'create', value)}
                          disabled={!permission.view}
                        />
                      </TableCell>
                      <TableCell className="text-center">
                        <Switch
                          checked={permission.delete}
                          onCheckedChange={(value) => handlePermissionChange(roleIndex, permissionIndex, 'delete', value)}
                          disabled={!permission.view}
                        />
                      </TableCell>
                    </TableRow>
                  ));
                })}
              </TableBody>
            </Table>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center mt-6 p-4 rounded-lg border bg-muted/30">
            <div className="flex items-center space-x-2">
              {hasChanges ? (
                <CheckCircle className="h-5 w-5 text-yellow-500" />
              ) : (
                <CheckCircle className="h-5 w-5 text-green-500" />
              )}
              <span className="text-sm font-medium">
                {hasChanges ? 'يوجد تغييرات غير محفوظة' : 'جميع التغييرات محفوظة'}
              </span>
            </div>
            <div className="flex gap-3">
              <Button onClick={handleResetChanges} variant="outline" disabled={!hasChanges}>
                <RotateCcw className="mr-2 h-4 w-4" />
                إعادة تعيين
              </Button>
              <Button onClick={handleSaveChanges} disabled={!hasChanges} className="bg-primary hover:bg-primary/90">
                <Save className="mr-2 h-4 w-4" />
                حفظ التغييرات
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
