'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Fingerprint, 
  Shield, 
  Smartphone, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Trash2,
  Plus,
  Eye,
  EyeOff
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth-context';
import { BiometricCredential } from '@/types';
import {
  isBiometricSupported,
  isPlatformAuthenticatorAvailable,
  registerBiometric,
  saveBiometricCredential,
  getBiometricCredentials,
  deleteBiometricCredential,
} from '@/lib/biometric-auth';

interface BiometricSetupProps {
  isOpen: boolean;
  onClose: () => void;
}

export function BiometricSetup({ isOpen, onClose }: BiometricSetupProps) {
  const { user } = useAuth();
  const { toast } = useToast();

  const [isSupported, setIsSupported] = useState(false);
  const [isPlatformAvailable, setIsPlatformAvailable] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [credentials, setCredentials] = useState<BiometricCredential[]>([]);
  const [showCredentials, setShowCredentials] = useState(false);

  useEffect(() => {
    checkBiometricSupport();
    if (user) {
      loadCredentials();
    }
  }, [user]);

  const checkBiometricSupport = async () => {
    const supported = isBiometricSupported();
    setIsSupported(supported);

    if (supported) {
      const platformAvailable = await isPlatformAuthenticatorAvailable();
      setIsPlatformAvailable(platformAvailable);
    }
  };

  const loadCredentials = () => {
    if (user) {
      const userCredentials = getBiometricCredentials(user.id);
      setCredentials(userCredentials);
    }
  };

  const handleRegisterBiometric = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const registrationResponse = await registerBiometric(
        user.id,
        user.username,
        user.name || user.username
      );

      const savedCredential = await saveBiometricCredential(user.id, registrationResponse);
      
      setCredentials(prev => [...prev, savedCredential]);
      
      toast({
        title: 'تم تفعيل البصمة بنجاح',
        description: `تم تسجيل البصمة على ${savedCredential.deviceName}`,
      });
    } catch (error) {
      console.error('Biometric registration failed:', error);
      toast({
        title: 'فشل في تفعيل البصمة',
        description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteCredential = async (credentialId: string) => {
    if (!user) return;

    try {
      const success = await deleteBiometricCredential(user.id, credentialId);
      if (success) {
        setCredentials(prev => prev.filter(cred => cred.credentialId !== credentialId));
        toast({
          title: 'تم حذف البصمة',
          description: 'تم حذف البصمة المحددة بنجاح',
        });
      }
    } catch (error) {
      toast({
        title: 'فشل في حذف البصمة',
        description: 'حدث خطأ أثناء حذف البصمة',
        variant: 'destructive',
      });
    }
  };

  const getBiometricIcon = () => {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
      return <Fingerprint className="h-8 w-8 text-blue-500" />;
    }
    if (userAgent.includes('Mac')) {
      return <Fingerprint className="h-8 w-8 text-gray-600" />;
    }
    if (userAgent.includes('Windows')) {
      return <Shield className="h-8 w-8 text-blue-600" />;
    }
    return <Fingerprint className="h-8 w-8 text-primary" />;
  };

  const getBiometricName = () => {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
      return 'Touch ID / Face ID';
    }
    if (userAgent.includes('Mac')) {
      return 'Touch ID';
    }
    if (userAgent.includes('Windows')) {
      return 'Windows Hello';
    }
    return 'البصمة البيومترية';
  };

  if (!isSupported) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <XCircle className="h-5 w-5 text-red-500" />
              <span>البصمة غير مدعومة</span>
            </DialogTitle>
            <DialogDescription>
              متصفحك أو جهازك لا يدعم المصادقة البيومترية
            </DialogDescription>
          </DialogHeader>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              للاستفادة من ميزة البصمة، يرجى استخدام متصفح حديث على جهاز يدعم المصادقة البيومترية
            </AlertDescription>
          </Alert>
          <DialogFooter>
            <Button onClick={onClose}>إغلاق</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            {getBiometricIcon()}
            <span>إعداد المصادقة البيومترية</span>
          </DialogTitle>
          <DialogDescription>
            قم بتفعيل {getBiometricName()} لتسجيل دخول أسرع وأكثر أماناً
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">حالة البصمة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span>دعم المتصفح:</span>
                <Badge variant={isSupported ? 'default' : 'destructive'}>
                  {isSupported ? 'مدعوم' : 'غير مدعوم'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>مصادق الجهاز:</span>
                <Badge variant={isPlatformAvailable ? 'default' : 'secondary'}>
                  {isPlatformAvailable ? 'متوفر' : 'غير متوفر'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>البصمات المسجلة:</span>
                <Badge variant="outline">
                  {credentials.filter(c => c.isActive).length} بصمة
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Registration Section */}
          {isPlatformAvailable && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">تسجيل بصمة جديدة</CardTitle>
                <CardDescription>
                  أضف بصمة جديدة لهذا الجهاز لتسجيل دخول سريع
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={handleRegisterBiometric}
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      جارٍ التسجيل...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      تسجيل بصمة جديدة
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Credentials List */}
          {credentials.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center justify-between">
                  <span>البصمات المسجلة</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowCredentials(!showCredentials)}
                  >
                    {showCredentials ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {showCredentials ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>الجهاز</TableHead>
                        <TableHead>تاريخ التسجيل</TableHead>
                        <TableHead>آخر استخدام</TableHead>
                        <TableHead>الحالة</TableHead>
                        <TableHead className="text-right">الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {credentials.map((credential) => (
                        <TableRow key={credential.id}>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Smartphone className="h-4 w-4 text-muted-foreground" />
                              <span>{credential.deviceName || 'جهاز غير معروف'}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {new Date(credential.createdAt).toLocaleDateString('ar-YE')}
                          </TableCell>
                          <TableCell>
                            {credential.lastUsed 
                              ? new Date(credential.lastUsed).toLocaleDateString('ar-YE')
                              : 'لم يستخدم بعد'
                            }
                          </TableCell>
                          <TableCell>
                            <Badge variant={credential.isActive ? 'default' : 'secondary'}>
                              {credential.isActive ? 'نشط' : 'غير نشط'}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteCredential(credential.credentialId)}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-muted-foreground">
                      انقر على أيقونة العين لعرض تفاصيل البصمات المسجلة
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Security Notice */}
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>ملاحظة أمنية:</strong> البصمات البيومترية محفوظة بشكل آمن على جهازك ولا يتم إرسالها إلى خوادمنا. 
              يتم استخدام مفاتيح التشفير فقط للتحقق من هويتك.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            إغلاق
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
