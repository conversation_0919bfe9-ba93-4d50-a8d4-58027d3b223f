"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Fingerprint, 
  Smartphone, 
  Plus, 
  Trash2, 
  CheckCircle, 
  AlertTriangle 
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { BiometricSetup } from '@/components/auth/biometric-setup';
import { 
  getBiometricCredentials, 
  deleteBiometricCredential, 
  isBiometricSupported 
} from '@/lib/biometric-auth';
import { BiometricCredential } from '@/types';

interface BiometricManagerProps {
  showTitle?: boolean;
  compact?: boolean;
}

export function BiometricManager({ showTitle = true, compact = false }: BiometricManagerProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  
  // States
  const [isBiometricSetupOpen, setIsBiometricSetupOpen] = useState(false);
  const [biometricCredentials, setBiometricCredentials] = useState<BiometricCredential[]>([]);
  const [isBiometricSupportedState, setIsBiometricSupportedState] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Load biometric credentials
  useEffect(() => {
    setIsClient(true);
    setIsBiometricSupportedState(isBiometricSupported());
    
    if (user) {
      const credentials = getBiometricCredentials(user.id);
      setBiometricCredentials(credentials);
    }
  }, [user]);

  const handleDeleteBiometric = async (credentialId: string) => {
    if (!user) return;

    try {
      const success = await deleteBiometricCredential(user.id, credentialId);
      if (success) {
        setBiometricCredentials(prev => 
          prev.filter(cred => cred.credentialId !== credentialId)
        );
        toast({
          title: 'تم حذف البصمة',
          description: 'تم حذف البصمة بنجاح من حسابك',
        });
      } else {
        throw new Error('فشل في حذف البصمة');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في حذف البصمة. حاول مرة أخرى.',
        variant: 'destructive',
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const refreshCredentials = () => {
    if (user) {
      const credentials = getBiometricCredentials(user.id);
      setBiometricCredentials(credentials);
    }
  };

  if (compact) {
    return (
      <div className="space-y-4">
        {/* Compact Biometric Support Status */}
        <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            {isClient && isBiometricSupportedState ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            )}
            <div>
              <p className="text-sm font-medium">
                {isClient && isBiometricSupportedState ? 'البصمة مدعومة' : 'البصمة غير مدعومة'}
              </p>
              <p className="text-xs text-muted-foreground">
                {biometricCredentials.length} بصمة مسجلة
              </p>
            </div>
          </div>
          {isClient && isBiometricSupportedState && (
            <Button
              size="sm"
              onClick={() => setIsBiometricSetupOpen(true)}
              className="flex items-center gap-1"
            >
              <Plus className="h-3 w-3" />
              إضافة
            </Button>
          )}
        </div>

        {/* Compact Registered Credentials */}
        {biometricCredentials.length > 0 && (
          <div className="space-y-2">
            {biometricCredentials.slice(0, 2).map((credential) => (
              <div
                key={credential.id}
                className="flex items-center justify-between p-2 border rounded text-sm"
              >
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Smartphone className="h-3 w-3 text-muted-foreground" />
                  <span className="font-medium">{credential.deviceName}</span>
                  <Badge variant={credential.isActive ? "default" : "secondary"} className="text-xs">
                    {credential.isActive ? 'نشط' : 'غير نشط'}
                  </Badge>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteBiometric(credential.credentialId)}
                  className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            ))}
            {biometricCredentials.length > 2 && (
              <p className="text-xs text-muted-foreground text-center">
                +{biometricCredentials.length - 2} بصمة أخرى
              </p>
            )}
          </div>
        )}

        {/* Biometric Setup Modal */}
        <BiometricSetup
          isOpen={isBiometricSetupOpen}
          onClose={() => {
            setIsBiometricSetupOpen(false);
            refreshCredentials();
          }}
        />
      </div>
    );
  }

  return (
    <Card className="shadow-lg">
      {showTitle && (
        <CardHeader>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Fingerprint className="h-6 w-6 text-primary" />
            <CardTitle>المصادقة البيومترية</CardTitle>
          </div>
          <CardDescription>
            استخدم بصمة الإصبع أو التعرف على الوجه لتسجيل الدخول بسرعة وأمان
          </CardDescription>
        </CardHeader>
      )}
      <CardContent className="space-y-4">
        {/* Biometric Support Status */}
        <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            {isClient && isBiometricSupportedState ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
            )}
            <div>
              <p className="font-medium">
                {isClient && isBiometricSupportedState ? 'البصمة مدعومة' : 'البصمة غير مدعومة'}
              </p>
              <p className="text-sm text-muted-foreground">
                {isClient && isBiometricSupportedState 
                  ? 'يمكنك استخدام البصمة على هذا الجهاز'
                  : 'هذا الجهاز لا يدعم المصادقة البيومترية'
                }
              </p>
            </div>
          </div>
          {isClient && isBiometricSupportedState && (
            <Button
              onClick={() => setIsBiometricSetupOpen(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              إضافة بصمة جديدة
            </Button>
          )}
        </div>

        {/* Registered Biometric Credentials */}
        {biometricCredentials.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">البصمات المسجلة</h4>
            {biometricCredentials.map((credential) => (
              <div
                key={credential.id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Smartphone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{credential.deviceName}</p>
                    <p className="text-sm text-muted-foreground">
                      تم التسجيل: {formatDate(credential.createdAt)}
                    </p>
                    {credential.lastUsed && (
                      <p className="text-xs text-muted-foreground">
                        آخر استخدام: {formatDate(credential.lastUsed)}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Badge variant={credential.isActive ? "default" : "secondary"}>
                    {credential.isActive ? 'نشط' : 'غير نشط'}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteBiometric(credential.credentialId)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* No Credentials Message */}
        {biometricCredentials.length === 0 && isClient && isBiometricSupportedState && (
          <div className="text-center py-6">
            <Fingerprint className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground mb-4">
              لم تقم بتسجيل أي بصمة بعد
            </p>
            <Button
              onClick={() => setIsBiometricSetupOpen(true)}
              className="flex items-center gap-2 mx-auto"
            >
              <Plus className="h-4 w-4" />
              تسجيل بصمة جديدة
            </Button>
          </div>
        )}
      </CardContent>

      {/* Biometric Setup Modal */}
      <BiometricSetup
        isOpen={isBiometricSetupOpen}
        onClose={() => {
          setIsBiometricSetupOpen(false);
          refreshCredentials();
        }}
      />
    </Card>
  );
}
