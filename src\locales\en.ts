
export default {
  common: {
    appName: 'MarketSync',
    profile: 'Profile',
    settings: 'Settings',
    logout: 'Log out',
    toggleTheme: 'Toggle theme',
    language: 'Language',
    english: 'English',
    arabic: 'Arabic',
    loading: 'Loading...',
    error: 'Error',
    save: 'Save',
    cancel: 'Cancel',
    edit: 'Edit',
    delete: 'Delete',
    viewDetails: "View Details",
    add: "Add",
    remove: "Remove",
    status: "Status",
    actions: "Actions",
    all: "All",
    yes: "Yes",
    no: "No",
    N_A: "N/A", // Not Applicable
    searchPlaceholder: "Search...",
    filterPlaceholder: "Filter...",
    noData: "No data available.",
    noResults: "No results found.",
    confirm: "Confirm",
    areYouSure: "Are you sure?",
    thisActionCannotBeUndone: "This action cannot be undone.",
    pickADate: "Pick a date",
    success: "Success",
    items: "items",
    optional: "Optional",
    comingSoon: "Feature coming soon.",
    subscriptionExpired: "Your subscription has expired.",
    renewSubscriptionPrompt: "Please renew your subscription to continue accessing all features.",
    manageSubscriptionNow: "Manage Subscription Now",

    // Navigation keys
    nav_dashboard: 'Dashboard',
    nav_userManagement: 'User Management',
    nav_subscriptionControl: 'Subscription Control',
    nav_systemLogs: 'System Logs',
    nav_accessControl: 'Access Control',
    nav_backupManagement: 'Backup Management',
    nav_staffManagement: 'User Management', // Changed from Staff Management
    nav_productManagement: 'Product Management',
    nav_onlineStore: 'Online Store',
    nav_customerOrders: 'Customer Orders',
    nav_debtsManagement: 'Debts Management',
    nav_salesReports: 'Sales Reports',
    nav_subscription: 'Subscription',
    nav_inventoryCheck: 'Inventory Check',
    nav_smartPredictions: 'Smart Predictions',
    nav_ownerSettings: 'Account Settings', 
    nav_profile: 'Profile',
    nav_generalSettings: 'Settings',
    nav_securitySettings: 'Security Settings',
    nav_recordTransactions: 'Record Transactions',
    nav_approveOrders: 'Approve Orders',
    nav_dispatchOrders: 'Dispatch Orders',
    nav_shop: 'Shop',
    nav_myOrders: 'My Orders',
    nav_myDebts: 'My Debts',
    nav_orderHistory: 'Order History',
    nav_favorites: 'Favorites',
    nav_manageWholesaleProducts: 'Manage Products', 
    nav_incomingOrders: 'Incoming Orders', 
    nav_manageAgents: 'Manage Agents', 
    nav_assignedOrders: 'Assigned Orders', 
    nav_notifications: 'Notifications',
    nav_tasks: 'Task Management', 
    nav_manageBalances: 'Manage Balances', 
    nav_processReturns: 'Process Returns', 
    nav_manageEmpProducts: 'Manage Products',
    nav_setCreditLimits: 'Set Credit Limits',
    nav_grantCredit: 'Grant Credit',
    nav_grantCreditOwner: "Grant Credit to Customers",
    nav_manageBalancesOwner: "Manage Customer Balances",
    nav_processReturnsOwner: "Process Sales Returns",
    nav_setCreditLimitsOwner: "Set Customer Credit Limits",
    nav_communicationCenter: "Communication Center",
    nav_productLookup: "Product Lookup",
    nav_shifts: "Shift Management",
    nav_performanceReports: "Performance Reports",
    nav_returnsManagement: "Returns Management",
    nav_customerBalances: "Customer Balances",
    nav_inventoryReports: "Inventory Reports",
    nav_printReports: "Print Reports",
    nav_customerManagement: "Customer Management",
    nav_advancedAnalytics: "Advanced Analytics",
    nav_marketingTools: "Marketing Tools",
    nav_supplierManagement: "Supplier Management",
    nav_financialManagement: "Financial Management",
    nav_loyaltyProgram: "Loyalty Program",
    nav_alerts: "Send Alerts",
    nav_supportTickets: "Support Tickets",
    nav_appSettings: "Application Settings",
  },
  login: {
    pageTitle: 'Login - MarketSync',
    logoAlt: 'MarketSync Logo',
    appName: 'MarketSync',
    tagline: 'Streamlining Supermarket Operations',
    welcome: 'Welcome Back!',
    signInPrompt: 'Sign in to access your MarketSync account.',
    usernameLabel: 'Username',
    usernamePlaceholder: 'e.g., admin or owner',
    usernameRequired: 'Username is required.',
    passwordLabel: 'Password',
    passwordPlaceholder: 'Enter your password',
    passwordRequired: 'Password is required.',
    signInButton: 'Sign In',
    signingIn: 'Signing In...',
    copyright: '© {year} MarketSync. All rights reserved.',
  },
  auth: {
    loginFailed: 'Login Failed',
    invalidCredentials: 'Invalid username or password.',
    loginError: 'Login Error',
    unexpectedError: 'An unexpected error occurred while logging in.',
    loginSuccess: 'Login Successful',
    welcomeBack: 'Welcome back, {name}!',
    loggedOut: 'Logged Out',
    loggedOutSuccess: 'You have been successfully logged out.',
  },
  form: {
    usernameMin: "Username must be at least 3 characters",
    emailInvalid: "Invalid email address",
    passwordMin: "Password must be at least 6 characters",
    required: "{field} is required.",
    fieldRequired: "{field} is required.", 
    fieldInvalid: "{field} is invalid.",
  },
  adminDashboard: {
    title: "Admin Dashboard",
    totalUsers: "Total Users",
    activeSubscriptions: "Active Subscriptions",
    systemLogs: "System Logs",
    pendingSupportTickets: "Pending Support Tickets",
    quickActions: "Quick Actions",
    quickActionsDescription: "Perform common administrative tasks.",
    manageUsers: "Manage Users",
    controlSubscriptions: "Control Subscriptions",
    accessControl: "Access Control",
    viewSystemReports: "View System Reports", // Obsolete, logs used instead
    sendNotifications: "Send Notifications",
    systemHealth: "System Health",
    systemHealthDescription: "Overview of system status and performance.",
    overallSystemStatus: "Overall System Status:",
    optimal: "Optimal",
    lastBackup: "Last Backup:",
    todayAt: "Today at {time}",
    recentSecurityAlerts: "Recent Security Alerts:",
    backupManagement: "Backup Management",
  },
  ownerDashboard: {
    title: "Owner Dashboard",
    totalStaff: "Total Users", // Changed from "Total Staff"
    productsListed: "Products Listed",
    todaysSales: "Today's Sales (YER)",
    pendingOrders: "Pending Orders",
    totalDebts: "Total Debts (YER)",
    quickActions: "Quick Actions",
    quickActionsDescription: "Access key supermarket management features.",
    manageStaff: "Manage Users", // Changed from "Manage Staff"
    manageProducts: "Manage Products",
    viewSalesReports: "View Sales Reports",
    inventoryCheck: "Inventory Check",
    aiDemandForecast: "AI Demand Forecast",
    manageDebts: "Manage Debts",
    subscriptionStatus: "Subscription Status",
    currentSubscriptionActive: "Your current subscription is active.",
    renewsOn: "Renews on: {date}",
    manageSubscription: "Manage Subscription",
    subscriptionExpiredTitle: "Subscription Expired",
    subscriptionExpiredMessage: "Your MarketSync subscription has expired. Please renew to continue using all features.",
    renewPrompt: "Please renew to continue accessing all features.",
    manageSubscriptionButton: "Renew / Manage Subscription",
  },
  userManagement: {
    title: "User Management",
    description: "Create, view, edit, and manage all user accounts in the system.",
    allUsers: "All Users",
    allUsersDescription: "A comprehensive list of all users within MarketSync.",
    filterPlaceholder: "Filter users by username, email, or role...",
    addUser: "Add User",
    editUser: "Edit User",
    editUserDesc: "Update the details for this user.",
    addNewUser: "Add New User",
    addNewUserDesc: "Fill in the details for the new user.",
    username: "Username",
    email: "Email",
    role: "Role",
    name: "Full Name",
    subscriptionEndDate: "Subscription End Date",
    password: "Password",
    passwordPlaceholderEdit: "Leave blank to keep current",
    passwordPlaceholderCreate: "Enter password",
    passwordRequiredForNew: "Password is required for new users.",
    passwordLeaveBlankDescription: "Leave password blank to keep it unchanged.",
    selectRole: "Select a role",
    userCreatedSuccess: "User created successfully.",
    userUpdatedSuccess: "User updated successfully.",
    userDeletedSuccess: "User deleted successfully.",
    errorFetchUsers: "Failed to fetch users.",
    errorCreateUser: "Failed to create user.",
    errorUpdateUser: "Failed to update user.",
    errorDeleteUser: "Failed to delete user.",
    confirmDeleteUser: "Are you sure you want to delete this user? This action cannot be undone.",
    role_admin: "Admin",
    role_owner: "Owner",
    role_employee: "Employee",
    role_customer: "Customer",
    role_wholesaler: "Wholesaler",
    role_agent: "Agent",
    subscriptionEndDateHelp: "Required for Owner and Wholesaler roles.",
    saving: "Saving...",
    adminRoleExistsError: "An admin user already exists. Only one admin is allowed.",
    exportUsersButton: "Export Users",
    exportUsersToastTitle: "Export Started",
    exportUsersToastDesc: "User data export process has started (placeholder).",
  },
  ownerSettings: {
    title: "Owner Account Settings",
    description: "Manage your store details, operational preferences, and application settings.",
    storeInformation: "Store Information",
    storeInfoDescription: "Update your supermarket's public details.",
    storeName: "Store Name",
    storeNamePlaceholder: "Your Supermarket Name",
    storeAddress: "Store Address",
    storeAddressPlaceholder: "e.g., 123 Main St, City, State",
    contactEmail: "Contact Email",
    contactEmailPlaceholder: "<EMAIL>",
    contactPhone: "Contact Phone",
    contactPhonePlaceholder: "(*************",
    storeLogo: "Store Logo",
    storeLogoHelp: "Upload your store's logo (e.g., PNG, JPG).",
    operationalSettings: "Operational Settings",
    operationalSettingsDescription: "Manage operational aspects of your store.",
    defaultDeliveryFee: "Default Delivery Fee (YER)",
    minOrderValueForDelivery: "Minimum Order Value for Delivery (YER)",
    openingHours: "Opening Hours (Text)",
    openingHoursPlaceholder: "e.g., Mon-Fri: 9 AM - 9 PM, Sat-Sun: 10 AM - 7 PM",
    appearanceSettings: "Appearance Settings",
    appearanceSettingsDescription: "Customize the look and feel of the application.",
    theme: "Theme",
    themeDescription: "Select your preferred application theme.",
    selectTheme: "Select theme",
    themeLight: "Light",
    themeDark: "Dark",
    themeSystem: "System",
    notificationSettings: "Notification Settings",
    notificationSettingsDescription: "Manage how you receive notifications.",
    emailNotifications: "Email Notifications",
    emailNotificationsHelp: "Receive important updates via email.",
    newOrderNotifications: "New Order Notifications",
    newOrderNotificationsHelp: "Get notified for new customer orders.",
    lowStockAlerts: "Low Stock Alerts",
    lowStockAlertsHelp: "Receive alerts when product stock is low.",
    saveAllSettings: "Save All Settings",
    pushNotifications: "Push Notifications",
    pushNotificationsHelp: "Get real-time alerts on your device.",
    inAppNotifications: "In-App Notifications",
    inAppNotificationsHelp: "Show notifications within the app.",
    integrationsTitle: "System Integrations",
    integrationsDescription: "Manage integrations with other systems like POS or accounting software.",
    integrationsComingSoon: "Integration options will be available here in the future.",
  },
  accessControl: {
    title: "Access Control Management",
    description: "Define and manage roles and permissions across the platform.",
    roleBasedPermissions: "Role-Based Permissions",
    configurePermissions: "Configure what each user role can access and do.",
    manageUsersAndRoles: "Manage Users & Roles",
    filterByRoleOrFeature: "Filter by role or feature...",
    noRolesConfigured: "No roles configured for permissions management.",
    editPermissionsForRole: "Edit {role} Permissions",
    feature: "Feature",
    view: "View",
    edit: "Edit",
    create: "Create",
    delete: "Delete",
    saveAllChanges: "Save All Changes",
    permissionsSavedSuccess: "Permissions saved successfully.",
    // Access Control Stats
    totalRoles: "Total Roles",
    rolesConfigured: "roles configured",
    totalFeatures: "Total Features",
    featuresManaged: "features managed",
    averagePermissionLevel: "Average Permission Level",
    securityStatus: "Security Status",
    review: "Review",
    secure: "Secure",
    highPrivilegeRoles: "high privilege roles",
    rolePermissionBreakdown: "Role Permission Breakdown",
    permissions: "permissions",
    highPrivilegeAlert: "High Privilege Alert",
    highPrivilegeWarning: "These roles have extensive permissions. Review regularly.",
    restrictedRoles: "Restricted Roles",
    restrictedRolesInfo: "These roles have limited access to system features.",
    // Permission Matrix
    permissionMatrix: "Permission Matrix",
    manageDetailedPermissions: "Manage detailed permissions for each role",
    searchPermissions: "Search Permissions",
    searchPermissionsPlaceholder: "Search by permission name or description...",
    category: "Category",
    allCategories: "All Categories",
    riskLevel: "Risk Level",
    allRiskLevels: "All Risk Levels",
    riskLevel_low: "Low",
    riskLevel_medium: "Medium",
    riskLevel_high: "High",
    resetToDefaults: "Reset to Defaults",
    permissionsGranted: "permissions granted",
    permission: "Permission",
    showingPermissions: "Showing {count} permissions",
    noPermissionsFound: "No permissions found",
    adjustFiltersToSeeMore: "Adjust your filters to see more permissions",
    permissionsSaved: "Permissions Saved",
    permissionsUpdatedSuccessfully: "Permissions have been updated successfully",
    confirmResetPermissions: "Are you sure you want to reset all permissions to defaults?",
    permissionsReset: "Permissions Reset",
    permissionsResetToDefaults: "All permissions have been reset to default values",
  },
  debtsManagement: {
    title: "Debts Management",
    description: "Record and manage outstanding debts and credits.",
    recordNewDebtCredit: "Record New Debt/Credit",
    formTitleRecord: "Record New Debt/Credit",
    formTitleEdit: "Edit Debt/Credit",
    partyName: "Party Name",
    partyNamePlaceholder: "e.g., John Customer or Supplier X",
    amount: "Amount (YER)",
    amountPlaceholder: "0.00",
    type: "Type",
    selectType: "Select type",
    typeDebt: "Debt (Owed to you)",
    typeCredit: "Credit (You Owe)",
    status: "Status",
    selectStatus: "Select status",
    statusUnpaid: "Unpaid",
    statusPaid: "Paid",
    statusPartiallyPaid: "Partially Paid",
    date: "Date",
    dueDateOptional: "Due Date (Optional)",
    pickDueDate: "Pick a due date",
    notesOptional: "Notes (Optional)",
    notesPlaceholder: "Any additional details...",
    debtUpdated: "Debt Updated",
    debtUpdatedDesc: "Details for {partyName} updated.",
    debtRecorded: "Debt Recorded",
    debtRecordedDesc: "{type} for {partyName} recorded.",
    debtRemoved: "Debt Removed",
    debtRemovedDesc: "The debt record has been removed.",
    recordsTitle: "Debt & Credit Records",
    recordsDescription: "Overview of all recorded debts and credits.",
    filterRecordsPlaceholder: "Filter by party name or notes...",
    noRecords: "No debts or credits recorded yet.",
    recordsCount: "Showing {count} of {total} records.",
    itemsForDebt: "Items for Debt",
    selectProduct: "Select Product",
    selectProductPlaceholder: "Choose a product",
    itemQuantity: "Quantity",
    itemPrice: "Price (YER)",
    each: "each",
    addItemButton: "Add Item to Debt",
    itemsRequiredError: "At least one item is required for the debt.",
    addItemError: "Please select a product and ensure quantity/price are valid.",
    itemsCount: "Items",
    invoiceTitle: "Invoice",
    invoiceTo: "Invoice To",
    debtTypeLabel: "Debt Type",
    invoiceNumber: "Invoice #",
    itemProduct: "Product",
    itemSubtotal: "Subtotal",
    totalAmount: "Total Amount",
    thankYouMessage: "Thank you for your business!",
    printInvoice: "Print Invoice",
    printWindowError: "Could not open print window. Please check your browser settings.",
    storeNamePlaceholder: "Your Store Name",
    storeAddressPlaceholder: "Your Store Address, City, ZIP",
  },
  agentDashboard: {
    title: "Agent Dashboard",
    currentTasks: "Current Tasks",
    newDeliveryTasks: "New Delivery Tasks",
    inProgressDeliveries: "In-Progress Deliveries",
    recentNotifications: "Recent Notifications",
    viewTasks: "View Tasks",
    quickActions: "Quick Actions",
    quickActionsDescription: "Efficiently manage your delivery tasks.",
    viewAssignedOrders: "View Assigned Orders",
    updateOrderStatus: "Update Order Status",
    communicationCenter: "Communication Center",
    myRouteForToday: "My Route for Today",
    deliveryPerformance: "Delivery Performance",
    deliveryPerformanceDescription: "Your recent delivery statistics.",
    completedDeliveriesLast7Days: "Completed Deliveries (Last 7 days): {count}",
    onTimeDeliveryRate: "On-time Delivery Rate: {rate}%",
    averageDeliveryTime: "Average Delivery Time: {time} minutes",
  },
  agentOrders: {
    title: "Assigned Orders",
    description: "Manage and update the status of your delivery tasks.",
    filterSortOrders: "Filter / Sort Orders",
    orderTitle: "Order {id} - {customerName}",
    openInMaps: "Open in Maps",
    itemsCountSlot: "Items: {count} | Slot: {slot}",
    notesLabel: "Notes: {notes}",
    callCustomer: "Call",
    messageCustomer: "Message",
    startDelivery: "Start Delivery",
    updateStatus: "Update Status",
    updateNotes: "Update Notes",
    noOrdersWithStatus: "No orders with status \"{status}\".",
    statusAssigned: "Assigned",
    statusOutForDelivery: "Out for Delivery",
    statusDelivered: "Delivered",
    statusFailedAttempt: "Failed Attempt",
    statusAll: "All",
  },
  profile: {
    title: "Profile",
    description: "Manage your personal information and account settings.",
    detailsTitle: "Profile Details",
    detailsDescription: "View and update your profile information. Your role is: {role}",
    updateProfileButton: "Update Profile",
    changePasswordTitle: "Change Password",
    changePasswordDescription: "Update your account password.",
    currentPasswordLabel: "Current Password",
    currentPasswordPlaceholder: "Enter current password",
    newPasswordLabel: "New Password",
    newPasswordPlaceholder: "Enter new password",
    confirmPasswordLabel: "Confirm New Password",
    confirmPasswordPlaceholder: "Confirm new password",
    changePasswordButton: "Change Password",
    usernameCannotBeChanged: "Username cannot be changed.",
  },
  adminLogs: {
    title: "System Logs",
    description: "Review system activity, errors, and audit trails.",
    activityLogsTitle: "Activity Logs",
    activityLogsDescription: "Detailed logs of system operations and events.",
    filterUserActionPlaceholder: "Filter by user or action...",
    logLevelSelectPlaceholder: "Log Level",
    allLevels: "All Levels",
    levelInfo: "INFO",
    levelWarn: "WARN",
    levelError: "ERROR",
    levelDebug: "DEBUG",
    applyFiltersButton: "Apply Filters",
    exportLogsButton: "Export Logs",
    timestampHeader: "Timestamp",
    levelHeader: "Level",
    userSystemHeader: "User/System",
    actionHeader: "Action",
    detailsHeader: "Details",
    noLogsFound: "No logs found for the selected criteria.",
    filtersAppliedTitle: "Filters Applied",
    filtersAppliedDesc: "Log display updated based on selected filters (simulated).",
    exportLogsTitle: "Export Logs",
    exportLogsDescPlaceholder: "Log export process started (placeholder).",
    showingLogsCount: "Showing {count} logs.",
    analyzeLogsButton: "Analyze Logs",
    // Logs Analytics
    systemLogs: "System Logs",
    analytics: "Analytics",
    totalLogs: "Total Logs",
    inLast24Hours: "in last 24 hours",
    errorCount: "Error Count",
    warningCount: "Warning Count",
    systemWarnings: "system warnings",
    activeUsers: "Active Users",
    uniqueUsers: "unique users",
    logLevelDistribution: "Log Level Distribution",
    distributionOfLogLevels: "Distribution of log levels across all entries",
    hourlyActivity: "Hourly Activity",
    activityLast24Hours: "Activity in the last 24 hours",
    topActiveUsers: "Top Active Users",
    mostActiveUsers: "Users with the most system activity",
    actions: "actions",
    recentCriticalEvents: "Recent Critical Events",
    latestErrorsAndWarnings: "Latest errors and warnings that require attention",
    noCriticalEvents: "No critical events found",
    // System Monitor
    systemMonitor: "System Monitor",
    realTimeSystemMetrics: "Real-time system performance and health metrics",
    refresh: "Refresh",
    cpuUsage: "CPU Usage",
    cores: "cores",
    memoryUsage: "Memory Usage",
    diskUsage: "Disk Usage",
    networkStatus: "Network Status",
    databaseStatus: "Database Status",
    connectionStatus: "Connection Status",
    latency: "Latency",
    bandwidth: "Bandwidth",
    activeConnections: "Active Connections",
    avgQueryTime: "Avg Query Time",
    systemInformation: "System Information",
    systemUptime: "System Uptime",
    lastUpdated: "Last Updated",
    systemHealth: "System Health",
    healthy: "Healthy",
    warning: "Warning",
    statusOnline: "Online",
    statusSlow: "Slow",
    statusOffline: "Offline",
    currentlyOnline: "currently online",
  },
  adminSubscriptions: {
    title: "Subscription Control",
    description: "Manage user subscriptions and plans for Owners and Wholesalers.",
    manageSubscriptionsTitle: "Manage Subscriptions",
    manageSubscriptionsDescription: "View, modify, or manage user subscriptions.",
    filterPlaceholder: "Filter by user, email or plan...",
    noSubscriptionsFound: "No active subscriptions found.",
    userHeader: "User",
    emailHeader: "Email",
    roleHeader: "Role",
    planHeader: "Plan",
    endDateHeader: "End Date",
    statusHeader: "Status", 
    actionsHeader: "Actions", 
    editSubscriptionItem: "Edit Subscription",
    cancelSubscriptionItem: "Cancel Subscription",
    statusActive: "Active",
    statusExpired: "Expired",
    statusCancelled: "Cancelled",
    statusUnknown: "Unknown",
    createNewPlanButton: "Create New Plan",
    createNewPlanToastTitle: "Create Plan",
    createNewPlanToastDesc: "Subscription plan creation functionality is under development.",
    paymentTrackingInfo: "Payment tracking features are under development.",
    planManagementInfo: "Subscription plan management features are under development."
  },
  inventoryCheck: {
    title: "Inventory Check",
    description: "Monitor and manage your current stock levels.",
    addNewProductButton: "Add New Product to Inventory",
    totalProducts: "Total Products",
    inStock: "In Stock",
    lowStock: "Low Stock",
    outOfStock: "Out of Stock",
    currentStockLevelsTitle: "Current Stock Levels",
    currentStockLevelsDescription: "Detailed view of products and their stock status.",
    filterPlaceholder: "Filter by name, category, or supplier...",
    stockStatusSelectPlaceholder: "Stock Status",
    statusAll: "All Statuses",
    statusInStock: "In Stock",
    statusLowStock: "Low Stock",
    statusOutOfStock: "Out of Stock",
    viewStockHistoryButton: "View Stock History",
    headerImage: "Image",
    headerName: "Name",
    headerCategory: "Category",
    headerStockQty: "Stock Qty",
    headerStatus: "Status",
    headerSupplier: "Supplier",
    headerLastRestock: "Last Restock",
    updateButton: "Update",
    noProductsFound: "No products match your filter, or inventory is empty.",
    updateStockModalTitle: "Update Stock for: {productName}",
    updateStockModalDescription: "Current stock: {currentStock}. Enter the new total stock level.",
    newStockLabel: "New Stock",
    newStockPlaceholder: "Enter new stock quantity",
    reasonLabel: "Reason (Optional)",
    reasonPlaceholder: "e.g., Weekly restock, Correction",
    toastInvalidStockTitle: "Invalid Stock Level",
    toastInvalidStockDesc: "Stock must be a non-negative number.",
    toastStockUpdatedTitle: "Stock Updated",
    toastStockUpdatedDesc: "Stock for {productName} updated to {stockValue}.",
    toastNewProductAddedTitle: "New Product Added",
    toastNewProductAddedDesc: "{productName} added to inventory. Update its details.",
    loadingProducts: "Loading products...",
    newProductDefaultName: "New Product",
    uncategorized: "Uncategorized",
    newProductDataAiHint: "new product",
    productAddedToastTitle: "Product Added",
    productAddedToastDesc: "{productName} added to inventory. Please update its details.",
    productUpdatedToastTitle: "Product Updated",
    productUpdatedToastDesc: "Stock for {productName} updated to {stockValue}.",
    updateErrorToastDesc: "Please select a product and enter a valid stock level.",
  },
  ownerStaff: { 
    title: "User Management", 
    description: "Manage employees and customers associated with your store.", 
    addNewEmployeeButton: "Add New User", 
    employeeListTitle: "User List", 
    employeeListDescription: "View, add, edit, or remove employees and customers.", 
    filterPlaceholder: "Filter users by name or role...", 
    noStaffMembers: "No users added yet. Click \"Add New User\" to start.", 
    rolePositionHeader: "Role/Position",
    editDetailsItem: "Edit Details",
    managePermissionsItem: "Manage Permissions",
    removeEmployeeItem: "Remove User", 
    roleManager: "Manager",
    roleCashierStaff: "Cashier/Staff", 
    addStaffMemberTitle: "Add New User", 
    editStaffMemberTitle: "Edit User Details", 
    addStaffMemberDesc: "Fill in the details for the new user.", 
    editStaffMemberDesc: "Update the details for this user.", 
    passwordLeaveBlankDescription: "Leave password blank to keep it unchanged.",
    passwordRequiredForNewStaff: "Password is required for new users.",
    staffMemberAddedSuccess: "User added successfully.", 
    staffMemberUpdatedSuccess: "User details updated successfully.", 
    staffMemberRemovedSuccess: "User removed successfully.", 
    confirmRemoveEmployee: "Are you sure you want to remove this user?", 
    errorFetchingUsers: "Failed to fetch users.",
    errorProcessingUser: "Failed to process user.",
    errorDeletingUser: "Failed to delete user.",
    permissionsTitle: "Permissions",
    permissionsForEmployeeRole: "Assign specific permissions for this employee.",
    permissionCanReceiveOrders: "Receive Customer Orders",
    permissionCanGrantCredit: "Grant Credit to Customers",
    permissionCanManageCustomerBalances: "Manage Customer Balances",
    permissionCanProcessSalesReturns: "Process Sales Returns",
    permissionCanManageProducts: "Manage Products (Add/Price)",
    permissionCanSetCreditLimits: "Set Customer Credit Limits",
    permissionCanDispatchOrders: "Dispatch Orders",
    permissionCanRecordTransactions: "Record Transactions",
    permissionCanManageShifts: "Manage Shifts",
    permissionCanViewPerformanceReports: "View Performance Reports",
    permissionCanAccessCommunicationTools: "Access Communication Tools",
    permissionCanViewInventoryReports: "View Inventory Reports",
    permissionCanPrintReports: "Print Reports",
    permissionCanAccessProductLookup: "Access Product Lookup",
    permissionCanManageTasks: "Manage Tasks",
  },
  customerDashboard: {
    welcome: "Welcome, Customer!",
    tagline: "Ready to shop? Here are some quick links to get you started.",
    startShopping: "Start Shopping",
    startShoppingDesc: "Browse our latest products and offers.",
    shopNow: "Shop Now",
    myOrders: "My Orders",
    myOrdersDesc: "Track your current orders and view details.",
    viewOrders: "View Orders",
    myDebts: "My Debts",
    myDebtsDesc: "View your outstanding debts with the store.",
    viewDebts: "View Debts",
    totalOutstandingDebt: "Total Outstanding Debt",
    orderHistory: "Order History",
    orderHistoryDesc: "Review your past purchases.",
    seeHistory: "See History",
    favorites: "Favorites",
    favoritesDesc: "Access your saved items.",
    myFavorites: "My Favorites",
    featuredCategories: "Featured Categories",
    freshProduce: "Fresh Produce",
    dairyEggs: "Dairy & Eggs",
    bakery: "Bakery",
    exploreCategory: "Explore {categoryName}",
    notificationsTitle: "Notifications",
    notificationsMessage: "You have {count} new notifications.",
    viewNotifications: "View Notifications",
    reviewsRatingsTitle: "Reviews & Ratings",
    reviewsRatingsMessage: "Share your feedback on recent purchases.",
    leaveAReview: "Leave a Review",
  },
  customerOrders: {
    title: "My Orders",
    description: "Track your current orders and view their status.",
    tabActive: "Active",
    tabDelivered: "Delivered",
    tabCancelled: "Cancelled",
    tabAllOrders: "All Orders",
    noOrdersFoundTitle: "No Orders Found",
    noOrdersWithStatus: "You have no orders with status \"{status}\".",
    startShoppingButton: "Start Shopping",
    orderDetails: "Date: {date} | {itemCount} items",
    statusProcessing: "Processing",
    statusShipped: "Shipped",
    statusDelivered: "Delivered",
    statusCancelled: "Cancelled",
    quantityLabel: "Qty: {qty}",
    andMoreItems: ", and {count} more...",
    totalLabel: "Total (YER)",
    trackingLabel: "Tracking",
    viewDetailsButton: "View Details",
    reorderButton: "Reorder",
    invoiceButton: "Invoice",
  },
  customerShop: {
    title: "Shop Products",
    description: "Browse and purchase items from our supermarket.",
    searchPlaceholder: "Search products...",
    filtersButton: "Filters",
    specialOffer: "✨ Special Offer: Get 10% off on all Dairy products this week! Use code: DAIRY10 ✨",
    storeUpdatingTitle: "Our online store is currently updating!",
    noProductsAvailable: "No products available at the moment. Please check back soon.",
    toggleFavorite: "Toggle Favorite",
    bestSellerBadge: "Best Seller",
    reviewsCount: "({count} reviews)",
    addToCartButton: "Add to Cart",
    outOfStockButton: "Out of Stock",
    loadMoreButton: "Load More Products",
    loadingProducts: "Loading products...",
    userNotAuthenticatedError: "You must be logged in to add items to the cart.",
    cannotDetermineStoreError: "Cannot determine the store for this item. Please try again.",
    addedToCartToast: "{productName} (Qty: {quantity}) added to cart.",
    addToCartErrorToast: "Failed to add item to cart. Please try again.",
    quantityLabelShort: "Qty",
    notEnoughStockError: "Not enough stock for {productName}. Available: {availableStock}.",
  },
  ownerProductManagement: {
    title: "Product Management",
    description: "Manage your supermarket's product inventory and listings.",
    add_new_product_button: "Add New Product",
    product_catalog_title: "Product Catalog",
    product_catalog_description: "Add, edit, categorize, and manage product details and stock.",
    filter_placeholder: "Filter products by name or category...",
    manage_categories_button: "Manage Categories",
    sort_by_button: "Sort By",
    no_products_message: "No products listed yet. Click \"Add New Product\" to start or adjust filters.",
    table_header_image: "Image",
    table_header_name: "Name",
    table_header_category: "Category",
    table_header_price: "Price (YER)",
    table_header_stock: "Stock",
    table_header_status: "Status",
    table_header_online: "Online",
    statusInStock: "In Stock",
    statusLowStock: "Low Stock",
    statusOutOfStock: "Out of Stock",
    dialog_edit_product_title: "Edit Product",
    dialog_add_product_title: "Add New Product",
    dialog_edit_product_desc: "Update the details for this product.",
    dialog_add_product_desc: "Fill in the details for the new product.",
    form_label_name: "Name",
    form_placeholder_name: "Product Name",
    form_label_category: "Category",
    form_placeholder_category: "e.g., Fresh Produce",
    form_label_price: "Price (YER)",
    form_label_stock: "Stock Qty",
    form_label_image_url: "Image URL",
    form_placeholder_image_url: "https://example.com/image.jpg",
    form_alt_current_image: "Current product image",
    form_label_min_order: "Min. Order Qty",
    form_label_online: "Available Online",
    validation_message_required_fields: "Name, category, and price are required.",
    toast_product_updated_title: "Product Updated",
    toast_product_updated_desc: "{productName} has been updated.",
    toast_product_added_title: "Product Added",
    toast_product_added_desc: "{productName} has been added.",
    confirm_delete_product: "Are you sure you want to delete this product?",
    toast_product_deleted_title: "Product Deleted",
    toast_product_deleted_desc: "The product has been removed.",
    product_image_generation_title: "AI Image Generation",
    product_image_generation_description: "Upload an image to enhance, or let AI generate one based on product details.",
    upload_for_ai_label: "Upload Image for AI Processing",
    generate_enhance_ai_button: "Generate/Enhance with AI",
    toast_generating_image_title: "Generating Image",
    toast_generating_image_desc: "AI is working on your product image...",
    toast_image_generated_success: "AI image generated successfully!",
    toast_image_generated_error_desc: "Failed to generate image with AI. Please try again or check details.",
    validation_message_name_category_for_ai: "Product name and category are required for AI image generation.",
    ai_hint_for_generation_label: "AI Hint for Image (Optional)",
    ai_hint_for_generation_placeholder: "e.g., on a white background, lifestyle shot",
  },
  wholesalerDashboard: {
    quickActions: "Quick Actions",
    quickActionsDescription: "Manage your wholesale operations.",
    subscriptionStatus: "Subscription Status",
    currentSubscriptionActive: "Your current subscription is active.",
    renewsOn: "Renews on: {date}",
    manageSubscription: "Manage Subscription",
    monthlyRevenue: "Monthly Revenue (YER)", 
    createSpecialOffers: "Create Special Offers",
    subscriptionExpiredTitle: "Subscription Expired",
    subscriptionExpiredMessage: "Your MarketSync Wholesale subscription has expired. Please renew to continue accessing all features.",
  },
   ownerPurchases: {
    title: "Customer Orders",
    description: "View, manage, and process incoming customer orders.",
    filterOrdersButton: "Filter Orders",
    invoiceTitle: "Invoice",
    orderIdLabel: "Order ID",
    dateLabel: "Date",
    customerLabel: "Customer",
    addressLabel: "Address",
    itemsLabel: "Items",
    productHeader: "Product",
    quantityHeader: "Quantity",
    priceHeader: "Price (YER)",
    totalHeader: "Total (YER)",
    totalAmountLabel: "Total Amount (YER)",
    printError: "Could not open print window. Please check your browser settings.",
    noOrdersFoundTitle: "No Orders Found",
    noOrdersInStatus: "There are no orders with status \"{status}\".",
    itemsCountLabel: "{count} Items",
    totalLabel: "Total (YER)",
    andMoreItems: ", and {count} more...",
    viewDetailsButton: "View Details",
    processOrderButton: "Process Order",
    markAsShippedButton: "Mark as Shipped",
    printInvoiceButton: "Print Invoice",
    tabActive: "Active",
    tabPending: "Pending",
    tabProcessing: "Processing",
    tabShipped: "Shipped",
    tabDelivered: "Delivered",
    tabCancelled: "Cancelled",
    tabAll: "All",
    status_pending: "Pending",
    status_processing: "Processing",
    status_shipped: "Shipped",
    status_delivered: "Delivered",
    status_cancelled: "Cancelled",
    approvedByLabel: "Approved by {name} on {time}",
  },
  ownerOrderDetails: {
    invoiceTitle: "Invoice",
    storeName: "MarketSync Store",
    storeAddress: "Your Store Address, City, ZIP",
    storeContact: "<EMAIL> | (*************",
    invoiceTo: "Invoice To:",
    invoiceNumber: "Invoice #",
    orderDate: "Order Date",
    paymentStatus: "Payment Status",
    paymentStatusPaid: "Paid (Example)",
    item: "Item",
    quantity: "Quantity",
    unitPrice: "Unit Price (YER)",
    total: "Total (YER)",
    grandTotal: "Grand Total (YER)",
    thankYou: "Thank you for your business!",
    questionsContact: "Questions? Contact <NAME_EMAIL>",
    footerText: "MarketSync - Modern Supermarket Solutions",
    printError: "Could not open print window. Please check your browser settings.",
    orderNotFoundTitle: "Order Not Found",
    orderNotFoundDesc: "The order with ID \"{orderId}\" could not be found.",
    goBackButton: "Go Back",
    backToOrdersButton: "Back to Orders",
    orderIdLabel: "Order ID",
    dateLabel: "Date",
    customerDetailsTitle: "Customer Details",
    nameLabel: "Name",
    addressLabel: "Address",
    paymentStatusTitle: "Payment & Status",
    totalAmountLabel: "Total Amount (YER)",
    paymentStatusLabel: "Payment Status",
    paymentStatusMock: "Paid (Mock)",
    orderStatusLabel: "Order Status",
    orderItemsTitle: "Order Items",
    imageHeader: "Image",
    productHeader: "Product",
    quantityHeader: "Quantity",
    unitPriceHeader: "Unit Price (YER)",
    subtotalHeader: "Subtotal (YER)",
    totalLabel: "Total (YER)",
    printInvoiceButton: "Print Invoice",
    processOrderButton: "Process Order",
    markAsShippedButton: "Mark as Shipped",
    modifyOrderButton: "Modify Order",
    contactCustomerButton: "Contact Customer",
    contactingCustomerPlaceholder: "Simulating contact with {customerName}... (Feature under development)",
    status_pending: "Pending",
    status_processing: "Processing",
    status_shipped: "Shipped",
    status_delivered: "Delivered",
    status_cancelled: "Cancelled",
  },
  employeeTransactions: {
    title: "Record Transactions",
    description: "Manage customer payments and record sales transactions.",
    newTransactionTitle: "New Transaction",
    newTransactionDescription: "Record a new payment or sale.",
    orderIdLabel: "Order ID / Reference",
    orderIdPlaceholder: "e.g., ORD12345 or Walk-in",
    orderIdPlaceholderOptional: "e.g., ORD12345 (Optional, auto-generated if blank)",
    amountLabel: "Amount (YER)",
    paymentMethodLabel: "Payment Method",
    paymentMethodPlaceholder: "Select method",
    paymentMethodCash: "Cash",
    paymentMethodAlKuraimiBank: "Al-Kuraimi Bank",
    paymentMethodEWallet: "E-Wallet Transfer",
    recordTransactionButton: "Record Transaction", // General, will be replaced
    recordCashSaleButton: "Record Cash Sale",
    recordCreditSaleButton: "Record Credit Sale (as Debt)",
    printReceiptButton: "Print Receipt",
    recentTransactionsTitle: "Recent Transactions",
    recentTransactionsDescription: "A list of recently recorded transactions.",
    viewFullLogButton: "View Full Log",
    searchTransactionsPlaceholder: "Search transactions by Order ID or Customer...",
    noRecentTransactions: "No recent transactions.",
    tableHeaderOrderId: "Order ID",
    tableHeaderCustomer: "Customer",
    tableHeaderAmount: "Amount (YER)",
    tableHeaderMethod: "Method", // To be changed to Method/Type
    tableHeaderMethodOrType: "Method / Type",
    tableHeaderTime: "Time",
    tableHeaderStatus: "Status",
    statusCompleted: "Completed",
    statusPending: "Pending",
    statusUnpaid: "Unpaid", // For debts
    transactionTypeLabel: "Transaction Type",
    transactionTypePlaceholder: "Select transaction type",
    cashSaleTransactionType: "Cash Sale",
    creditSaleTransactionType: "Credit Sale (Debt)",
    customerNameLabel: "Customer Name (for Credit Sale)",
    customerNameCashLabel: "Customer Name (Optional for Cash Sale)",
    customerNamePlaceholder: "Enter customer's full name",
    customerNameCashPlaceholder: "Optional: Enter customer name",
    notesPlaceholder: "Transaction notes (e.g. items purchased for credit)",
    errorOwnerNotAssociated: "Employee not associated with an owner. Cannot record transactions.",
    errorInvalidAmount: "Invalid amount entered. Amount must be greater than zero.",
    errorPaymentMethodRequired: "Payment method is required for cash sales.",
    errorPermissionDeniedCredit: "You do not have permission to grant credit.",
    permissionDenied: "Permission Denied",
    errorCustomerNameRequired: "Customer name is required for credit sales.",
    successCashSaleRecorded: "Cash sale recorded successfully.",
    successCreditSaleRecorded: "Credit sale for {customerName} recorded as debt.",
    creditSaleItemName: "Credit Sale (Ref: {orderId})",
    defaultDebtNote: "Credit sale recorded via transaction page. Order/Ref: {orderId}",
    receiptTitle: "Transaction Receipt",
    transactionIdLabel: "Transaction ID",
    receiptFooterThanks: "Thank you for your business!",
    noTransactionToPrint: "No transaction selected or recorded to print.",
  },
  wholesalerProducts: {
    title: "Manage Products",
    description: "List, edit, and manage products you offer for wholesale.",
    addNewProductButton: "Add New Product",
    productListingsTitle: "Your Product Listings",
    productListingsDesc: "Oversee your wholesale product catalog.",
    filterPlaceholder: "Filter products by name, SKU, or category...",
    sortByButton: "Sort By",
    noProductsMessage: "You have not listed any products yet or no products match filter. Click \"Add New Product\" to get started.",
    headerImage: "Image",
    headerName: "Name",
    headerCategory: "Category",
    headerPricePerUnit: "Price/Unit (YER)",
    headerMinQty: "Min. Qty",
    headerStock: "Stock",
    headerAvailability: "Availability",
    headerVisibility: "Visibility",
    openMenu: "Open menu",
    menuEditProduct: "Edit Product",
    menuMakePrivate: "Make Private",
    menuMakePublic: "Make Public",
    menuViewAnalytics: "View Analytics",
    menuDeleteProduct: "Delete Product",
    analyticsTitle: "Analytics",
    analyticsDesc: "Analytics for this product (not implemented).",
    userNotAuthenticated: "User not authenticated.",
    newProductDefaultName: "New Wholesale Product",
    defaultCategory: "Wholesale Bulk",
    productAddedTitle: "Product Added",
    productAddedDesc: "New wholesale product listed.",
    editProductTitle: "Edit Product",
    editProductDesc: "Editing for {productName}. Full edit form not implemented here yet.",
    productDeletedTitle: "Product Deleted",
    productDeletedDesc: "Product listing removed.",
    visibilityChangedTitle: "Visibility Changed",
    visibilityChangedDesc: "{productName} is now {status}.",
    statusPublic: "Public",
    statusPrivate: "Private",
    stockStatusAvailable: "Available",
    stockStatusLowStock: "Low Stock",
    stockStatusUnavailable: "Unavailable",
    visibilityPublic: "Public",
    visibilityPrivate: "Private",
  },
  backupManagement: {
    title: "Backup Management",
    description: "Manage and create backups of critical application data.",
    createBackup: "Create Backup",
    lastBackupLabel: "Last Backup:",
    backupInProgress: "Backup in progress...",
    backupSuccessful: "Backup created successfully on {timestamp}.",
    backupFailed: "Backup failed. Please try again.",
    noBackupPerformed: "No backup has been performed yet.",
    backupDataFor: "This will backup data for Owners and Wholesalers (Products, Debts, etc.).",
    backupNow: "Backup Now",
    backupDetailsTitle: "Backup Details",
    lastBackupTime: "Last Backup Time",
    backupSummary: "Backed up {productCount} products and {debtCount} debt records.",
    backupDataNote: "Note: This is a simulated backup using browser local storage. For production, use a robust backend solution.",
    restoreOperationsTitle: "Restore Operations",
    restoreOperationsDesc: "Restore data from a previous backup.",
    restoreButton: "Restore Last Backup",
    restoreInProgress: "Restoring...",
    restoreSuccessfulTitle: "Restore Successful",
    restoreSuccessfulDesc: "Data successfully restored from backup taken on {timestamp}.",
    restoreFailedTitle: "Restore Failed",
    noBackupToRestore: "No backup found to restore from.",
    restoreWarning: "This will overwrite current data with the last backup. Proceed with caution.",
    noBackupToRestoreNote: "No backup available to restore.",
    // Backup Management Tabs
    manualBackup: "Manual Backup",
    scheduler: "Scheduler",
    history: "History",
    // Backup Scheduler
    backupSchedules: "Backup Schedules",
    automateBackupProcess: "Automate your backup process with scheduled operations",
    addSchedule: "Add Schedule",
    scheduleName: "Schedule Name",
    frequency: "Frequency",
    frequency_daily: "Daily",
    frequency_weekly: "Weekly",
    frequency_monthly: "Monthly",
    time: "Time",
    dayOfWeek: "Day of Week",
    dayOfMonth: "Day of Month",
    dataTypes: "Data Types",
    nextRun: "Next Run",
    active: "Active",
    inactive: "Inactive",
    editSchedule: "Edit Schedule",
    scheduleUpdated: "Schedule Updated",
    scheduleEnabled: "Schedule enabled successfully",
    scheduleDisabled: "Schedule disabled successfully",
    confirmDeleteSchedule: "Are you sure you want to delete this schedule?",
    scheduleDeleted: "Schedule Deleted",
    scheduleDeletedSuccess: "Schedule deleted successfully",
    scheduleSaved: "Schedule Saved",
    scheduleCreated: "Schedule created successfully",
    noSchedulesConfigured: "No backup schedules configured",
    createFirstSchedule: "Create First Schedule",
    notScheduled: "Not scheduled",
    inHours: "in {hours} hours",
    weekday_0: "Sunday",
    weekday_1: "Monday",
    weekday_2: "Tuesday",
    weekday_3: "Wednesday",
    weekday_4: "Thursday",
    weekday_5: "Friday",
    weekday_6: "Saturday",
    // Backup History
    totalBackups: "Total Backups",
    successful: "successful",
    successRate: "Success Rate",
    totalStorage: "Total Storage",
    usedStorage: "used storage",
    averageDuration: "Average Duration",
    perBackup: "per backup",
    backupHistory: "Backup History",
    recentBackupOperations: "Recent backup operations and their status",
    timestamp: "Timestamp",
    type: "Type",
    size: "Size",
    duration: "Duration",
    backupType_manual: "Manual",
    backupType_scheduled: "Scheduled",
    backupType_automatic: "Automatic",
    completed: "Completed",
    failed: "Failed",
    inProgress: "In Progress",
    download: "Download",
    restore: "Restore",
    downloadFailed: "Download Failed",
    cannotDownloadIncompleteBackup: "Cannot download incomplete backup",
    downloadStarted: "Download Started",
    backupDownloadStarted: "Backup {id} download started",
    restoreFailed: "Restore Failed",
    cannotRestoreIncompleteBackup: "Cannot restore incomplete backup",
    confirmRestoreBackup: "Are you sure you want to restore this backup? This will overwrite current data.",
    restoreStarted: "Restore Started",
    backupRestoreStarted: "Backup {id} restore process started",
    confirmDeleteBackup: "Are you sure you want to delete this backup?",
    backupDeleted: "Backup Deleted",
    backupDeletedSuccess: "Backup deleted successfully",
    noBackupHistory: "No backup history available",
  },
  customerDebts: {
    title: "My Debts",
    description: "View your outstanding debts with the store.",
    debtRecordsTitle: "Your Debt Records",
    debtRecordsDescription: "Overview of your current debts.",
    filterPlaceholder: "Filter by date, description, or items...",
    noDebtsFound: "You have no outstanding debts with this store.",
    showingRecordsCount: "Showing {count} debt records.",
    descriptionHeader: "Description/Notes",
    itemsHeader: "Items",
    printStatement: "Print Statement",
    statementOfAccount: "Statement of Account",
    customerNameLabel: "Customer Name",
    dateIssuedLabel: "Date Issued",
    totalOutstandingDebt: "Total Outstanding Debt",
    noDebtsAccount: "This account is not linked to a store for debt tracking or no debts have been recorded.",
  },
  employeeDashboard: {
    title: "Employee Dashboard",
    taskNewOrders: "New Orders to Process",
    taskOrdersToDispatch: "Orders to Dispatch",
    taskPendingTransactions: "Pending Transactions",
    viewTaskAction: "Go to task",
    quickActionsTitle: "Quick Actions",
    quickActionsDesc: "Access your daily tasks quickly.",
    recentActivityTitle: "Recent Activity",
    recentActivityDesc: "Your latest actions and notifications.",
    activityItemApprovedOrder: "Approved Order #{orderId}",
    activityItemRecordedPayment: "Recorded cash payment for Order #{orderId}",
    activityItemDispatchedOrder: "Dispatched Order #{orderId}",
    activityItemNewOrderNotification: "Notification: New high-priority order received.",
  },
  employeeApproveOrders: {
    title: "Approve Customer Orders",
    description: "Review and approve or reject incoming customer orders.",
    pendingApprovalTitle: "Orders Pending Approval",
    pendingApprovalDesc: "Review the details of new orders and take action.",
    filterPlaceholder: "Filter by Order ID or Customer Name...",
    orderId: "Order ID",
    customerName: "Customer",
    orderDate: "Order Date",
    items: "Items",
    totalAmount: "Total Amount (YER)",
    approveButton: "Approve Order",
    rejectButton: "Reject Order",
    noPendingOrders: "There are no orders currently pending approval.",
    orderApprovedTitle: "Order Approved",
    orderApprovedDesc: "Order {orderId} has been approved and moved to processing.",
    orderRejectedTitle: "Order Rejected",
    orderRejectedDesc: "Order {orderId} has been rejected.",
    viewOrderDetails: "View Order Details",
    permissionDenied: "Permission Denied",
    permissionDeniedMessage: "You do not have permission to approve or reject orders.",
  },
  employeeManageBalances: { 
    description: "View and manage customer balances and credit accounts.",
    underDevelopmentTitle: "Manage Customer Balances",
    underDevelopmentMessage: "This feature allows employees to view and potentially adjust customer balances and credit information.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  employeeProcessReturns: { 
    description: "Process customer returns and manage related inventory adjustments.",
    underDevelopmentTitle: "Process Sales Returns",
    underDevelopmentMessage: "This feature will enable employees to handle customer returns, issue refunds or credits, and update inventory accordingly.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  employeeManageProducts: {
    title: "Manage Products",
    description: "Manage product listings, update pricing, and adjust stock details based on permissions.",
    totalProducts: "Total Products",
    lowStockItems: "Low Stock Items",
    outOfStockItems: "Out of Stock Items",
    activeProducts: "Active Products",
    productsListTitle: "Products List",
    productsListDesc: "View and manage available products",
    addProduct: "Add Product",
    editProduct: "Edit Product",
    productDetails: "Product Details",
    searchProductsPlaceholder: "Search products...",
    filterByCategory: "Filter by Category",
    filterByStatus: "Filter by Status",
    allCategories: "All Categories",
    allStatuses: "All Statuses",
    productName: "Product Name",
    productDescription: "Product Description",
    category: "Category",
    price: "Price",
    cost: "Cost",
    stock: "Stock",
    minStock: "Minimum Stock",
    maxStock: "Maximum Stock",
    barcode: "Barcode",
    sku: "SKU",
    supplier: "Supplier",
    status: "Status",
    stockStatus: "Stock Status",
    statusActive: "Active",
    statusInactive: "Inactive",
    statusDiscontinued: "Discontinued",
    inStock: "In Stock",
    lowStock: "Low Stock",
    outOfStock: "Out of Stock",
    lastUpdated: "Last Updated",
    updatedBy: "Updated By",
    addProductTitle: "Add New Product",
    editProductTitle: "Edit Product",
    productAddedSuccess: "Product added successfully",
    productUpdatedSuccess: "Product updated successfully",
    productDeletedSuccess: "Product deleted successfully",
    errorAddingProduct: "Error adding product",
    errorUpdatingProduct: "Error updating product",
    errorDeletingProduct: "Error deleting product",
    noProductsFound: "No products found",
    loadingProducts: "Loading products...",
    confirmDeleteProduct: "Are you sure you want to delete this product?",
    deleteProduct: "Delete Product",
    viewProduct: "View Product",
    duplicateProduct: "Duplicate Product",
    exportProducts: "Export Products",
    importProducts: "Import Products",
    bulkActions: "Bulk Actions",
    selectAll: "Select All",
    selectedItems: "Selected Items",
    categoryManagement: "Category Management",
    addCategory: "Add Category",
    editCategory: "Edit Category",
    deleteCategory: "Delete Category",
    categoryName: "Category Name",
    categoryDescription: "Category Description",
    productCount: "Product Count"
  },
  employeeSetCreditLimits: {
    description: "Set and manage credit limits for approved customers.",
    underDevelopmentTitle: "Set Customer Credit Limits",
    underDevelopmentMessage: "This feature allows authorized employees to define and adjust credit limits for customers.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  employeeGrantCredit: {
    description: "Manage and grant credit to customers.",
    underDevelopmentTitle: "Grant Credit to Customers",
    underDevelopmentMessage: "This feature allows authorized employees to manage credit lines for customers.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  ownerProcessReturns: {
    description: "Process customer returns and manage related inventory adjustments.",
    underDevelopmentTitle: "Process Sales Returns",
    underDevelopmentMessage: "This feature will enable owners to handle customer returns, issue refunds or credits, and update inventory accordingly.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  ownerSetCreditLimits: {
    description: "Set and manage credit limits for approved customers.",
    underDevelopmentTitle: "Set Customer Credit Limits",
    underDevelopmentMessage: "This feature allows owners to define and adjust credit limits for customers.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  ownerOnlineStore: {
    title: "Manage Online Store",
    description: "Control which products are visible to customers in your online shop.",
    productVisibilityTitle: "Product Online Visibility",
    productVisibilityDesc: "Toggle products on or off for your e-commerce storefront. Changes are saved automatically. Out-of-stock products cannot be made online.",
    filterPlaceholder: "Filter by name or category...",
    visibilityStatusPlaceholder: "Visibility Status",
    statusAllProducts: "All Products",
    statusOnline: "Online",
    statusOffline: "Offline",
    noProductsMatchFilters: "No products match your current filters.",
    headerImage: "Image",
    headerName: "Name",
    headerCategory: "Category",
    headerStock: "Stock",
    headerOnlineVisibility: "Online Visibility",
    stockStatusOutOfStock: "Out of Stock",
    stockStatusLowStock: "Low Stock",
    stockStatusInStock: "In Stock",
    toggleOnlineStatusAria: "Toggle {productName} online status",
    showingProductsCount: "Showing {count} of {total} products.",
    confirmSettingsButton: "Confirm Settings",
    toastVisibilityUpdatedTitle: "Visibility Updated",
    toastVisibilityUpdatedDesc: "Product {productName} is now {status}.",
    toastSettingsConfirmedTitle: "Settings Confirmed",
    toastSettingsConfirmedDesc: "Online store product visibility settings are up-to-date."
  },
  ownerPredictions: {
    title: "Smart Inventory Predictions",
    description: "Utilize AI to forecast demand and optimize your stock levels.",
    formTitle: "Predict Demand",
    formDescription: "Enter product details and historical data to get demand predictions and order recommendations.",
    howItWorksTitle: "How it Works",
    howItWorksP1: "Our AI model analyzes historical sales data, current stock levels, lead times, and seasonal trends to provide accurate demand forecasts.",
    howItWorksP2: "This helps you reduce overstocking, prevent stockouts, and optimize your inventory management for better profitability.",
    howItWorksNoteTitle: "Note",
    howItWorksNoteP: "Predictions are based on the data provided. The more accurate and comprehensive your data, the better the predictions."
  },
  inventoryPredictionForm: {
    productCategoryLabel: "Product Category",
    productCategoryPlaceholder: "e.g., Dairy Products",
    productCategoryDescription: "The category of the product you want to predict demand for.",
    historicalSalesDataLabel: "Historical Sales Data (JSON)",
    historicalSalesDataPlaceholder: '[{"date": "YYYY-MM-DD", "quantitySold": 0, "price": 0.00}]',
    historicalSalesDataDescription: "Provide historical sales data in JSON format.",
    currentStockLevelLabel: "Current Stock Level",
    currentStockLevelPlaceholder: "e.g., 100",
    leadTimeDaysLabel: "Lead Time (Days)",
    leadTimeDaysPlaceholder: "e.g., 7",
    leadTimeDaysDescription: "Time it takes for new stock to arrive.",
    seasonalTrendsLabel: "Seasonal Trends (JSON, Optional)",
    seasonalTrendsPlaceholder: 'e.g., [{"period": "Summer", "impactFactor": 1.2}]',
    seasonalTrendsDescription: "Any known seasonal trends affecting demand, in JSON format.",
    getPredictionButton: "Get Prediction",
    toastPredictionGeneratedTitle: "Prediction Generated",
    toastPredictionGeneratedDesc: "Demand forecast for {productCategory} is ready.",
    toastPredictionErrorTitle: "Prediction Error",
    toastPredictionErrorDesc: "Failed to generate prediction. Please check your input or try again later.",
    toastUnexpectedError: "An unexpected error occurred.",
    predictionResultTitle: "Prediction Result for: {productCategory}",
    predictedDemandLabel: "Predicted Demand",
    recommendedOrderQuantityLabel: "Recommended Order Quantity",
    confidenceLevelLabel: "Confidence Level",
    explanationLabel: "Explanation",
    unitsLabel: "units"
  },
  ownerSalesReports: {
    title: "Sales Reports",
    description: "Analyze sales data, revenue, and trends for your supermarket.",
    overviewTitle: "Sales Overview",
    overviewDescription: "Key metrics and performance indicators for your sales.",
    detailedReportsTitle: "Detailed Reports",
    detailedReportsDescription: "Generate and view detailed sales reports for specific periods or products.",
    comingSoon: "Detailed reporting features are coming soon.",
    totalSales: "Total Sales",
    totalOrders: "Total Orders",
    averageOrderValue: "Average Order Value",
    fromLastMonth: "from last month",
    generateDailyReport: "Generate Daily Report",
    generateWeeklyReport: "Generate Weekly Report",
    generateMonthlyReport: "Generate Monthly Report",
    generateCustomReport: "Generate Custom Report",
    bestSellingProducts: "Best Selling Products",
    bestSellingProductsDescription: "Top products based on sales volume or revenue.",
    salesCount: "Sales Count",
    revenueGenerated: "Revenue (YER)",
    reportGenerationInProgressTitle: "Report Generation In Progress",
    reportGenerationInProgressDesc: "Your {reportType} is being generated. This feature is a placeholder.",
    customReportsTitle: "Custom Reports",
    customReportsDescription: "Generate tailored reports based on specific criteria and data points.",
    customReportsComingSoon: "Custom report generation functionality will be available here.",
  },
  employeeTasks: { 
    title: "Task Management",
    description: "View and manage your assigned tasks and responsibilities.",
    underDevelopmentTitle: "Task Management System",
    underDevelopmentMessage: "This section will provide a detailed interface for employees to view, track, and update their assigned tasks. Features will include task prioritization, status updates, and notes.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  employeeCommunication: {
    title: "Communication Center",
    description: "Communicate with customers or other team members regarding orders and tasks.",
    contactsTitle: "Contacts",
    allContacts: "All Contacts",
    customers: "Customers",
    employees: "Employees",
    suppliers: "Suppliers",
    searchContacts: "Search contacts...",
    selectContactToStart: "Select a contact to start messaging",
    typeMessage: "Type your message...",
    sendMessage: "Send Message",
    messageSent: "Message Sent",
    messageSentTo: "Message sent to {name}",
    failedToSend: "Failed to send message",
    online: "Online",
    offline: "Offline",
    lastSeen: "Last seen",
    unreadMessages: "Unread messages",
    orderInquiry: "Order inquiry",
    complaint: "Complaint",
    support: "Support",
    generalMessage: "General message",
    messageStatus: {
      sent: "Sent",
      delivered: "Delivered",
      read: "Read"
    },
    contactTypes: {
      customer: "Customer",
      employee: "Employee",
      supplier: "Supplier"
    },
    noContactsFound: "No contacts found",
    loadingContacts: "Loading contacts...",
    loadingMessages: "Loading messages...",
    callContact: "Call",
    emailContact: "Email",
    newConversation: "New conversation",
    markAsRead: "Mark as read",
    deleteConversation: "Delete conversation",
    blockContact: "Block contact",
    reportContact: "Report contact"
  },
  employeeNotifications: {
    title: "Notifications",
    description: "Stay updated with important alerts, tasks, and system messages.",
    refresh: "Refresh",
    refreshSuccess: "Refreshed",
    notificationsUpdated: "Notifications have been updated.",
    loadError: "Load Error",
    failedToLoadNotifications: "Failed to load notifications. Please try again.",

    // Notification stats and actions
    notificationStats: "{unread} unread of {total} total notifications",
    unread: "Unread",
    read: "Read",
    markAsRead: "Mark as read",
    markAllAsRead: "Mark all as read",
    deleteNotification: "Delete notification",
    clearAll: "Clear all",
    clearAllFilters: "Clear all filters",

    // Filter and search
    searchPlaceholder: "Search notifications...",
    filterByType: "Filter by type",
    filterByPriority: "Filter by priority",
    filterByStatus: "Filter by status",
    allTypes: "All types",
    allPriorities: "All priorities",
    allStatuses: "All statuses",
    sort: "Sort",
    sortBy: "Sort by",
    sortByDate: "Date",
    sortByPriority: "Priority",
    sortByType: "Type",
    sortOrder: "Sort order",
    newestFirst: "Newest first",
    oldestFirst: "Oldest first",
    activeFilters: "Active filters",
    search: "Search",
    type: "Type",
    priority: "Priority",
    status: "Status",
    clearFilters: "Clear filters",

    // Notification types
    type_info: "Information",
    type_warning: "Warning",
    type_error: "Error",
    type_success: "Success",
    type_system: "System",
    type_order: "Order",
    type_task: "Task",
    type_alert: "Alert",

    // Priority levels
    priority_low: "Low",
    priority_medium: "Medium",
    priority_high: "High",
    priority_urgent: "Urgent",

    // Empty states
    noNotifications: "No notifications",
    noNotificationsMessage: "You don't have any notifications yet. New alerts and updates will appear here.",
    noFilteredNotifications: "No matching notifications",
    noFilteredNotificationsMessage: "No notifications match your current filters. Try adjusting your search criteria.",

    // Actions and feedback
    markedAsRead: "Marked as read",
    notificationMarkedAsRead: "Notification has been marked as read.",
    allMarkedAsRead: "All marked as read",
    allNotificationsMarkedAsRead: "All notifications have been marked as read.",
    notificationDeleted: "Notification deleted",
    notificationDeletedSuccess: "Notification has been deleted successfully.",
    notificationsCleared: "Notifications cleared",
    notificationsClearedSuccess: "{count} notifications have been cleared.",
    showingNotifications: "Showing {count} of {total} notifications",

    // Notification details
    expiresAt: "Expires {date}",
  },
  employeeProductLookup: { 
    title: "Product Lookup",
    description: "Quickly find information about products, including price and stock levels.",
    underDevelopmentTitle: "Product Information Access",
    underDevelopmentMessage: "This feature will allow employees to search or scan products to get details like current price, stock availability, and other relevant information.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  employeeShifts: { 
    title: "Shift Management",
    description: "View your work schedule, and manage shift-related activities.",
    underDevelopmentTitle: "Shift Management System",
    underDevelopmentMessage: "Employees will be able to view their upcoming shifts, request changes (if applicable), and clock in/out. Supervisors may have additional management capabilities.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  employeePerformance: { 
    title: "Performance Reports",
    description: "View your individual performance metrics and reports.",
    underDevelopmentTitle: "Employee Performance Overview",
    underDevelopmentMessage: "This section will display key performance indicators for employees, such as orders processed, customer satisfaction scores (if applicable), and other relevant metrics.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  employeeReturnsManagement: {
    title: "Returns Management",
    description: "Process customer returns, manage refunds, and update inventory.",
    // Main interface
    pendingReturns: "Pending Returns",
    allReturns: "All Returns",
    newReturn: "New Return",
    searchPlaceholder: "Search by return ID, customer name, or order ID...",
    filterByStatus: "Filter by Status",
    filterByReason: "Filter by Reason",
    allStatuses: "All Statuses",
    allReasons: "All Reasons",

    // Return statuses
    statusPending: "Pending",
    statusApproved: "Approved",
    statusRejected: "Rejected",
    statusProcessed: "Processed",
    statusRefunded: "Refunded",

    // Return reasons
    reasonDefective: "Defective Product",
    reasonWrongItem: "Wrong Item",
    reasonNotSatisfied: "Customer Not Satisfied",
    reasonDamagedShipping: "Damaged During Shipping",
    reasonExpired: "Expired Product",
    reasonOther: "Other",

    // Refund methods
    refundCash: "Cash Refund",
    refundCredit: "Account Credit",
    refundExchange: "Exchange",
    refundStoreCredit: "Store Credit",

    // Table headers
    returnId: "Return ID",
    customerName: "Customer Name",
    orderId: "Order ID",
    returnDate: "Return Date",
    totalAmount: "Total Amount",
    status: "Status",
    actions: "Actions",

    // Actions
    view: "View",
    process: "Process",
    approve: "Approve",
    reject: "Reject",
    refund: "Refund",

    // Return details
    returnDetails: "Return Details",
    customerInfo: "Customer Information",
    orderInfo: "Order Information",
    returnItems: "Returned Items",
    returnReason: "Return Reason",
    returnReasonDetails: "Reason Details",
    refundMethod: "Refund Method",
    processedBy: "Processed By",
    processedAt: "Processed At",
    approvedBy: "Approved By",
    notes: "Notes",
    attachments: "Attachments",

    // Item details
    productName: "Product Name",
    originalQuantity: "Original Quantity",
    returnQuantity: "Return Quantity",
    originalPrice: "Original Price",
    returnAmount: "Return Amount",
    condition: "Product Condition",
    itemReason: "Item Return Reason",

    // Product conditions
    conditionExcellent: "Excellent",
    conditionGood: "Good",
    conditionDamaged: "Damaged",
    conditionDefective: "Defective",

    // Process return form
    processReturn: "Process Return",
    approveReturn: "Approve Return",
    rejectReturn: "Reject Return",
    refundAmount: "Refund Amount",
    restockingFee: "Restocking Fee",
    finalRefundAmount: "Final Refund Amount",
    processingNotes: "Processing Notes",
    confirmAction: "Confirm Action",
    cancel: "Cancel",

    // New return form
    createNewReturn: "Create New Return",
    selectOrder: "Select Order",
    selectCustomer: "Select Customer",
    orderNotFound: "Order not found",
    selectProducts: "Select Products to Return",
    enterQuantity: "Enter Quantity",
    selectCondition: "Select Product Condition",
    enterReason: "Enter Return Reason",
    selectRefundMethod: "Select Refund Method",
    additionalNotes: "Additional Notes",
    submitReturn: "Submit Return",

    // Messages
    noReturnsFound: "No Returns Found",
    noReturnsMessage: "No returns found matching the specified criteria.",
    returnProcessedSuccess: "Return processed successfully",
    returnApprovedSuccess: "Return approved successfully",
    returnRejectedSuccess: "Return rejected successfully",
    returnCreatedSuccess: "Return created successfully",
    errorProcessingReturn: "Error processing return",
    errorCreatingReturn: "Error creating return",

    // Validation messages
    quantityRequired: "Quantity is required",
    quantityInvalid: "Invalid quantity",
    reasonRequired: "Reason is required",
    conditionRequired: "Product condition is required",
    refundMethodRequired: "Refund method is required",

    // Statistics
    totalReturns: "Total Returns",
    pendingCount: "Pending",
    processedCount: "Processed",
    refundedAmount: "Refunded Amount",
    thisMonth: "This Month",

    // Permissions
    permissionDenied: "Permission Denied",
    permissionDeniedMessage: "You don't have permission to process returns.",

    // Exchange items
    exchangeItems: "Exchange Items",
    selectExchangeProducts: "Select Exchange Products",
    exchangeQuantity: "Exchange Quantity",
    exchangePrice: "Exchange Price",
    addExchangeItem: "Add Exchange Item",
    removeExchangeItem: "Remove Exchange Item",
    totalExchangeValue: "Total Exchange Value",

    // Return policy
    returnPolicy: "Return Policy",
    maxReturnDays: "Maximum Return Days",
    allowedReasons: "Allowed Reasons",
    requiresApproval: "Requires Approval",
    days: "days",
    yes: "Yes",
    no: "No"
  },
  employeeCustomerBalances: { 
    title: "Customer Balances",
    description: "View and manage customer account balances and credit information.",
    underDevelopmentTitle: "Customer Account Balances",
    underDevelopmentMessage: "This section will allow authorized employees to view customer debt/credit balances, payment history, and manage related account information.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  employeeInventoryReports: { 
    title: "Inventory Reports",
    description: "Access and view reports related to stock levels and inventory movements.",
    underDevelopmentTitle: "Inventory Reporting",
    underDevelopmentMessage: "Employees with appropriate permissions can view various inventory reports, such as stock on hand, low stock alerts, and product movement history.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  employeePrintReports: {
    title: "Print Reports",
    description: "Generate and print various operational reports.",
    underDevelopmentTitle: "Report Printing Facility",
    underDevelopmentMessage: "This section will provide options to generate and print different types of reports relevant to employee tasks, such as daily sales summaries, order lists, etc.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  employeeManageBalances: {
    title: "Manage Balances",
    description: "Manage customer balances, debts, and credit.",
    balanceOverviewTitle: "Balance Overview",
    balanceOverviewDesc: "Comprehensive summary of all customer balances",
    totalCustomers: "Total Customers",
    totalDebt: "Total Debt",
    totalCredit: "Total Credit",
    netBalance: "Net Balance",
    customerBalancesTitle: "Customer Balances",
    customerBalancesDesc: "View and manage individual customer balances",
    customerName: "Customer Name",
    currentBalance: "Current Balance",
    creditLimit: "Credit Limit",
    lastPayment: "Last Payment",
    balanceStatus: "Balance Status",
    statusGood: "Good",
    statusWarning: "Warning",
    statusOverdue: "Overdue",
    statusBlocked: "Blocked",
    viewDetailsButton: "View Details",
    adjustBalanceButton: "Adjust Balance",
    recordPaymentButton: "Record Payment",
    searchCustomerPlaceholder: "Search for customer...",
    filterByStatus: "Filter by Status",
    allStatuses: "All Statuses",
    adjustBalanceTitle: "Adjust Customer Balance",
    adjustBalanceDesc: "Adjust customer balance with reason",
    adjustmentAmount: "Adjustment Amount",
    adjustmentReason: "Adjustment Reason",
    adjustmentType: "Adjustment Type",
    typeDebit: "Debit",
    typeCredit: "Credit",
    reasonSale: "Sale",
    reasonReturn: "Return",
    reasonAdjustment: "Adjustment",
    reasonPayment: "Payment",
    reasonOther: "Other",
    confirmAdjustment: "Confirm Adjustment",
    balanceAdjustedSuccess: "Balance adjusted successfully",
    paymentRecordedSuccess: "Payment recorded successfully",
    errorAdjustingBalance: "Error adjusting balance",
    errorRecordingPayment: "Error recording payment",
    noCustomersFound: "No customers found",
    loadingBalances: "Loading balances...",
    balanceHistory: "Balance History",
    recentTransactions: "Recent Transactions",
    transactionDate: "Transaction Date",
    transactionType: "Transaction Type",
    transactionAmount: "Amount",
    transactionBalance: "Balance After Transaction",
    exportBalances: "Export Balances",
    printReport: "Print Report"
  },
  ownerCustomerManagement: {
    title: "Customer Management",
    description: "Manage your customer database, view purchase history, and engage with your clients.",
    underDevelopmentTitle: "Customer Relationship Management (CRM)",
    underDevelopmentMessage: "This section will provide tools to manage customer information, track their purchase history, segment customers for marketing, and manage loyalty programs.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  ownerAdvancedAnalytics: {
    title: "Advanced Analytics",
    description: "Dive deeper into your sales data, product performance, and customer behavior patterns.",
    underDevelopmentTitle: "Data Analytics Dashboard",
    underDevelopmentMessage: "Explore advanced analytics with customizable charts, trend analysis, and deeper insights into your supermarket's performance.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  ownerMarketingTools: {
    title: "Marketing Tools",
    description: "Create and manage marketing campaigns, promotions, and customer outreach.",
    underDevelopmentTitle: "Campaign Management",
    underDevelopmentMessage: "This section will allow you to create promotional offers, send newsletters, and manage marketing campaigns to attract and retain customers.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  ownerSupplierManagement: {
    title: "Supplier Management",
    description: "Manage your suppliers, track orders, and monitor pricing and performance.",

    // Main sections
    suppliersDatabase: "Suppliers Database",
    purchaseOrders: "Purchase Orders",
    deliveryTracking: "Delivery Tracking",
    supplierEvaluations: "Supplier Evaluations",
    payments: "Payments",
    reports: "Reports",

    // Suppliers Database
    allSuppliers: "All Suppliers",
    activeSuppliers: "Active Suppliers",
    inactiveSuppliers: "Inactive Suppliers",
    pendingApproval: "Pending Approval",
    blockedSuppliers: "Blocked Suppliers",

    // Supplier Information
    supplierName: "Supplier Name",
    companyName: "Company Name",
    contactPerson: "Contact Person",
    email: "Email",
    phone: "Phone",
    address: "Address",
    city: "City",
    country: "Country",
    taxId: "Tax ID",
    businessLicense: "Business License",
    category: "Category",
    products: "Products",
    paymentTerms: "Payment Terms",
    creditLimit: "Credit Limit",
    currentBalance: "Current Balance",
    rating: "Rating",
    status: "Status",
    dateAdded: "Date Added",
    lastOrderDate: "Last Order Date",
    totalOrders: "Total Orders",
    totalPurchaseAmount: "Total Purchase Amount",
    averageDeliveryTime: "Average Delivery Time",
    qualityRating: "Quality Rating",
    reliabilityRating: "Reliability Rating",
    priceCompetitiveness: "Price Competitiveness",
    notes: "Notes",

    // Supplier Status
    statusActive: "Active",
    statusInactive: "Inactive",
    statusBlocked: "Blocked",
    statusPendingApproval: "Pending Approval",

    // Actions
    addSupplier: "Add New Supplier",
    editSupplier: "Edit Supplier",
    viewSupplier: "View Supplier",
    deleteSupplier: "Delete Supplier",
    blockSupplier: "Block Supplier",
    unblockSupplier: "Unblock Supplier",
    approveSupplier: "Approve Supplier",
    contactSupplier: "Contact Supplier",
    createPurchaseOrder: "Create Purchase Order",
    viewPurchaseHistory: "View Purchase History",
    evaluateSupplier: "Evaluate Supplier",

    // Purchase Orders
    purchaseOrderNumber: "Purchase Order Number",
    orderDate: "Order Date",
    expectedDeliveryDate: "Expected Delivery Date",
    actualDeliveryDate: "Actual Delivery Date",
    orderStatus: "Order Status",
    priority: "Priority",
    subtotal: "Subtotal",
    taxAmount: "Tax Amount",
    shippingCost: "Shipping Cost",
    totalAmount: "Total Amount",
    paymentStatus: "Payment Status",
    deliveryAddress: "Delivery Address",

    // Purchase Order Status
    statusDraft: "Draft",
    statusSent: "Sent",
    statusConfirmed: "Confirmed",
    statusPartiallyReceived: "Partially Received",
    statusCompleted: "Completed",
    statusCancelled: "Cancelled",

    // Priority Levels
    priorityLow: "Low",
    priorityMedium: "Medium",
    priorityHigh: "High",
    priorityUrgent: "Urgent",

    // Payment Status
    paymentPending: "Pending",
    paymentPartial: "Partial",
    paymentPaid: "Paid",
    paymentOverdue: "Overdue",

    // Purchase Order Items
    productName: "Product Name",
    description: "Description",
    sku: "SKU",
    quantity: "Quantity",
    unitPrice: "Unit Price",
    totalPrice: "Total Price",
    receivedQuantity: "Received Quantity",
    pendingQuantity: "Pending Quantity",
    unit: "Unit",
    specifications: "Specifications",

    // Delivery Tracking
    deliveryDate: "Delivery Date",
    trackingNumber: "Tracking Number",
    carrier: "Carrier",
    deliveryStatus: "Delivery Status",
    statusScheduled: "Scheduled",
    statusInTransit: "In Transit",
    statusDelivered: "Delivered",
    statusDelayed: "Delayed",
    totalItems: "Total Items",
    receivedItems: "Received Items",
    damagedItems: "Damaged Items",
    missingItems: "Missing Items",
    deliveryNotes: "Delivery Notes",
    receivedBy: "Received By",
    inspectedBy: "Inspected By",
    inspectionNotes: "Inspection Notes",

    // Supplier Evaluation
    evaluationDate: "Evaluation Date",
    evaluationPeriod: "Evaluation Period",
    evaluatedBy: "Evaluated By",
    overallRating: "Overall Rating",
    qualityRating: "Quality Rating",
    deliveryRating: "Delivery Rating",
    priceRating: "Price Rating",
    serviceRating: "Service Rating",
    communicationRating: "Communication Rating",
    onTimeDeliveryRate: "On-Time Delivery Rate",
    qualityDefectRate: "Quality Defect Rate",
    orderAccuracyRate: "Order Accuracy Rate",
    responseTime: "Response Time",
    strengths: "Strengths",
    weaknesses: "Weaknesses",
    improvementSuggestions: "Improvement Suggestions",
    actionItems: "Action Items",
    nextEvaluationDate: "Next Evaluation Date",

    // Forms
    supplierForm: "Supplier Form",
    purchaseOrderForm: "Purchase Order Form",
    evaluationForm: "Evaluation Form",
    requiredField: "Required field",
    invalidEmail: "Invalid email",
    invalidPhone: "Invalid phone number",
    save: "Save",
    cancel: "Cancel",
    submit: "Submit",
    approve: "Approve",
    reject: "Reject",

    // Search and Filters
    searchSuppliers: "Search suppliers...",
    filterByCategory: "Filter by Category",
    filterByStatus: "Filter by Status",
    filterByRating: "Filter by Rating",
    sortBy: "Sort By",
    sortByName: "Name",
    sortByRating: "Rating",
    sortByLastOrder: "Last Order",
    sortByTotalAmount: "Total Amount",

    // Statistics
    totalSuppliersCount: "Total Suppliers",
    activeSuppliersCount: "Active Suppliers",
    totalPurchaseOrders: "Total Purchase Orders",
    pendingOrders: "Pending Orders",
    averageRating: "Average Rating",
    totalSpent: "Total Spent",

    // Messages
    supplierAdded: "Supplier added successfully",
    supplierUpdated: "Supplier updated successfully",
    supplierDeleted: "Supplier deleted successfully",
    supplierBlocked: "Supplier blocked successfully",
    supplierUnblocked: "Supplier unblocked successfully",
    supplierApproved: "Supplier approved successfully",
    purchaseOrderCreated: "Purchase order created successfully",
    purchaseOrderUpdated: "Purchase order updated successfully",
    evaluationSaved: "Evaluation saved successfully",
    noSuppliersFound: "No suppliers found",
    noPurchaseOrdersFound: "No purchase orders found",
    loadingSuppliers: "Loading suppliers...",
    loadingPurchaseOrders: "Loading purchase orders...",

    // Export
    exportSuppliers: "Export Suppliers",
    exportPurchaseOrders: "Export Purchase Orders",
    exportEvaluations: "Export Evaluations",
    exportToCSV: "Export to CSV",
    exportToPDF: "Export to PDF",

    // Bank Details
    bankDetails: "Bank Details",
    bankName: "Bank Name",
    accountNumber: "Account Number",
    routingNumber: "Routing Number",
    swiftCode: "SWIFT Code",

    // Additional Fields
    certifications: "Certifications",
    minimumOrderAmount: "Minimum Order Amount",
    leadTime: "Lead Time",
    returnPolicy: "Return Policy",
    warrantyTerms: "Warranty Terms",
    currency: "Currency",
    exchangeRate: "Exchange Rate",
    discountAmount: "Discount Amount",
    discountPercentage: "Discount Percentage",

    // Days
    days: "days",
    hours: "hours",

    // Condition
    conditionGood: "Good",
    conditionDamaged: "Damaged",
    conditionExpired: "Expired",
    conditionDefective: "Defective"
  },

  // Biometric Authentication
  biometricAuth: {
    title: "Biometric Authentication",
    description: "Use fingerprint or face recognition for secure and fast login",

    // Setup
    setupTitle: "Setup Biometric Authentication",
    setupDescription: "Enable biometric authentication for faster and more secure login",
    registerBiometric: "Register New Biometric",
    biometricSetup: "Biometric Setup",

    // Login
    loginWithBiometric: "Login with Biometric",
    biometricLoginButton: "Login with Biometric",
    verifyingBiometric: "Verifying biometric...",
    registering: "Registering...",

    // Status
    supported: "Supported",
    notSupported: "Not Supported",
    available: "Available",
    notAvailable: "Not Available",
    active: "Active",
    inactive: "Inactive",

    // Device Types
    touchId: "Touch ID",
    faceId: "Face ID",
    windowsHello: "Windows Hello",
    fingerprint: "Fingerprint",
    biometric: "Biometric Authentication",

    // Messages
    registrationSuccess: "Biometric authentication enabled successfully",
    registrationFailed: "Failed to enable biometric authentication",
    loginSuccess: "Logged in with biometric authentication",
    loginFailed: "Biometric login failed",
    verificationFailed: "Biometric verification failed",
    noCredentials: "No biometric credentials found",
    credentialDeleted: "Biometric credential deleted",
    deleteFailed: "Failed to delete biometric credential",

    // Errors
    notSupportedError: "Your browser or device does not support biometric authentication",
    platformNotAvailable: "Platform authenticator not available",
    registrationError: "Error occurred during biometric registration",
    authenticationError: "Error occurred during authentication",
    unexpectedError: "An unexpected error occurred",

    // Credentials Management
    credentialsTitle: "Registered Biometrics",
    credentialsDescription: "Manage biometric credentials registered on your devices",
    deviceName: "Device Name",
    registrationDate: "Registration Date",
    lastUsed: "Last Used",
    neverUsed: "Never Used",
    status: "Status",
    actions: "Actions",
    delete: "Delete",
    unknownDevice: "Unknown Device",

    // Security
    securityNotice: "Security Notice",
    securityDescription: "Biometric data is stored securely on your device and is never sent to our servers. Only encryption keys are used to verify your identity.",

    // Browser Support
    browserSupport: "Browser Support",
    platformAuthenticator: "Platform Authenticator",
    registeredFingerprints: "Registered Biometrics",

    // Instructions
    instructionsTitle: "How to Use",
    instructionsSteps: [
      "Click 'Register New Biometric'",
      "Follow the on-screen instructions",
      "Place your finger on the sensor or use Face ID",
      "After registration, you can use biometric to login"
    ],

    // Compatibility
    compatibilityTitle: "Compatibility",
    compatibilityDescription: "Biometric authentication works with:",
    compatibleDevices: [
      "iPhone and iPad with Touch ID or Face ID",
      "Mac devices with Touch ID",
      "Windows devices with Windows Hello",
      "Android devices with fingerprint sensor"
    ],

    // Settings
    biometricStatus: "Biometric Status",
    enableBiometric: "Enable Biometric",
    disableBiometric: "Disable Biometric",
    manageBiometric: "Manage Biometric",

    // Buttons
    close: "Close",
    save: "Save",
    cancel: "Cancel",
    enable: "Enable",
    disable: "Disable",
    register: "Register",
    verify: "Verify",
    retry: "Retry",

    // Prompts
    touchSensor: "Touch the fingerprint sensor",
    lookAtCamera: "Look at the camera",
    followInstructions: "Follow the on-screen instructions",

    // Privacy
    privacyTitle: "Privacy and Security",
    privacyPoints: [
      "Your biometric data is not stored on our servers",
      "All biometric data is stored locally on your device",
      "We only use encryption keys for identity verification",
      "You can delete your biometric data at any time"
    ]
  },
  ownerFinancialManagement: {
    title: "Financial Management",
    description: "Track expenses, manage budgets, and generate financial reports for your supermarket.",
    underDevelopmentTitle: "Financial Overview",
    underDevelopmentMessage: "This section will provide tools for basic bookkeeping, expense tracking, budget planning, and generating financial statements.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  ownerLoyaltyProgram: {
    title: "Loyalty Program",
    description: "Create and manage a customer loyalty program to reward repeat customers.",
    underDevelopmentTitle: "Loyalty Program Setup",
    underDevelopmentMessage: "Design and manage your customer loyalty program, including point systems, rewards, and member tracking.",
    featureComingSoon: "This functionality is currently under development and will be available soon."
  },
  adminAlerts: {
    title: "Send System Alerts",
    description: "Compose and dispatch important notifications and updates to users.",
    formTitle: "Create New Alert",
    formDescription: "Select the target audience and compose your alert message.",
    targetRoleLabel: "Target Audience",
    selectTargetRolePlaceholder: "Select target role",
    targetRoleAll: "All Users",
    notificationTitleLabel: "Alert Title",
    notificationTitlePlaceholder: "e.g., Scheduled System Maintenance",
    notificationMessageLabel: "Alert Message",
    notificationMessagePlaceholder: "Enter the details of the alert here...",
    sendButton: "Send Alert",
    sendingButton: "Sending...",
    notificationSentTitle: "Alert Sent",
    notificationSentDesc: "Alert to {target} titled \"{title}\" has been dispatched.",
    validationError: "Title and message are required.",
  },
  adminSupport: {
    title: "Support Ticket Management",
    description: "View, manage, and respond to user support tickets.",
    ticketQueueTitle: "Support Ticket Queue",
    ticketQueueDesc: "Review and address pending support requests from users.",
    filterTicketsPlaceholder: "Filter by ticket ID, user, or subject...",
    noTicketsFound: "No support tickets found or matching your filter.",
    ticketIdHeader: "Ticket ID",
    subjectHeader: "Subject",
    userHeader: "User",
    dateHeader: "Date Submitted",
    priorityHeader: "Priority",
    viewRespondButton: "View/Respond",
    resolveButton: "Resolve",
    archivedButton: "Archived",
    ticketResolvedTitle: "Ticket Resolved",
    ticketResolvedDesc: "Ticket {ticketId} has been marked as resolved.",
    open: "Open",
    inprogress: "In Progress",
    closed: "Closed",
    high: "High",
    medium: "Medium",
    low: "Low",
  },
  adminAppSettings: {
    title: "Application Settings",
    description: "Configure global settings and parameters for the MarketSync application.",
    generalConfigTitle: "General Configuration",
    generalConfigDesc: "Manage core application settings.",
    appNameLabel: "Application Name",
    defaultLanguageLabel: "Default Language",
    maintenanceModeLabel: "Maintenance Mode",
    maintenanceModeDesc: "Temporarily disable access for users during maintenance.",
    maxUsersLabel: "Maximum User Limit",
    maxUsersDesc: "Set a global limit for the number of users.",
    securitySettingsTitle: "Security Settings",
    securitySettingsDesc: "Configure security-related parameters.",
    twoFactorAuthLabel: "Enable Two-Factor Authentication (2FA)",
    twoFactorAuthDesc: "Enhance account security for all users.",
    sessionTimeoutLabel: "Session Timeout (minutes)",
    sessionTimeoutPlaceholder: "e.g., 30",
    sessionTimeoutDesc: "Inactive user sessions will automatically log out after this duration.",
    apiIntegrationTitle: "API & Integrations",
    apiIntegrationDesc: "Manage API keys and third-party service integrations.",
    settingsSavedTitle: "Settings Saved",
    settingsSavedDesc: "Application settings have been updated successfully.",
  },
  subscriptionManagement: {
    title: "Manage Subscription",
    description: "View your current subscription details and manage your plan.",
    currentPlan: "Current Plan",
    status: "Status",
    endDate: "Renews/Expires On",
    active: "Active",
    expired: "Expired",
    cancelled: "Cancelled",
    premiumYearly: "Premium Yearly",
    standardMonthly: "Standard Monthly",
    basicMonthly: "Basic Monthly",
    manageBilling: "Manage Billing Details",
    upgradePlan: "Upgrade Plan",
    cancelSubscription: "Cancel Subscription",
    renewSubscription: "Renew Subscription",
    featureUnderDevelopment: "This feature is currently under development and will be available soon.",
    contactSupport: "Contact support for assistance.",
  }
} as const;

    
