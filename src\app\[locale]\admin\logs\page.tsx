
"use client"; 

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FileText, Filter, Download, Calendar as CalendarIcon, BarChart2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import React from 'react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';
import { LogsAnalytics } from '@/components/admin/logs-analytics';
import { SystemMonitor } from '@/components/admin/system-monitor';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';


const mockLogs = [
  { id: 'log001', timestamp: '2024-07-21 10:05:32', level: 'INFO', user: 'admin', action: 'User admin logged in', details: 'IP: *************' },
  { id: 'log002', timestamp: '2024-07-21 10:15:01', level: 'WARN', user: 'system', action: 'High CPU usage detected', details: 'Usage: 95%' },
  { id: 'log003', timestamp: '2024-07-21 11:00:45', level: 'ERROR', user: 'owner001', action: 'Failed to process payment for order #P123', details: 'Gateway timeout' },
  { id: 'log004', timestamp: '2024-07-21 11:30:00', level: 'INFO', user: 'employee001', action: 'Order #CUST567 approved', details: 'Order total: $55.20' },
  { id: 'log005', timestamp: '2024-07-21 12:05:12', level: 'DEBUG', user: 'developer_script', action: 'Database query executed', details: 'Query: SELECT * FROM products' },
];

export default function AdminLogsPage() {
  const [date, setDate] = React.useState<Date | undefined>(new Date());
  const t = useScopedI18n('adminLogs'); 
  const tCommon = useScopedI18n('common'); 
  const { toast } = useToast();

  const getLevelBadgeVariant = (level: string) => {
    switch (level.toUpperCase()) {
      case 'INFO': return 'default';
      case 'WARN': return 'secondary'; 
      case 'ERROR': return 'destructive';
      case 'DEBUG': return 'outline';
      default: return 'outline';
    }
  };

  const handleApplyFilters = () => {
    // In a real app, this would trigger fetching filtered logs or client-side filtering.
    // For this mock, we just show a toast.
    toast({
        title: t('filtersAppliedTitle'),
        description: t('filtersAppliedDesc'),
    });
  };

  const handleExportLogs = () => {
    // Placeholder for actual export functionality
     toast({
        title: t('exportLogsTitle'),
        description: t('exportLogsDescPlaceholder'),
    });
  };
  
  return (
    <AuthenticatedLayout expectedRole="admin">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <FileText className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        {/* Logs Management Tabs */}
        <Tabs defaultValue="logs" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="logs">{t('systemLogs')}</TabsTrigger>
            <TabsTrigger value="analytics">{t('analytics')}</TabsTrigger>
            <TabsTrigger value="monitor">{t('systemMonitor')}</TabsTrigger>
          </TabsList>

          <TabsContent value="logs">
            <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{t('activityLogsTitle')}</CardTitle>
            <CardDescription>
              {t('activityLogsDescription')}
            </CardDescription>
             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-2 mt-4">
                <Input placeholder={t('filterUserActionPlaceholder')} className="lg:col-span-2" />
                <Select defaultValue="all">
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder={t('logLevelSelectPlaceholder')} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">{t('allLevels')}</SelectItem>
                        <SelectItem value="info">{t('levelInfo')}</SelectItem>
                        <SelectItem value="warn">{t('levelWarn')}</SelectItem>
                        <SelectItem value="error">{t('levelError')}</SelectItem>
                        <SelectItem value="debug">{t('levelDebug')}</SelectItem>
                    </SelectContent>
                </Select>
                <Popover>
                    <PopoverTrigger asChild>
                    <Button
                        variant={"outline"}
                        className="w-full justify-start text-left font-normal"
                    >
                        <CalendarIcon className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
                        {date ? format(date, "PPP") : <span>{tCommon('pickADate')}</span>}
                    </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                    <Calendar
                        mode="single"
                        selected={date}
                        onSelect={setDate}
                        initialFocus
                    />
                    </PopoverContent>
                </Popover>
                <Button onClick={handleApplyFilters} className="w-full bg-primary hover:bg-primary/90 text-primary-foreground">
                    <Filter className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('applyFiltersButton')}
                </Button>
             </div>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center mb-4">
                <p className="text-sm text-muted-foreground">{t('showingLogsCount', {count: mockLogs.length.toString()})}</p>
                <div>
                    <Button variant="outline" onClick={handleExportLogs} className="mr-2 rtl:ml-2 rtl:mr-0">
                        <Download className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('exportLogsButton')}
                    </Button>
                    <Button variant="outline" disabled> {/* Placeholder for future analysis tools */}
                        <BarChart2 className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('analyzeLogsButton')}
                    </Button>
                </div>
            </div>
             <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[180px]">{t('timestampHeader')}</TableHead>
                      <TableHead className="w-[80px]">{t('levelHeader')}</TableHead>
                      <TableHead>{t('userSystemHeader')}</TableHead>
                      <TableHead>{t('actionHeader')}</TableHead>
                      <TableHead>{t('detailsHeader')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell className="text-xs">{log.timestamp}</TableCell>
                        <TableCell>
                          <Badge variant={getLevelBadgeVariant(log.level) as any} className="text-xs">
                            {log.level}
                          </Badge>
                        </TableCell>
                        <TableCell>{log.user}</TableCell>
                        <TableCell className="font-medium">{log.action}</TableCell>
                        <TableCell className="text-xs text-muted-foreground">{log.details}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              {mockLogs.length === 0 && (
                <p className="text-center text-muted-foreground mt-4">{t('noLogsFound')}</p>
              )}
          </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics">
            <LogsAnalytics logs={mockLogs} />
          </TabsContent>
        </Tabs>
      </div>
    </AuthenticatedLayout>
  );
}

