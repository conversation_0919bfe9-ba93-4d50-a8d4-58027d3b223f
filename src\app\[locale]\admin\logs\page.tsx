
"use client"; 

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FileText, Filter, Download, Calendar as CalendarIcon, BarChart2, Search, RefreshCw, AlertCircle, Info, AlertTriangle, Bug } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import React from 'react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';
import { LogsAnalytics } from '@/components/admin/logs-analytics';
import { SystemMonitor } from '@/components/admin/system-monitor';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';


const mockLogs = [
  { id: 'log001', timestamp: '2024-07-21 10:05:32', level: 'INFO', user: 'admin', action: 'User admin logged in', details: 'IP: *************', category: 'Authentication' },
  { id: 'log002', timestamp: '2024-07-21 10:15:01', level: 'WARN', user: 'system', action: 'High CPU usage detected', details: 'Usage: 95%', category: 'Performance' },
  { id: 'log003', timestamp: '2024-07-21 11:00:45', level: 'ERROR', user: 'owner001', action: 'Failed to process payment for order #P123', details: 'Gateway timeout', category: 'Payment' },
  { id: 'log004', timestamp: '2024-07-21 11:30:00', level: 'INFO', user: 'employee001', action: 'Order #CUST567 approved', details: 'Order total: $55.20', category: 'Orders' },
  { id: 'log005', timestamp: '2024-07-21 12:05:12', level: 'DEBUG', user: 'developer_script', action: 'Database query executed', details: 'Query: SELECT * FROM products', category: 'Database' },
  { id: 'log006', timestamp: '2024-07-21 12:15:30', level: 'INFO', user: 'customer123', action: 'New user registration', details: 'Email: <EMAIL>', category: 'User Management' },
  { id: 'log007', timestamp: '2024-07-21 12:25:45', level: 'WARN', user: 'system', action: 'Memory usage high', details: 'Usage: 87%', category: 'Performance' },
  { id: 'log008', timestamp: '2024-07-21 12:35:12', level: 'ERROR', user: 'system', action: 'Database connection failed', details: 'Connection timeout after 30s', category: 'Database' },
];

export default function AdminLogsPage() {
  const [date, setDate] = React.useState<Date | undefined>(new Date());
  const [searchTerm, setSearchTerm] = React.useState('');
  const [selectedLevel, setSelectedLevel] = React.useState('all');
  const t = useScopedI18n('adminLogs');
  const tCommon = useScopedI18n('common');
  const { toast } = useToast();

  const getLevelBadgeVariant = (level: string) => {
    switch (level.toUpperCase()) {
      case 'INFO': return 'default';
      case 'WARN': return 'secondary';
      case 'ERROR': return 'destructive';
      case 'DEBUG': return 'outline';
      default: return 'outline';
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level.toUpperCase()) {
      case 'INFO': return Info;
      case 'WARN': return AlertTriangle;
      case 'ERROR': return AlertCircle;
      case 'DEBUG': return Bug;
      default: return Info;
    }
  };

  const filteredLogs = mockLogs.filter(log => {
    const matchesSearch = searchTerm === '' ||
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.details.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesLevel = selectedLevel === 'all' || log.level.toLowerCase() === selectedLevel.toLowerCase();

    return matchesSearch && matchesLevel;
  });

  const logStats = {
    total: mockLogs.length,
    info: mockLogs.filter(log => log.level === 'INFO').length,
    warn: mockLogs.filter(log => log.level === 'WARN').length,
    error: mockLogs.filter(log => log.level === 'ERROR').length,
    debug: mockLogs.filter(log => log.level === 'DEBUG').length,
  };

  const handleApplyFilters = () => {
    // In a real app, this would trigger fetching filtered logs or client-side filtering.
    // For this mock, we just show a toast.
    toast({
        title: t('filtersAppliedTitle'),
        description: t('filtersAppliedDesc'),
    });
  };

  const handleExportLogs = () => {
    // Placeholder for actual export functionality
     toast({
        title: t('exportLogsTitle'),
        description: t('exportLogsDescPlaceholder'),
    });
  };
  
  return (
    <AuthenticatedLayout expectedRole="admin">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <FileText className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        {/* Logs Management Tabs */}
        <Tabs defaultValue="logs" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="logs">{t('systemLogs')}</TabsTrigger>
            <TabsTrigger value="analytics">{t('analytics')}</TabsTrigger>
            <TabsTrigger value="monitor">{t('systemMonitor')}</TabsTrigger>
          </TabsList>

          <TabsContent value="logs" className="space-y-6">
            {/* Log Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="p-4 rounded-lg border bg-card">
                <div className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">إجمالي السجلات</p>
                    <p className="text-2xl font-bold">{logStats.total}</p>
                  </div>
                </div>
              </div>
              <div className="p-4 rounded-lg border bg-card">
                <div className="flex items-center space-x-2">
                  <Info className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">معلومات</p>
                    <p className="text-2xl font-bold">{logStats.info}</p>
                  </div>
                </div>
              </div>
              <div className="p-4 rounded-lg border bg-card">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">تحذيرات</p>
                    <p className="text-2xl font-bold">{logStats.warn}</p>
                  </div>
                </div>
              </div>
              <div className="p-4 rounded-lg border bg-card">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="h-5 w-5 text-red-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">أخطاء</p>
                    <p className="text-2xl font-bold">{logStats.error}</p>
                  </div>
                </div>
              </div>
              <div className="p-4 rounded-lg border bg-card">
                <div className="flex items-center space-x-2">
                  <Bug className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">تصحيح</p>
                    <p className="text-2xl font-bold">{logStats.debug}</p>
                  </div>
                </div>
              </div>
            </div>

            <Card className="shadow-lg border-0">
          <CardHeader className="pb-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-6 w-6 text-primary" />
              <CardTitle className="text-xl">{t('activityLogsTitle')}</CardTitle>
            </div>
            <CardDescription>
              {t('activityLogsDescription')}
            </CardDescription>
             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3 mt-4">
                <div className="relative lg:col-span-2">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={t('filterUserActionPlaceholder')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={selectedLevel} onValueChange={setSelectedLevel}>
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder={t('logLevelSelectPlaceholder')} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">{t('allLevels')}</SelectItem>
                        <SelectItem value="info">{t('levelInfo')}</SelectItem>
                        <SelectItem value="warn">{t('levelWarn')}</SelectItem>
                        <SelectItem value="error">{t('levelError')}</SelectItem>
                        <SelectItem value="debug">{t('levelDebug')}</SelectItem>
                    </SelectContent>
                </Select>
                <Popover>
                    <PopoverTrigger asChild>
                    <Button
                        variant={"outline"}
                        className="w-full justify-start text-left font-normal"
                    >
                        <CalendarIcon className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
                        {date ? format(date, "PPP") : <span>{tCommon('pickADate')}</span>}
                    </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                    <Calendar
                        mode="single"
                        selected={date}
                        onSelect={setDate}
                        initialFocus
                    />
                    </PopoverContent>
                </Popover>
                <Button onClick={handleApplyFilters} className="w-full bg-primary hover:bg-primary/90 text-primary-foreground">
                    <Filter className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('applyFiltersButton')}
                </Button>
             </div>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center mb-4">
                <p className="text-sm text-muted-foreground">{t('showingLogsCount', {count: filteredLogs.length.toString()})}</p>
                <div className="flex gap-2">
                    <Button variant="outline" onClick={() => window.location.reload()} size="sm">
                        <RefreshCw className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> تحديث
                    </Button>
                    <Button variant="outline" onClick={handleExportLogs} size="sm">
                        <Download className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('exportLogsButton')}
                    </Button>
                    <Button variant="outline" disabled size="sm"> {/* Placeholder for future analysis tools */}
                        <BarChart2 className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('analyzeLogsButton')}
                    </Button>
                </div>
            </div>
             <div className="rounded-lg border shadow-sm bg-card">
                <Table>
                  <TableHeader>
                    <TableRow className="border-b bg-muted/50">
                      <TableHead className="w-[180px] font-semibold">{t('timestampHeader')}</TableHead>
                      <TableHead className="w-[100px] font-semibold">{t('levelHeader')}</TableHead>
                      <TableHead className="font-semibold">{t('userSystemHeader')}</TableHead>
                      <TableHead className="font-semibold">{t('actionHeader')}</TableHead>
                      <TableHead className="font-semibold">الفئة</TableHead>
                      <TableHead className="font-semibold">{t('detailsHeader')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredLogs.map((log, index) => {
                      const LevelIcon = getLevelIcon(log.level);
                      return (
                        <TableRow key={log.id} className={`hover:bg-muted/30 transition-colors ${index % 2 === 0 ? 'bg-background' : 'bg-muted/10'}`}>
                          <TableCell className="text-xs font-mono">{log.timestamp}</TableCell>
                          <TableCell>
                            <Badge variant={getLevelBadgeVariant(log.level) as any} className="text-xs font-medium">
                              <LevelIcon className="mr-1 h-3 w-3" />
                              {log.level}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-medium">
                            <div className="flex items-center space-x-2">
                              <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
                                <span className="text-xs font-medium text-primary">
                                  {log.user.charAt(0).toUpperCase()}
                                </span>
                              </div>
                              <span>{log.user}</span>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">{log.action}</TableCell>
                          <TableCell>
                            <Badge variant="outline" className="text-xs">
                              {log.category}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-xs text-muted-foreground max-w-xs truncate" title={log.details}>
                            {log.details}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
              {filteredLogs.length === 0 && (
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-muted-foreground/50" />
                  <p className="text-muted-foreground mt-2">{t('noLogsFound')}</p>
                </div>
              )}
          </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics">
            <LogsAnalytics logs={mockLogs} />
          </TabsContent>
        </Tabs>
      </div>
    </AuthenticatedLayout>
  );
}

