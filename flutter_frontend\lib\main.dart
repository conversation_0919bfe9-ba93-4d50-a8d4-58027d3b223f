import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'l10n/l10n.dart';
import 'ui/screens/login/login_screen.dart';
import 'core/theme/app_theme.dart';

void main() {
  runApp(const MarketSyncApp());
}

class MarketSyncApp extends StatelessWidget {
  const MarketSyncApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MarketSync',
      debugShowCheckedModeBanner: false,
      locale: const Locale('ar'),
      supportedLocales: L10n.all,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: LoginScreen(),
    );
  }
} 