'use client';

import { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Package, DollarSign, Clock, CheckCircle } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import type { Return } from '@/types';

interface ReturnsStatsProps {
  returns: Return[];
}

export function ReturnsStats({ returns }: ReturnsStatsProps) {
  const t = useScopedI18n('employeeReturnsManagement');

  const stats = useMemo(() => {
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    
    // Filter returns for current month
    const thisMonthReturns = returns.filter(returnItem => {
      const returnDate = new Date(returnItem.createdAt);
      return returnDate.getMonth() === currentMonth && returnDate.getFullYear() === currentYear;
    });

    // Filter returns for previous month
    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    const lastMonthReturns = returns.filter(returnItem => {
      const returnDate = new Date(returnItem.createdAt);
      return returnDate.getMonth() === lastMonth && returnDate.getFullYear() === lastMonthYear;
    });

    // Calculate statistics
    const totalReturns = returns.length;
    const pendingReturns = returns.filter(r => r.status === 'pending').length;
    const processedReturns = returns.filter(r => r.status === 'processed' || r.status === 'refunded').length;
    const rejectedReturns = returns.filter(r => r.status === 'rejected').length;
    
    const totalRefundedAmount = returns
      .filter(r => r.refundAmount)
      .reduce((sum, r) => sum + (r.refundAmount || 0), 0);
    
    const thisMonthTotal = thisMonthReturns.length;
    const lastMonthTotal = lastMonthReturns.length;
    const monthlyChange = lastMonthTotal === 0 ? 0 : ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100;

    const thisMonthRefunded = thisMonthReturns
      .filter(r => r.refundAmount)
      .reduce((sum, r) => sum + (r.refundAmount || 0), 0);
    
    const lastMonthRefunded = lastMonthReturns
      .filter(r => r.refundAmount)
      .reduce((sum, r) => sum + (r.refundAmount || 0), 0);
    
    const refundChange = lastMonthRefunded === 0 ? 0 : ((thisMonthRefunded - lastMonthRefunded) / lastMonthRefunded) * 100;

    // Most common return reasons
    const reasonCounts = returns.reduce((acc, returnItem) => {
      acc[returnItem.returnReason] = (acc[returnItem.returnReason] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topReasons = Object.entries(reasonCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3);

    return {
      totalReturns,
      pendingReturns,
      processedReturns,
      rejectedReturns,
      totalRefundedAmount,
      thisMonthTotal,
      monthlyChange,
      thisMonthRefunded,
      refundChange,
      topReasons
    };
  }, [returns]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-YE', {
      style: 'currency',
      currency: 'YER',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getReasonText = (reason: string) => {
    switch (reason) {
      case 'defective': return t('reasonDefective');
      case 'wrong_item': return t('reasonWrongItem');
      case 'not_satisfied': return t('reasonNotSatisfied');
      case 'damaged_shipping': return t('reasonDamagedShipping');
      case 'expired': return t('reasonExpired');
      case 'other': return t('reasonOther');
      default: return reason;
    }
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {/* Total Returns */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('totalReturns')}</CardTitle>
          <Package className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalReturns}</div>
          <div className="flex items-center text-xs text-muted-foreground">
            {stats.monthlyChange >= 0 ? (
              <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
            ) : (
              <TrendingDown className="h-3 w-3 mr-1 text-red-600" />
            )}
            <span className={stats.monthlyChange >= 0 ? 'text-green-600' : 'text-red-600'}>
              {formatPercentage(stats.monthlyChange)}
            </span>
            <span className="mr-1">{t('thisMonth')}</span>
          </div>
        </CardContent>
      </Card>

      {/* Pending Returns */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('pendingCount')}</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.pendingReturns}</div>
          <p className="text-xs text-muted-foreground">
            يتطلب معالجة فورية
          </p>
        </CardContent>
      </Card>

      {/* Processed Returns */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('processedCount')}</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.processedReturns}</div>
          <p className="text-xs text-muted-foreground">
            تم المعالجة بنجاح
          </p>
        </CardContent>
      </Card>

      {/* Refunded Amount */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('refundedAmount')}</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(stats.totalRefundedAmount)}</div>
          <div className="flex items-center text-xs text-muted-foreground">
            {stats.refundChange >= 0 ? (
              <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
            ) : (
              <TrendingDown className="h-3 w-3 mr-1 text-red-600" />
            )}
            <span className={stats.refundChange >= 0 ? 'text-green-600' : 'text-red-600'}>
              {formatPercentage(stats.refundChange)}
            </span>
            <span className="mr-1">{t('thisMonth')}</span>
          </div>
        </CardContent>
      </Card>

      {/* Top Return Reasons */}
      {stats.topReasons.length > 0 && (
        <Card className="md:col-span-2 lg:col-span-4">
          <CardHeader>
            <CardTitle className="text-sm font-medium">أكثر أسباب الإرجاع شيوعاً</CardTitle>
            <CardDescription>
              الأسباب الأكثر تكراراً لإرجاع المنتجات
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {stats.topReasons.map(([reason, count]) => (
                <Badge key={reason} variant="secondary" className="text-sm">
                  {getReasonText(reason)}: {count}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
