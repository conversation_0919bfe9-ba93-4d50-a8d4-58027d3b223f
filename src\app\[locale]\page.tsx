
import { LoginForm } from '@/components/auth/login-form';
import Image from 'next/image';
import { getScopedI18n } from '@/lib/i18n/server';
import type { Locale } from '@/lib/i18n/config';

export default async function LoginPage({ params }: { params: Promise<{ locale: Locale }> }) {
  // استخدام params.locale بشكل صريح، على الرغم من أن getScopedI18n قد يستخدم اللغة الحالية افتراضيًا
  const { locale } = await params;
  const t = await getScopedI18n(locale, 'login');
  const currentYear = new Date().getFullYear();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-teal-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <LoginForm />
    </div>
  );
}
