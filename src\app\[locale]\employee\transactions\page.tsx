
"use client"; 

import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CreditCard, PlusCircle, ListChecks, Printer, DollarSign, UserCircleIcon, BookText } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from '@/components/ui/badge';
import { useScopedI18n } from '@/lib/i18n/client'; 
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import type { Debt, DebtItem } from '@/types';
import { format } from 'date-fns';
import { Textarea } from '@/components/ui/textarea';


const DEBTS_STORAGE_KEY = 'marketSyncOwnerDebts';

const getStoredDebts = (ownerId?: string): Debt[] => {
  if (typeof window === 'undefined') return [];
  const stored = localStorage.getItem(DEBTS_STORAGE_KEY);
  let allDebts: Debt[] = [];
  if (stored) {
    allDebts = JSON.parse(stored);
  }
  return ownerId ? allDebts.filter(d => d.ownerId === ownerId) : allDebts;
};

const saveStoredDebts = (debts: Debt[]) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(DEBTS_STORAGE_KEY, JSON.stringify(debts));
  }
};


// Simplified mock transaction type for this page's display
interface DisplayTransaction {
    id: string;
    orderId: string;
    customer?: string; // Optional for cash sales, required for credit
    amount: number;
    methodOrType: string; // "Card", "Cash", "Credit Sale"
    timestamp: string;
    status: string; // "Completed", "Pending" (for cash), or "Unpaid" (for debt)
    notes?: string; // Added notes
}


const mockRecentTransactions: DisplayTransaction[] = [
    { id: 'TRN001', orderId: 'ORD5821', customer: 'Alice B.', amount: 45.90, methodOrType: 'Card', timestamp: '10:32 AM', status: 'Completed', notes: 'Payment for order 5821' },
    { id: 'TRN002', orderId: 'ORD5822', customer: 'Robert C.', amount: 12.50, methodOrType: 'Cash', timestamp: '10:45 AM', status: 'Completed', notes: 'Quick cash sale' },
    { id: 'TRN003', orderId: 'ORD5823', customer: 'Eve D.', amount: 88.00, methodOrType: 'Card', timestamp: '11:15 AM', status: 'Pending', notes: 'Awaiting confirmation' },
];

export default function EmployeeTransactionsPage() {
  const t = useScopedI18n('employeeTransactions'); 
  const tCommon = useScopedI18n('common');
  const tDebts = useScopedI18n('debtsManagement');
  const { user: authUser } = useAuth();
  const { toast } = useToast();

  const [transactionType, setTransactionType] = useState<'cash' | 'credit'>('cash');
  const [orderId, setOrderId] = useState('');
  const [customerName, setCustomerName] = useState(''); // For credit sales
  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState(''); // For cash sales
  const [notes, setNotes] = useState(''); 

  const [recentTransactions, setRecentTransactions] = useState<DisplayTransaction[]>(mockRecentTransactions);
  const [lastRecordedTransaction, setLastRecordedTransaction] = useState<DisplayTransaction | null>(null);

  const canGrantCredit = authUser?.permissions?.canGrantCredit || false;


  const handleRecordTransaction = () => {
    if (!authUser || !authUser.createdById) {
        toast({ title: tCommon('error'), description: t('errorOwnerNotAssociated'), variant: "destructive" });
        return;
    }

    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
        toast({ title: tCommon('error'), description: t('errorInvalidAmount'), variant: "destructive" });
        return;
    }
    
    let finalOrderId = orderId.trim();
    if (!finalOrderId) {
      finalOrderId = `${transactionType.toUpperCase()}-${Date.now().toString().slice(-6)}`;
    }


    if (transactionType === 'cash') {
        if (!paymentMethod) {
            toast({ title: tCommon('error'), description: t('errorPaymentMethodRequired'), variant: "destructive" });
            return;
        }
        
        const newCashTransaction: DisplayTransaction = {
            id: `CTRN${Date.now().toString().slice(-6)}`,
            orderId: finalOrderId,
            customer: customerName.trim() || undefined,
            amount: numericAmount,
            methodOrType: paymentMethod === 'Cash' ? t('paymentMethodCash') : paymentMethod === 'AlKuraimiBank' ? t('paymentMethodAlKuraimiBank') : t('paymentMethodEWallet'),
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            status: 'Completed',
            notes: notes.trim() || undefined,
        };
        setRecentTransactions(prev => [newCashTransaction, ...prev]);
        setLastRecordedTransaction(newCashTransaction);
        toast({ title: tCommon('success'), description: t('successCashSaleRecorded') });

    } else if (transactionType === 'credit') {
        if (!canGrantCredit) {
            toast({ title: tCommon('error'), description: t('errorPermissionDeniedCredit'), variant: "destructive" });
            return;
        }
        if (!customerName.trim()) {
            toast({ title: tCommon('error'), description: t('errorCustomerNameRequired'), variant: "destructive" });
            return;
        }

        const allDebts = getStoredDebts();
        const debtId = `DEBT${Date.now().toString().slice(-6)}`;
        
        const newDebtItem: DebtItem = {
          productId: `CREDIT_SALE_${finalOrderId}`,
          productName: t('creditSaleItemName', {orderId: finalOrderId}),
          quantity: 1,
          price: numericAmount,
        };

        const newDebt: Debt = {
            id: debtId,
            partyName: customerName.trim(),
            type: 'debt',
            amount: numericAmount,
            date: format(new Date(), 'yyyy-MM-dd'),
            status: 'unpaid',
            notes: notes.trim() || t('defaultDebtNote', {orderId: finalOrderId}),
            items: [newDebtItem],
            ownerId: authUser.createdById,
        };
        
        saveStoredDebts([...allDebts, newDebt]);
        
        const newCreditDisplayTransaction: DisplayTransaction = {
            id: newDebt.id,
            orderId: finalOrderId,
            customer: customerName.trim(),
            amount: numericAmount,
            methodOrType: t('creditSaleTransactionType'),
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            status: 'Unpaid',
            notes: newDebt.notes,
        };
        setRecentTransactions(prev => [newCreditDisplayTransaction, ...prev]);
        setLastRecordedTransaction(newCreditDisplayTransaction);
        toast({ title: tCommon('success'), description: t('successCreditSaleRecorded', { customerName: customerName.trim() }) });
    }

    // Reset form fields
    setOrderId('');
    setCustomerName('');
    setAmount('');
    setPaymentMethod('');
    setNotes('');
  };

  const handlePrintReceipt = (transactionToPrint: DisplayTransaction | null) => {
    if (!transactionToPrint) {
      toast({ title: tCommon('error'), description: t('noTransactionToPrint'), variant: "destructive" });
      return;
    }

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const direction = tCommon('language') === 'Arabic' ? 'rtl' : 'ltr';
      let receiptHTML = `<html><head><title>${t('receiptTitle')}</title><style>`;
      receiptHTML += `
        body { font-family: Arial, sans-serif; margin: 20px; color: #333; direction: ${direction}; font-size: 12px; }
        .receipt-box { max-width: 300px; margin: auto; padding: 15px; border: 1px solid #ccc; }
        .receipt-header { text-align: center; margin-bottom: 10px; }
        .receipt-header h1 { margin: 0; font-size: 1.2em; color: hsl(var(--primary)); }
        .receipt-header p { margin: 1px 0; font-size: 0.8em; }
        .detail-item { display: flex; justify-content: space-between; margin-bottom: 4px; }
        .detail-item strong { font-weight: bold; }
        .total-section { text-align: ${direction === 'rtl' ? 'left' : 'right'}; margin-top: 10px; font-size: 1.1em; font-weight: bold; border-top: 1px dashed #ccc; padding-top: 5px; }
        .notes-section { margin-top: 10px; font-size: 0.9em; border-top: 1px dashed #ccc; padding-top: 5px;}
        .footer { text-align: center; margin-top: 15px; font-size: 0.8em; color: #777; }
      `;
      receiptHTML += `</style></head><body><div class="receipt-box">`;
      receiptHTML += `<div class="receipt-header"><h1>${authUser?.name || tDebts('storeNamePlaceholder')}</h1><p>${t('receiptTitle')}</p></div>`; 
      
      receiptHTML += `<div class="detail-item"><span>${t('transactionIdLabel')}:</span><strong>${transactionToPrint.id}</strong></div>`;
      receiptHTML += `<div class="detail-item"><span>${t('orderIdLabel')}:</span><strong>${transactionToPrint.orderId}</strong></div>`;
      receiptHTML += `<div class="detail-item"><span>${tDebts('date')}:</span><strong>${format(new Date(), "PP")} ${transactionToPrint.timestamp}</strong></div>`;
      if (transactionToPrint.customer) {
        receiptHTML += `<div class="detail-item"><span>${t('tableHeaderCustomer')}:</span><strong>${transactionToPrint.customer}</strong></div>`;
      }
      receiptHTML += `<div class="detail-item"><span>${t('transactionTypeLabel')}:</span><strong>${transactionToPrint.methodOrType}</strong></div>`;
      
      if(transactionToPrint.notes){
        receiptHTML += `<div class="notes-section"><strong>${tDebts('notesOptional').replace(' (Optional)', '').replace('(اختياري)','')}:</strong><p>${transactionToPrint.notes}</p></div>`;
      }

      receiptHTML += `<div class="total-section"><span>${tDebts('totalAmount')}:</span><span>YER ${transactionToPrint.amount.toFixed(2)}</span></div>`;
      
      receiptHTML += `<div class="footer">${t('receiptFooterThanks')}</div>`;
      receiptHTML += `</div></body></html>`;
      printWindow.document.write(receiptHTML);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    } else {
      toast({ title: tCommon('error'), description: tDebts('printWindowError'), variant: 'destructive' });
    }
  };


  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <CreditCard className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>
        
        <div className="grid lg:grid-cols-3 gap-6">
            <Card className="shadow-lg lg:col-span-1">
                <CardHeader>
                    <CardTitle>{t('newTransactionTitle')}</CardTitle>
                    <CardDescription>
                    {t('newTransactionDescription')}
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <Label htmlFor="transactionType">{t('transactionTypeLabel')}</Label>
                        <Select 
                            value={transactionType} 
                            onValueChange={(value: 'cash' | 'credit') => { 
                                if (value === 'credit' && !canGrantCredit) {
                                    toast({ title: tCommon('error'), description: t('errorPermissionDeniedCredit'), variant: "destructive" });
                                    return; 
                                }
                                setTransactionType(value); 
                                setLastRecordedTransaction(null); 
                            }}
                        >
                            <SelectTrigger id="transactionType">
                                <SelectValue placeholder={t('transactionTypePlaceholder')} />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="cash">{t('cashSaleTransactionType')}</SelectItem>
                                <SelectItem value="credit" disabled={!canGrantCredit}>
                                    {t('creditSaleTransactionType')}
                                    {!canGrantCredit && <span className="text-xs text-muted-foreground ltr:ml-2 rtl:mr-2">({t('permissionDenied')})</span>}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div>
                        <Label htmlFor="orderId">{t('orderIdLabel')}</Label>
                        <Input id="orderId" value={orderId} onChange={(e) => setOrderId(e.target.value)} placeholder={t('orderIdPlaceholderOptional')} />
                    </div>

                    {(transactionType === 'credit' || (transactionType === 'cash' && customerName.trim())) && (
                         <div>
                            <Label htmlFor="customerName">{transactionType === 'credit' ? t('customerNameLabel') : t('customerNameCashLabel')}</Label>
                            <Input 
                                id="customerName" 
                                value={customerName} 
                                onChange={(e) => setCustomerName(e.target.value)} 
                                placeholder={transactionType === 'credit' ? t('customerNamePlaceholder') : t('customerNameCashPlaceholder')} 
                            />
                        </div>
                    )}
                    
                     <div>
                        <Label htmlFor="amount">{t('amountLabel')}</Label>
                        <div className="relative">
                            <DollarSign className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground rtl:right-2.5 rtl:left-auto" />
                            <Input id="amount" type="number" value={amount} onChange={(e) => setAmount(e.target.value)} placeholder="0.00" className="pl-8 rtl:pr-8 rtl:pl-3" />
                        </div>
                    </div>

                    {transactionType === 'cash' && (
                        <div>
                            <Label htmlFor="paymentMethod">{t('paymentMethodLabel')}</Label>
                            <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                                <SelectTrigger id="paymentMethod">
                                    <SelectValue placeholder={t('paymentMethodPlaceholder')} />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Cash">{t('paymentMethodCash')}</SelectItem>
                                    <SelectItem value="AlKuraimiBank">{t('paymentMethodAlKuraimiBank')}</SelectItem>
                                    <SelectItem value="EWallet">{t('paymentMethodEWallet')}</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    )}
                    
                    <div>
                        <Label htmlFor="notes">{tDebts('notesOptional')}</Label>
                        <Textarea id="notes" value={notes} onChange={(e) => setNotes(e.target.value)} placeholder={t('notesPlaceholder')} />
                    </div>

                    <Button 
                        onClick={handleRecordTransaction} 
                        className="w-full bg-accent hover:bg-accent/90 text-accent-foreground"
                        disabled={transactionType === 'credit' && !canGrantCredit}
                    >
                        {transactionType === 'cash' ? <PlusCircle className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> : <BookText className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />}
                        {transactionType === 'cash' ? t('recordCashSaleButton') : t('recordCreditSaleButton')}
                    </Button>
                     <Button 
                        variant="outline" 
                        className="w-full" 
                        onClick={() => handlePrintReceipt(lastRecordedTransaction)}
                        disabled={!lastRecordedTransaction}
                      >
                        <Printer className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('printReceiptButton')}
                    </Button>
                </CardContent>
            </Card>

            <Card className="shadow-lg lg:col-span-2">
                <CardHeader>
                    <div className="flex justify-between items-start">
                        <div>
                            <CardTitle>{t('recentTransactionsTitle')}</CardTitle>
                            <CardDescription>
                            {t('recentTransactionsDescription')}
                            </CardDescription>
                        </div>
                        <Button variant="outline" size="sm">
                            <ListChecks className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('viewFullLogButton')}
                        </Button>
                    </div>
                     <Input placeholder={t('searchTransactionsPlaceholder')} className="mt-4" />
                </CardHeader>
                <CardContent>
                    {recentTransactions.length === 0 ? (
                        <p className="text-muted-foreground text-center py-4">{t('noRecentTransactions')}</p>
                    ) : (
                    <div className="rounded-md border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                <TableHead>{t('tableHeaderOrderId')}</TableHead>
                                <TableHead>{t('tableHeaderCustomer')}</TableHead>
                                <TableHead className="text-right rtl:text-left">{t('tableHeaderAmount')}</TableHead>
                                <TableHead>{t('tableHeaderMethodOrType')}</TableHead>
                                <TableHead>{t('tableHeaderTime')}</TableHead>
                                <TableHead>{t('tableHeaderStatus')}</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {recentTransactions.map((trn) => (
                                <TableRow key={trn.id}>
                                    <TableCell className="font-medium">{trn.orderId}</TableCell>
                                    <TableCell>
                                        <div className="flex items-center gap-2 rtl:gap-reverse">
                                            <UserCircleIcon className="h-4 w-4 text-muted-foreground" />
                                            {trn.customer || tCommon('N_A')}
                                        </div>
                                    </TableCell>
                                    <TableCell className="text-right rtl:text-left">YER {trn.amount.toFixed(2)}</TableCell>
                                    <TableCell>{trn.methodOrType}</TableCell>
                                    <TableCell>{trn.timestamp}</TableCell>
                                    <TableCell>
                                        <Badge variant={trn.status === 'Completed' ? 'default' : (trn.status === 'Unpaid' ? 'destructive' : 'secondary')}>
                                            {t(`status${trn.status.replace(/\s+/g, '')}` as any, undefined, trn.status)}
                                        </Badge>
                                    </TableCell>
                                </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                    )}
                </CardContent>
            </Card>
        </div>

      </div>
    </AuthenticatedLayout>
  );
}

