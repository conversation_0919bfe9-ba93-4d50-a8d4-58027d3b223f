import { Supplier, PurchaseOrder, SupplierEvaluation, SupplyDelivery, SupplierPayment } from '@/types';

// Mock Suppliers Data
export const mockSuppliers: Supplier[] = [
  {
    id: 'supplier-001',
    name: 'شركة الحبوب المتحدة',
    companyName: 'United Grains Company',
    contactPerson: 'أحمد محمد الحبوب',
    email: '<EMAIL>',
    phone: '+967-1-234567',
    address: 'شارع الزراعة، صنعاء',
    city: 'صنعاء',
    country: 'اليمن',
    taxId: 'TAX-001-YE',
    businessLicense: 'BL-2023-001',
    category: 'grains',
    products: ['أرز', 'قمح', 'شعير', 'ذرة'],
    paymentTerms: 'Net 30',
    creditLimit: 500000,
    currentBalance: 125000,
    rating: 4.5,
    status: 'active',
    dateAdded: '2023-01-15',
    lastOrderDate: '2024-01-20',
    totalOrders: 45,
    totalPurchaseAmount: 2250000,
    averageDeliveryTime: 3,
    qualityRating: 4.5,
    reliabilityRating: 4.8,
    priceCompetitiveness: 4.2,
    notes: 'مورد موثوق للحبوب والبقوليات',
    ownerId: 'owner001',
    bankDetails: {
      bankName: 'البنك الأهلي اليمني',
      accountNumber: '*********',
      routingNumber: '001',
      swiftCode: 'NBOYYE22'
    },
    certifications: ['ISO 9001', 'HACCP', 'Halal'],
    minimumOrderAmount: 50000,
    leadTime: 5,
    returnPolicy: 'إرجاع خلال 7 أيام للمنتجات التالفة',
    warrantyTerms: 'ضمان الجودة لمدة 6 أشهر'
  },
  {
    id: 'supplier-002',
    name: 'مؤسسة الألبان الطازجة',
    companyName: 'Fresh Dairy Corporation',
    contactPerson: 'فاطمة علي الألبان',
    email: '<EMAIL>',
    phone: '+967-2-345678',
    address: 'المنطقة الصناعية، عدن',
    city: 'عدن',
    country: 'اليمن',
    taxId: 'TAX-002-YE',
    businessLicense: 'BL-2023-002',
    category: 'dairy',
    products: ['حليب', 'جبن', 'زبدة', 'لبن'],
    paymentTerms: 'Net 15',
    creditLimit: 300000,
    currentBalance: 75000,
    rating: 4.7,
    status: 'active',
    dateAdded: '2023-02-10',
    lastOrderDate: '2024-01-18',
    totalOrders: 62,
    totalPurchaseAmount: 1850000,
    averageDeliveryTime: 2,
    qualityRating: 4.8,
    reliabilityRating: 4.6,
    priceCompetitiveness: 4.0,
    notes: 'منتجات ألبان طازجة وعالية الجودة',
    ownerId: 'owner001',
    bankDetails: {
      bankName: 'بنك التضامن الإسلامي',
      accountNumber: '*********',
      routingNumber: '002',
      swiftCode: 'TISBYE22'
    },
    certifications: ['ISO 22000', 'HACCP', 'Organic'],
    minimumOrderAmount: 25000,
    leadTime: 3,
    returnPolicy: 'إرجاع فوري للمنتجات منتهية الصلاحية',
    warrantyTerms: 'ضمان الطزاجة حتى تاريخ الانتهاء'
  },
  {
    id: 'supplier-003',
    name: 'شركة الخضار والفواكه',
    companyName: 'Vegetables & Fruits Co.',
    contactPerson: 'محمد سالم الخضار',
    email: '<EMAIL>',
    phone: '+967-3-456789',
    address: 'السوق المركزي، تعز',
    city: 'تعز',
    country: 'اليمن',
    taxId: 'TAX-003-YE',
    businessLicense: 'BL-2023-003',
    category: 'produce',
    products: ['طماطم', 'خيار', 'تفاح', 'موز'],
    paymentTerms: 'Cash on Delivery',
    creditLimit: 150000,
    currentBalance: 0,
    rating: 4.2,
    status: 'active',
    dateAdded: '2023-03-05',
    lastOrderDate: '2024-01-22',
    totalOrders: 78,
    totalPurchaseAmount: 980000,
    averageDeliveryTime: 1,
    qualityRating: 4.3,
    reliabilityRating: 4.1,
    priceCompetitiveness: 4.5,
    notes: 'خضار وفواكه طازجة يومياً',
    ownerId: 'owner001',
    bankDetails: {
      bankName: 'البنك التجاري اليمني',
      accountNumber: '*********',
      routingNumber: '003',
      swiftCode: 'CBYEYE22'
    },
    certifications: ['Organic', 'Fresh Produce'],
    minimumOrderAmount: 10000,
    leadTime: 1,
    returnPolicy: 'إرجاع خلال 24 ساعة للمنتجات غير الطازجة',
    warrantyTerms: 'ضمان الطزاجة لمدة 3 أيام'
  },
  {
    id: 'supplier-004',
    name: 'مصنع المعلبات الذهبية',
    companyName: 'Golden Canned Foods Factory',
    contactPerson: 'عبدالله أحمد المعلبات',
    email: '<EMAIL>',
    phone: '+967-4-567890',
    address: 'المنطقة الصناعية، الحديدة',
    city: 'الحديدة',
    country: 'اليمن',
    taxId: 'TAX-004-YE',
    businessLicense: 'BL-2023-004',
    category: 'canned_goods',
    products: ['تونة معلبة', 'فول معلب', 'طماطم معلبة', 'ذرة معلبة'],
    paymentTerms: 'Net 45',
    creditLimit: 400000,
    currentBalance: 180000,
    rating: 4.0,
    status: 'active',
    dateAdded: '2023-04-12',
    lastOrderDate: '2024-01-15',
    totalOrders: 32,
    totalPurchaseAmount: 1600000,
    averageDeliveryTime: 7,
    qualityRating: 4.0,
    reliabilityRating: 3.8,
    priceCompetitiveness: 4.3,
    notes: 'معلبات عالية الجودة وأسعار تنافسية',
    ownerId: 'owner001',
    bankDetails: {
      bankName: 'بنك الكريمي للتمويل الأصغر',
      accountNumber: '*********',
      routingNumber: '004',
      swiftCode: 'KBMFYE22'
    },
    certifications: ['ISO 9001', 'HACCP', 'FDA'],
    minimumOrderAmount: 75000,
    leadTime: 10,
    returnPolicy: 'إرجاع خلال 30 يوم للمنتجات التالفة',
    warrantyTerms: 'ضمان الجودة حسب تاريخ الانتهاء'
  },
  {
    id: 'supplier-005',
    name: 'شركة المنظفات الحديثة',
    companyName: 'Modern Cleaning Products',
    contactPerson: 'سارة محمد المنظفات',
    email: '<EMAIL>',
    phone: '+967-5-678901',
    address: 'شارع الصناعة، إب',
    city: 'إب',
    country: 'اليمن',
    taxId: 'TAX-005-YE',
    businessLicense: 'BL-2023-005',
    category: 'cleaning',
    products: ['صابون غسيل', 'منظف أطباق', 'مطهر', 'منعم أقمشة'],
    paymentTerms: 'Net 30',
    creditLimit: 200000,
    currentBalance: 45000,
    rating: 4.3,
    status: 'active',
    dateAdded: '2023-05-20',
    lastOrderDate: '2024-01-19',
    totalOrders: 28,
    totalPurchaseAmount: 840000,
    averageDeliveryTime: 4,
    qualityRating: 4.4,
    reliabilityRating: 4.2,
    priceCompetitiveness: 4.1,
    notes: 'منتجات تنظيف فعالة وآمنة',
    ownerId: 'owner001',
    bankDetails: {
      bankName: 'بنك سبأ',
      accountNumber: '*********',
      routingNumber: '005',
      swiftCode: 'SABABYE22'
    },
    certifications: ['ISO 14001', 'Eco-friendly'],
    minimumOrderAmount: 30000,
    leadTime: 6,
    returnPolicy: 'إرجاع خلال 14 يوم للمنتجات المعيبة',
    warrantyTerms: 'ضمان الفعالية لمدة سنة'
  }
];

// Mock Purchase Orders Data
export const mockPurchaseOrders: PurchaseOrder[] = [
  {
    id: 'po-001',
    orderNumber: 'PO-2024-001',
    supplierId: 'supplier-001',
    supplierName: 'شركة الحبوب المتحدة',
    orderDate: '2024-01-15',
    expectedDeliveryDate: '2024-01-20',
    actualDeliveryDate: '2024-01-20',
    status: 'completed',
    priority: 'medium',
    items: [
      {
        id: 'poi-001',
        productName: 'أرز بسمتي',
        description: 'أرز بسمتي فاخر من الهند',
        sku: 'RICE-BASMATI-001',
        quantity: 100,
        unitPrice: 2000,
        totalPrice: 200000,
        receivedQuantity: 100,
        pendingQuantity: 0,
        unit: 'كيس 25 كيلو',
        specifications: 'أرز بسمتي درجة أولى'
      }
    ],
    subtotal: 200000,
    taxAmount: 30000,
    shippingCost: 5000,
    totalAmount: 235000,
    paymentTerms: 'Net 30',
    paymentStatus: 'paid',
    deliveryAddress: 'سوبر ماركت الوفاء، شارع الستين، صنعاء',
    notes: 'طلب عاجل للمخزون',
    createdBy: 'owner001',
    approvedBy: 'owner001',
    approvalDate: '2024-01-15',
    ownerId: 'owner001',
    currency: 'YER',
    exchangeRate: 1,
    discountAmount: 0,
    discountPercentage: 0
  }
];

// Mock Supplier Evaluations Data
export const mockSupplierEvaluations: SupplierEvaluation[] = [
  {
    id: 'eval-001',
    supplierId: 'supplier-001',
    supplierName: 'شركة الحبوب المتحدة',
    evaluationDate: '2024-01-01',
    evaluationPeriod: {
      startDate: '2023-10-01',
      endDate: '2023-12-31'
    },
    evaluatedBy: 'owner001',
    overallRating: 4.5,
    criteria: {
      qualityRating: 4.5,
      deliveryRating: 4.8,
      priceRating: 4.2,
      serviceRating: 4.6,
      communicationRating: 4.4,
      reliabilityRating: 4.8
    },
    metrics: {
      onTimeDeliveryRate: 95,
      qualityDefectRate: 2,
      orderAccuracyRate: 98,
      responseTime: 4,
      totalOrdersEvaluated: 15,
      totalValueEvaluated: 750000
    },
    strengths: [
      'جودة عالية للمنتجات',
      'التزام بمواعيد التسليم',
      'خدمة عملاء ممتازة'
    ],
    weaknesses: [
      'أسعار مرتفعة نسبياً',
      'تأخير أحياناً في الرد على الاستفسارات'
    ],
    improvementSuggestions: [
      'تحسين أوقات الاستجابة',
      'تقديم خصومات للطلبات الكبيرة'
    ],
    actionItems: [
      'مناقشة تحسين الأسعار',
      'وضع نظام تواصل أسرع'
    ],
    nextEvaluationDate: '2024-04-01',
    status: 'completed',
    ownerId: 'owner001',
    comments: 'مورد ممتاز بشكل عام مع إمكانية تحسين الأسعار'
  }
];

// Helper functions
export const getSuppliers = (): Promise<Supplier[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockSuppliers);
    }, 500);
  });
};

export const getSupplierById = (id: string): Promise<Supplier | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const supplier = mockSuppliers.find(s => s.id === id);
      resolve(supplier || null);
    }, 300);
  });
};

export const addSupplier = (supplier: Omit<Supplier, 'id'>): Promise<Supplier> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newSupplier: Supplier = {
        ...supplier,
        id: `supplier-${Date.now()}`
      };
      mockSuppliers.push(newSupplier);
      resolve(newSupplier);
    }, 500);
  });
};

export const updateSupplier = (id: string, updates: Partial<Supplier>): Promise<Supplier | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockSuppliers.findIndex(s => s.id === id);
      if (index !== -1) {
        mockSuppliers[index] = { ...mockSuppliers[index], ...updates };
        resolve(mockSuppliers[index]);
      } else {
        resolve(null);
      }
    }, 500);
  });
};

export const deleteSupplier = (id: string): Promise<boolean> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockSuppliers.findIndex(s => s.id === id);
      if (index !== -1) {
        mockSuppliers.splice(index, 1);
        resolve(true);
      } else {
        resolve(false);
      }
    }, 300);
  });
};

export const getPurchaseOrders = (): Promise<PurchaseOrder[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockPurchaseOrders);
    }, 500);
  });
};

export const getSupplierEvaluations = (): Promise<SupplierEvaluation[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockSupplierEvaluations);
    }, 500);
  });
};
