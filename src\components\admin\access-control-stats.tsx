"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Shield, 
  Users, 
  Lock, 
  Unlock, 
  <PERSON>ert<PERSON><PERSON>gle, 
  CheckCircle,
  <PERSON>r<PERSON><PERSON>ck,
  UserX,
  Settings
} from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import type { Role } from '@/types';

interface PermissionSetting {
  feature: string;
  view: boolean;
  edit: boolean;
  create: boolean;
  delete: boolean;
}

interface RolePermission {
  role: Role;
  permissions: PermissionSetting[];
}

interface AccessControlStatsProps {
  rolePermissions: RolePermission[];
  totalUsers?: number;
  activeUsers?: number;
}

export function AccessControlStats({ 
  rolePermissions, 
  totalUsers = 0, 
  activeUsers = 0 
}: AccessControlStatsProps) {
  const t = useScopedI18n('accessControl');
  const tCommon = useScopedI18n('common');

  // Calculate statistics
  const totalRoles = rolePermissions.length;
  const totalFeatures = rolePermissions.reduce((acc, role) => 
    Math.max(acc, role.permissions.length), 0
  );
  
  const permissionStats = rolePermissions.map(role => {
    const totalPermissions = role.permissions.length * 4; // 4 permission types per feature
    const grantedPermissions = role.permissions.reduce((acc, perm) => 
      acc + (perm.view ? 1 : 0) + (perm.edit ? 1 : 0) + 
      (perm.create ? 1 : 0) + (perm.delete ? 1 : 0), 0
    );
    
    return {
      role: role.role,
      percentage: totalPermissions > 0 ? (grantedPermissions / totalPermissions) * 100 : 0,
      granted: grantedPermissions,
      total: totalPermissions
    };
  });

  const averagePermissionLevel = permissionStats.reduce((acc, stat) => 
    acc + stat.percentage, 0) / permissionStats.length;

  const highPrivilegeRoles = permissionStats.filter(stat => stat.percentage > 75);
  const restrictedRoles = permissionStats.filter(stat => stat.percentage < 25);

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Roles */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {t('totalRoles')}
          </CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalRoles}</div>
          <p className="text-xs text-muted-foreground">
            {t('rolesConfigured')}
          </p>
        </CardContent>
      </Card>

      {/* Total Features */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {t('totalFeatures')}
          </CardTitle>
          <Settings className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalFeatures}</div>
          <p className="text-xs text-muted-foreground">
            {t('featuresManaged')}
          </p>
        </CardContent>
      </Card>

      {/* Average Permission Level */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {t('averagePermissionLevel')}
          </CardTitle>
          <Shield className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {averagePermissionLevel.toFixed(1)}%
          </div>
          <Progress value={averagePermissionLevel} className="mt-2" />
        </CardContent>
      </Card>

      {/* Security Status */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {t('securityStatus')}
          </CardTitle>
          {highPrivilegeRoles.length > 2 ? (
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          ) : (
            <CheckCircle className="h-4 w-4 text-green-500" />
          )}
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {highPrivilegeRoles.length > 2 ? t('review') : t('secure')}
          </div>
          <p className="text-xs text-muted-foreground">
            {highPrivilegeRoles.length} {t('highPrivilegeRoles')}
          </p>
        </CardContent>
      </Card>

      {/* Role Permission Breakdown */}
      <Card className="md:col-span-2 lg:col-span-4">
        <CardHeader>
          <CardTitle className="flex items-center">
            <UserCheck className="h-5 w-5 mr-2" />
            {t('rolePermissionBreakdown')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {permissionStats.map((stat) => (
              <div key={stat.role} className="flex items-center justify-between">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Badge variant={
                    stat.percentage > 75 ? 'destructive' : 
                    stat.percentage > 50 ? 'default' : 
                    stat.percentage > 25 ? 'secondary' : 'outline'
                  }>
                    {tCommon(`role_${stat.role}` as any, undefined, stat.role)}
                  </Badge>
                  <div className="flex-1">
                    <div className="flex items-center justify-between text-sm">
                      <span>{stat.granted}/{stat.total} {t('permissions')}</span>
                      <span className="font-medium">{stat.percentage.toFixed(1)}%</span>
                    </div>
                    <Progress value={stat.percentage} className="mt-1 h-2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* High Privilege Roles Alert */}
      {highPrivilegeRoles.length > 0 && (
        <Card className="md:col-span-2 lg:col-span-2 border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center text-yellow-800">
              <AlertTriangle className="h-5 w-5 mr-2" />
              {t('highPrivilegeAlert')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {highPrivilegeRoles.map((role) => (
                <div key={role.role} className="flex items-center justify-between">
                  <Badge variant="outline" className="text-yellow-800 border-yellow-300">
                    {tCommon(`role_${role.role}` as any, undefined, role.role)}
                  </Badge>
                  <span className="text-sm font-medium text-yellow-800">
                    {role.percentage.toFixed(1)}% {t('permissions')}
                  </span>
                </div>
              ))}
            </div>
            <p className="text-xs text-yellow-700 mt-2">
              {t('highPrivilegeWarning')}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Restricted Roles */}
      {restrictedRoles.length > 0 && (
        <Card className="md:col-span-2 lg:col-span-2 border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-800">
              <Lock className="h-5 w-5 mr-2" />
              {t('restrictedRoles')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {restrictedRoles.map((role) => (
                <div key={role.role} className="flex items-center justify-between">
                  <Badge variant="outline" className="text-blue-800 border-blue-300">
                    {tCommon(`role_${role.role}` as any, undefined, role.role)}
                  </Badge>
                  <span className="text-sm font-medium text-blue-800">
                    {role.percentage.toFixed(1)}% {t('permissions')}
                  </span>
                </div>
              ))}
            </div>
            <p className="text-xs text-blue-700 mt-2">
              {t('restrictedRolesInfo')}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
