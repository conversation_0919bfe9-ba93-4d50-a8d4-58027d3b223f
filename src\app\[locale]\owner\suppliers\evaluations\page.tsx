"use client";

import { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  ClipboardList,
  Calendar,
  Star,
  TrendingUp,
  TrendingDown,
  Minus,
  CheckCircle,
  Clock,
  AlertTriangle,
  BarChart3
} from 'lucide-react';
import { SupplierEvaluation } from '@/types';
import { getSupplierEvaluations } from '@/lib/mock-supplier-data';

export default function SupplierEvaluationsPage() {
  const t = useScopedI18n('ownerSupplierManagement');
  const { user } = useAuth();
  const { toast } = useToast();

  const [evaluations, setEvaluations] = useState<SupplierEvaluation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    loadEvaluations();
  }, []);

  const loadEvaluations = async () => {
    setIsLoading(true);
    try {
      const evaluationsData = await getSupplierEvaluations();
      setEvaluations(evaluationsData);
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في تحميل تقييمات الموردين',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filteredEvaluations = evaluations.filter(evaluation => {
    const matchesSearch = evaluation.supplierName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         evaluation.evaluatedBy.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || evaluation.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: 'مسودة', variant: 'secondary' as const, icon: Clock },
      completed: { label: 'مكتمل', variant: 'default' as const, icon: CheckCircle },
      approved: { label: 'معتمد', variant: 'default' as const, icon: CheckCircle }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config?.icon || Clock;
    
    return (
      <Badge variant={config?.variant} className="flex items-center space-x-1">
        <Icon className="h-3 w-3" />
        <span>{config?.label}</span>
      </Badge>
    );
  };

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
      />
    ));
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return 'text-green-600';
    if (rating >= 3.5) return 'text-yellow-600';
    if (rating >= 2.5) return 'text-orange-600';
    return 'text-red-600';
  };

  const getTrendIcon = (current: number, previous: number = 0) => {
    if (current > previous) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (current < previous) return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <Minus className="h-4 w-4 text-gray-600" />;
  };

  const stats = {
    totalEvaluations: evaluations.length,
    completedEvaluations: evaluations.filter(e => e.status === 'completed' || e.status === 'approved').length,
    averageRating: evaluations.length > 0 ? 
      (evaluations.reduce((sum, e) => sum + e.overallRating, 0) / evaluations.length).toFixed(1) : '0',
    highPerformers: evaluations.filter(e => e.overallRating >= 4.5).length,
    needsImprovement: evaluations.filter(e => e.overallRating < 3.0).length
  };

  if (isLoading) {
    return (
      <AuthenticatedLayout expectedRole="owner">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">جارٍ تحميل التقييمات...</p>
          </div>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        {/* Header */}
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <ClipboardList className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('supplierEvaluations')}</h1>
            <p className="text-muted-foreground">
              تقييم أداء الموردين ومراجعة الجودة والخدمة
            </p>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي التقييمات</CardTitle>
              <ClipboardList className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalEvaluations}</div>
              <p className="text-xs text-muted-foreground">
                {stats.completedEvaluations} مكتملة
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">متوسط التقييم</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageRating}</div>
              <div className="flex items-center space-x-1">
                {getRatingStars(parseFloat(stats.averageRating))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">أداء ممتاز</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.highPerformers}</div>
              <p className="text-xs text-muted-foreground">
                تقييم 4.5+ نجوم
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">يحتاج تحسين</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.needsImprovement}</div>
              <p className="text-xs text-muted-foreground">
                تقييم أقل من 3 نجوم
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">معدل الإنجاز</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.totalEvaluations > 0 ? 
                  Math.round((stats.completedEvaluations / stats.totalEvaluations) * 100) : 0}%
              </div>
              <Progress 
                value={stats.totalEvaluations > 0 ? 
                  (stats.completedEvaluations / stats.totalEvaluations) * 100 : 0} 
                className="mt-2" 
              />
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="البحث في التقييمات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center space-x-2">
                  <Filter className="h-4 w-4" />
                  <span>تصفية حسب الحالة</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                  جميع الحالات
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('draft')}>
                  مسودة
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('completed')}>
                  مكتمل
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('approved')}>
                  معتمد
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button className="flex items-center space-x-2">
              <Plus className="h-4 w-4" />
              <span>إنشاء تقييم جديد</span>
            </Button>
          </div>
        </div>

        {/* Evaluations Table */}
        <Card>
          <CardHeader>
            <CardTitle>تقييمات الموردين</CardTitle>
            <CardDescription>
              قائمة بجميع تقييمات الموردين ونتائج الأداء
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredEvaluations.length === 0 ? (
              <div className="text-center py-8">
                <ClipboardList className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">لم يتم العثور على تقييمات</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('supplierName')}</TableHead>
                    <TableHead>{t('evaluationDate')}</TableHead>
                    <TableHead>{t('evaluationPeriod')}</TableHead>
                    <TableHead>{t('overallRating')}</TableHead>
                    <TableHead>المعايير الرئيسية</TableHead>
                    <TableHead>{t('status')}</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEvaluations.map((evaluation) => (
                    <TableRow key={evaluation.id}>
                      <TableCell>
                        <div className="font-medium">{evaluation.supplierName}</div>
                        <div className="text-sm text-muted-foreground">
                          قيمه: {evaluation.evaluatedBy}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{new Date(evaluation.evaluationDate).toLocaleDateString('ar-YE')}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(evaluation.evaluationPeriod.startDate).toLocaleDateString('ar-YE')} - 
                          {new Date(evaluation.evaluationPeriod.endDate).toLocaleDateString('ar-YE')}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <div className={`text-lg font-bold ${getRatingColor(evaluation.overallRating)}`}>
                            {evaluation.overallRating}
                          </div>
                          <div className="flex items-center space-x-1">
                            {getRatingStars(evaluation.overallRating)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between text-xs">
                            <span>الجودة:</span>
                            <span className={getRatingColor(evaluation.criteria.qualityRating)}>
                              {evaluation.criteria.qualityRating}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span>التسليم:</span>
                            <span className={getRatingColor(evaluation.criteria.deliveryRating)}>
                              {evaluation.criteria.deliveryRating}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span>السعر:</span>
                            <span className={getRatingColor(evaluation.criteria.priceRating)}>
                              {evaluation.criteria.priceRating}
                            </span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(evaluation.status)}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                            <DropdownMenuItem>
                              عرض التفاصيل
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              تعديل التقييم
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              مقارنة مع التقييمات السابقة
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              طباعة التقرير
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              إرسال للمورد
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}
