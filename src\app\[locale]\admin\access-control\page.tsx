
"use client";

import React from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { EnhancedPermissionMatrix } from '@/components/admin/enhanced-permission-matrix';
import { ShieldCheck } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';




export default function AdminAccessControlPage() {
  const t = useScopedI18n('accessControl');


  return (
    <AuthenticatedLayout expectedRole="admin">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <ShieldCheck className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        {/* Enhanced Permission Matrix */}
        <EnhancedPermissionMatrix />
      </div>
    </AuthenticatedLayout>
  );
}
