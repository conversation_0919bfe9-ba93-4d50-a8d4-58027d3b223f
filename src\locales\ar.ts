
export default {
  common: {
    appName: 'ماركت سينك',
    appDescription: "إدارة مركزية وآمنة لعمليات السوبر ماركت.",
    profile: 'الملف الشخصي',
    settings: 'الإعدادات',
    logout: 'تسجيل الخروج',
    toggleTheme: 'تبديل السمة',
    language: 'اللغة',
    english: 'الإنجليزية',
    arabic: 'العربية',
    loading: 'جار التحميل...',
    error: 'خطأ',
    save: 'حفظ',
    cancel: 'إلغاء',
    edit: 'تعديل',
    delete: 'حذف',
    viewDetails: "عرض التفاصيل",
    add: "إضافة",
    remove: "إزالة",
    status: "الحالة",
    actions: "الإجراءات",
    all: "الكل",
    yes: "نعم",
    no: "لا",
    N_A: "غير متاح", // Not Applicable
    searchPlaceholder: "بحث...",
    filterPlaceholder: "تصفية...",
    noData: "لا توجد بيانات متاحة.",
    noResults: "لم يتم العثور على نتائج.",
    confirm: "تأكيد",
    areYouSure: "هل أنت متأكد؟",
    thisActionCannotBeUndone: "لا يمكن التراجع عن هذا الإجراء.",
    pickADate: "اختر تاريخًا",
    success: "نجاح",
    items: "عناصر",
    optional: "(اختياري)",
    comingSoon: "الميزة قادمة قريبًا.",
    subscriptionExpired: "لقد انتهى اشتراكك.",
    renewSubscriptionPrompt: "يرجى تجديد اشتراكك لمواصلة الوصول إلى جميع الميزات.",
    manageSubscriptionNow: "إدارة الاشتراك الآن",

    // Navigation keys
    nav_dashboard: 'لوحة التحكم',
    nav_userManagement: 'إدارة المستخدمين',
    nav_subscriptionControl: 'التحكم بالاشتراكات',
    nav_systemLogs: 'سجلات النظام',
    nav_accessControl: 'التحكم بالوصول',
    nav_backupManagement: 'إدارة النسخ الاحتياطي',
    nav_staffManagement: 'إدارة المستخدمين',
    nav_productManagement: 'إدارة المنتجات',
    nav_onlineStore: 'المتجر الإلكتروني',
    nav_customerOrders: 'طلبات العملاء',
    nav_debtsManagement: 'إدارة الديون',
    nav_salesReports: 'تقارير المبيعات',
    nav_subscription: 'الاشتراك',
    nav_inventoryCheck: 'فحص المخزون',
    nav_smartPredictions: 'التنبؤات الذكية',
    nav_ownerSettings: 'إعدادات الحساب', 
    nav_profile: 'الملف الشخصي',
    nav_generalSettings: 'الإعدادات',
    nav_securitySettings: 'إعدادات الأمان',
    nav_recordTransactions: 'تسجيل المعاملات',
    nav_approveOrders: 'الموافقة على الطلبات',
    nav_dispatchOrders: 'إرسال الطلبات',
    nav_shop: 'المتجر',
    nav_myOrders: 'طلباتي',
    nav_myDebts: 'ديوني',
    nav_orderHistory: 'سجل الطلبات',
    nav_favorites: 'المفضلة',
    nav_manageWholesaleProducts: 'إدارة المنتجات', 
    nav_incomingOrders: 'الطلبات الواردة', 
    nav_manageAgents: 'إدارة الوكلاء', 
    nav_assignedOrders: 'الطلبات المعينة', 
    nav_notifications: 'الإشعارات',
    nav_tasks: 'إدارة المهام', 
    nav_manageBalances: 'إدارة الأرصدة', 
    nav_processReturns: 'معالجة المرتجعات', 
    nav_manageEmpProducts: 'إدارة المنتجات (موظف)',
    nav_setCreditLimits: 'تحديد حدود الائتمان',
    nav_grantCredit: 'منح ائتمان',
    nav_grantCreditOwner: "منح ائتمان للعملاء",
    nav_manageBalancesOwner: "إدارة أرصدة العملاء",
    nav_processReturnsOwner: "معالجة مرتجعات المبيعات",
    nav_setCreditLimitsOwner: "تقييم حدود ائتمان العملاء",
    nav_communicationCenter: "مركز الاتصالات",
    nav_productLookup: "البحث عن منتج",
    nav_shifts: "إدارة الورديات",
    nav_performanceReports: "تقارير الأداء",
    nav_returnsManagement: "إدارة المرتجعات",
    nav_customerBalances: "أرصدة العملاء",
    nav_inventoryReports: "تقارير المخزون",
    nav_printReports: "طباعة التقارير",
    nav_customerManagement: "إدارة العملاء",
    nav_advancedAnalytics: "التحليلات المتقدمة",
    nav_marketingTools: "أدوات التسويق",
    nav_supplierManagement: "إدارة الموردين",
    nav_financialManagement: "الإدارة المالية",
    nav_loyaltyProgram: "برنامج الولاء",
    nav_alerts: "إرسال تنبيهات",
    nav_supportTickets: "تذاكر الدعم",
    nav_appSettings: "إعدادات التطبيق",
    nav_browseWholesale: "تصفح منتجات الجملة",
  },
  readme: {
    readmeTitle: "فايربيس ستوديو",
    readmeIntro: "هذا قالب بداية لتطبيق NextJS في فايربيس ستوديو.",
    readmeGuide: "للبدء، ألقِ نظرة على src/app/ar/page.tsx."
  },
  login: {
    pageTitle: 'تسجيل الدخول - ماركت سينك',
    logoAlt: 'شعار ماركت سينك',
    appName: 'ماركت سينك',
    tagline: 'تبسيط عمليات السوبر ماركت',
    welcome: 'مرحباً بعودتك!',
    signInPrompt: 'سجل الدخول للوصول إلى حسابك في ماركت سينك.',
    usernameLabel: 'اسم المستخدم', 
    usernamePlaceholder: 'مثال: admin أو owner',
    usernameRequired: 'اسم المستخدم مطلوب.',
    phoneNumberLabel: 'رقم الهاتف',
    phoneNumberPlaceholder: 'مثال: 7XXXXXXXX',
    phoneNumberRequired: 'رقم الهاتف مطلوب.',
    passwordLabel: 'كلمة المرور',
    passwordPlaceholder: 'أدخل كلمة المرور الخاصة بك',
    passwordRequired: 'كلمة المرور مطلوبة.',
    signInButton: 'تسجيل الدخول',
    signingIn: 'جارٍ تسجيل الدخول...',
    copyright: '© {year} ماركت سينك. جميع الحقوق محفوظة.',
  },
  auth: {
    loginFailed: 'فشل تسجيل الدخول',
    invalidCredentials: 'رقم الهاتف أو كلمة المرور غير صالحة.',
    loginError: 'خطأ في تسجيل الدخول',
    unexpectedError: 'حدث خطأ غير متوقع أثناء تسجيل الدخول.',
    loginSuccess: 'تم تسجيل الدخول بنجاح',
    welcomeBack: 'مرحباً بعودتك، {name}!',
    loggedOut: 'تم تسجيل الخروج',
    loggedOutSuccess: 'لقد تم تسجيل خروجك بنجاح.',
  },
  form: {
    usernameMin: "يجب أن يتكون اسم المستخدم من 3 أحرف على الأقل.",
    emailInvalid: "عنوان البريد الإلكتروني غير صالح.",
    passwordMin: "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل.",
    required: "{field} مطلوب.",
    fieldRequired: "{field} مطلوب.",
    fieldInvalid: "{field} غير صالح.",
    phoneNumberInvalid: "رقم الهاتف يجب أن يكون 9 أو 10 أرقام.",
    phoneNumberDigitsOnly: "رقم الهاتف يجب أن يحتوي على أرقام فقط.",
    phoneNumberPlaceholder: "أدخل رقم الهاتف (بدون مفتاح الدولة)",
    phoneNumberHelpNoPrefix: "أدخل رقم الهاتف المكون من 9 أرقام (مثال: 771234567). مفتاح الدولة (+967) مُضمَّن.",
    usernameOptionalHelp: "اسم المستخدم اختياري الآن. إذا تُرك فارغًا، سيتم استخدام رقم الهاتف أو إنشاء معرف فريد."
  },
  adminDashboard: {
    title: "لوحة تحكم المسؤول",
    welcomeMessage: "مرحباً بك في لوحة تحكم المسؤول! لديك السيطرة الكاملة على النظام من هنا.",
    totalUsers: "إجمالي المستخدمين",
    activeSubscriptions: "الاشتراكات النشطة",
    systemLogs: "سجلات النظام",
    pendingSupportTickets: "تذاكر الدعم المعلقة",
    quickActions: "إجراءات سريعة",
    quickActionsDescription: "قم بتنفيذ المهام الإدارية الشائعة.",
    manageUsers: "إدارة المستخدمين",
    controlSubscriptions: "التحكم في الاشتراكات",
    accessControl: "التحكم في الوصول",
    viewSystemReports: "عرض تقارير النظام",
    sendNotifications: "إرسال الإشعارات",
    systemHealth: "صحة النظام",
    systemHealthDescription: "نظرة عامة على حالة النظام وأدائه.",
    overallSystemStatus: "الحالة العامة للنظام:",
    optimal: "مثالية",
    lastBackup: "آخر نسخة احتياطية:",
    todayAt: "اليوم الساعة {time}",
    recentSecurityAlerts: "تنبيهات الأمان الأخيرة:",
    backupManagement: "إدارة النسخ الاحتياطي",
  },
  ownerDashboard: {
    title: "لوحة تحكم المالك",
    totalStaff: "إجمالي المستخدمين",
    productsListed: "المنتجات المدرجة",
    todaysSales: "مبيعات اليوم",
    pendingOrders: "الطلبات المعلقة",
    totalDebts: "إجمالي الديون",
    quickActions: "إجراءات سريعة",
    quickActionsDescription: "الوصول إلى ميزات إدارة السوبر ماركت الرئيسية.",
    manageStaff: "إدارة المستخدمين",
    manageProducts: "إدارة المنتجات",
    viewSalesReports: "عرض تقارير المبيعات",
    inventoryCheck: "فحص المخزون",
    aiDemandForecast: "توقعات الطلب بالذكاء الاصطناعي",
    manageDebts: "إدارة الديون",
    subscriptionStatus: "حالة الاشتراك",
    currentSubscriptionActive: "اشتراكك الحالي نشط.",
    renewsOn: "يتجدد في: {date}",
    manageSubscription: "إدارة الاشتراك",
    subscriptionExpiredTitle: "انتهى الاشتراك",
    subscriptionExpiredMessage: "لقد انتهى اشتراكك في ماركت سينك. يرجى التجديد لمواصلة استخدام جميع الميزات.",
    renewPrompt: "يرجى التجديد لمواصلة الوصول إلى جميع الميزات.",
    manageSubscriptionButton: "تجديد / إدارة الاشتراك",
  },
  userManagement: {
    title: "إدارة المستخدمين",
    description: "إنشاء وعرض وتحرير وإدارة جميع حسابات المستخدمين في النظام.",
    allUsers: "جميع المستخدمين",
    allUsersDescription: "قائمة شاملة بجميع المستخدمين داخل ماركت سينك.",
    filterPlaceholder: "تصفية المستخدمين حسب رقم الهاتف، الاسم، البريد الإلكتروني أو الدور...",
    addUser: "إضافة مستخدم",
    editUser: "تعديل المستخدم",
    editUserDesc: "تحديث بيانات هذا المستخدم.",
    addNewUser: "إضافة مستخدم جديد",
    addNewUserDesc: "املأ تفاصيل المستخدم الجديد.",
    username: "اسم المستخدم",
    phoneNumber: "رقم الهاتف",
    email: "البريد الإلكتروني",
    role: "الدور",
    name: "الاسم الكامل",
    subscriptionEndDate: "تاريخ انتهاء الاشتراك",
    password: "كلمة المرور",
    passwordPlaceholderEdit: "اتركه فارغًا للاحتفاظ بالكلمة الحالية",
    passwordPlaceholderCreate: "أدخل كلمة المرور",
    passwordRequiredForNew: "كلمة المرور مطلوبة للمستخدمين الجدد.",
    passwordLeaveBlankDescription: "اترك كلمة المرور فارغة للاحتفاظ بها بدون تغيير.",
    selectRole: "اختر دورًا",
    userCreatedSuccess: "تم إنشاء المستخدم بنجاح.",
    userUpdatedSuccess: "تم تحديث المستخدم بنجاح.",
    userDeletedSuccess: "تم حذف المستخدم بنجاح.",
    errorFetchUsers: "فشل في جلب المستخدمين.",
    errorCreateUser: "فشل في إنشاء المستخدم.",
    errorUpdateUser: "فشل في تحديث المستخدم.",
    errorDeleteUser: "فشل في حذف المستخدم.",
    confirmDeleteUser: "هل أنت متأكد أنك تريد حذف هذا المستخدم؟ لا يمكن التراجع عن هذا الإجراء.",
    role_admin: "مسؤول النظام",
    role_owner: "مالك",
    role_employee: "موظف",
    role_customer: "عميل",
    role_wholesaler: "تاجر جملة",
    role_agent: "وكيل",
    subscriptionEndDateHelp: "مطلوب لأدوار المالك وتاجر الجملة.",
    saving: "جار الحفظ...",
    adminRoleExistsError: "يوجد مستخدم مسؤول بالفعل. يُسمح بمسؤول واحد فقط.",
    exportUsersButton: "تصدير المستخدمين",
    exportUsersToastTitle: "بدء التصدير",
    exportUsersToastDesc: "بدأت عملية تصدير بيانات المستخدمين (عنصر نائب).",
    businessTypeLabel: "نوع النشاط التجاري",
    businessTypePlaceholder: "اختر نوع النشاط",
    businessTypeHelp: "مطلوب لأدوار المالك وتاجر الجملة.",
    businessType_grocery: "مواد غذائية",
    businessType_pharmacy: "صيدلية",
    businessType_building_materials: "مواد بناء",
    businessType_electrical_tools: "أدوات كهربائية",
    businessType_supermarket: "سوبر ماركت",
  },
  ownerSettings: {
    title: "إعدادات حساب المالك",
    description: "إدارة تفاصيل متجرك وتفضيلات التشغيل وإعدادات التطبيق.",
    storeInformation: "معلومات المتجر",
    storeInfoDescription: "تحديث التفاصيل العامة للسوبر ماركت الخاص بك.",
    storeName: "اسم المتجر",
    storeNamePlaceholder: "اسم السوبر ماركت الخاص بك",
    storeAddress: "عنوان المتجر",
    storeAddressPlaceholder: "مثال: 123 شارع السوق، المدينة",
    contactEmail: "البريد الإلكتروني للتواصل",
    contactEmailPlaceholder: "<EMAIL>",
    contactPhone: "هاتف الاتصال",
    contactPhonePlaceholder: "(*************",
    storeLogo: "شعار المتجر",
    storeLogoHelp: "قم بتحميل شعار متجرك (مثل PNG، JPG).",
    operationalSettings: "الإعدادات التشغيلية",
    operationalSettingsDescription: "إدارة الجوانب التشغيلية لمتجرك.",
    defaultDeliveryFee: "رسوم التوصيل الافتراضية",
    minOrderValueForDelivery: "الحد الأدنى لقيمة الطلب للتوصيل",
    openingHours: "ساعات العمل (نص)",
    openingHoursPlaceholder: "مثال: الاثنين-الجمعة: 9 صباحًا - 9 مساءً، السبت-الأحد: 10 صباحًا - 7 مساءً",
    appearanceSettings: "إعدادات المظهر",
    appearanceSettingsDescription: "تخصيص شكل وأسلوب التطبيق.",
    theme: "السمة",
    themeDescription: "اختر سمة التطبيق المفضلة لديك.",
    selectTheme: "اختر السمة",
    themeLight: "فاتح",
    themeDark: "داكن",
    themeSystem: "النظام",
    notificationSettings: "إعدادات الإشعارات",
    notificationSettingsDescription: "إدارة كيفية تلقي الإشعارات.",
    emailNotifications: "إشعارات البريد الإلكتروني",
    emailNotificationsHelp: "تلقي التحديثات الهامة عبر البريد الإلكتروني.",
    newOrderNotifications: "إشعارات الطلبات الجديدة",
    newOrderNotificationsHelp: "الحصول على إشعار بطلبات العملاء الجديدة.",
    lowStockAlerts: "تنبيهات انخفاض المخزون",
    lowStockAlertsHelp: "تلقي تنبيهات عندما يكون مخزون المنتج منخفضًا.",
    saveAllSettings: "حفظ جميع الإعدادات",
    pushNotifications: "إشعارات لحظية",
    pushNotificationsHelp: "احصل على تنبيهات فورية على جهازك.",
    inAppNotifications: "إشعارات داخل التطبيق",
    inAppNotificationsHelp: "إظهار الإشعارات داخل التطبيق.",
    integrationsTitle: "تكامل الأنظمة",
    integrationsDescription: "إدارة التكامل مع الأنظمة الأخرى مثل نقاط البيع أو برامج المحاسبة.",
    integrationsComingSoon: "ستتوفر خيارات التكامل هنا في المستقبل.",
  },
  accessControl: {
    title: "إدارة التحكم بالوصول",
    description: "تحديد وإدارة الأدوار والأذونات عبر المنصة.",
    roleBasedPermissions: "الأذونات المستندة إلى الدور",
    configurePermissions: "تكوين ما يمكن لكل دور مستخدم الوصول إليه والقيام به.",
    manageUsersAndRoles: "إدارة المستخدمين والأدوار",
    filterByRoleOrFeature: "تصفية حسب الدور أو الميزة...",
    noRolesConfigured: "لا توجد أدوار مهيأة لإدارة الأذونات.",
    editPermissionsForRole: "تعديل أذونات دور {role}",
    feature: "الميزة",
    view: "عرض",
    edit: "تعديل",
    create: "إنشاء",
    delete: "حذف",
    saveAllChanges: "حفظ جميع التغييرات",
    permissionsSavedSuccess: "تم حفظ الأذونات بنجاح.",
    // إحصائيات التحكم في الوصول
    totalRoles: "إجمالي الأدوار",
    rolesConfigured: "أدوار مهيأة",
    totalFeatures: "إجمالي الميزات",
    featuresManaged: "ميزات مُدارة",
    averagePermissionLevel: "متوسط مستوى الأذونات",
    securityStatus: "حالة الأمان",
    review: "مراجعة",
    secure: "آمن",
    highPrivilegeRoles: "أدوار عالية الصلاحية",
    rolePermissionBreakdown: "تفصيل أذونات الأدوار",
    permissions: "أذونات",
    highPrivilegeAlert: "تنبيه الصلاحيات العالية",
    highPrivilegeWarning: "هذه الأدوار لديها أذونات واسعة. راجعها بانتظام.",
    restrictedRoles: "الأدوار المقيدة",
    restrictedRolesInfo: "هذه الأدوار لديها وصول محدود لميزات النظام.",
    // مصفوفة الأذونات
    permissionMatrix: "مصفوفة الأذونات",
    manageDetailedPermissions: "إدارة الأذونات التفصيلية لكل دور",
    searchPermissions: "البحث في الأذونات",
    searchPermissionsPlaceholder: "البحث بالاسم أو الوصف...",
    category: "الفئة",
    allCategories: "جميع الفئات",
    riskLevel: "مستوى المخاطر",
    allRiskLevels: "جميع مستويات المخاطر",
    riskLevel_low: "منخفض",
    riskLevel_medium: "متوسط",
    riskLevel_high: "عالي",
    resetToDefaults: "إعادة تعيين للافتراضي",
    permissionsGranted: "أذونات ممنوحة",
    permission: "الإذن",
    showingPermissions: "عرض {count} أذونات",
    noPermissionsFound: "لم يتم العثور على أذونات",
    adjustFiltersToSeeMore: "اضبط المرشحات لرؤية المزيد من الأذونات",
    permissionsSaved: "تم حفظ الأذونات",
    permissionsUpdatedSuccessfully: "تم تحديث الأذونات بنجاح",
    confirmResetPermissions: "هل أنت متأكد من إعادة تعيين جميع الأذونات للقيم الافتراضية؟",
    permissionsReset: "تم إعادة تعيين الأذونات",
    permissionsResetToDefaults: "تم إعادة تعيين جميع الأذونات للقيم الافتراضية",
    feature_ProductManagement: "إدارة منتجات المتجر",
    feature_StaffManagement: "إدارة موظفي المتجر",
    feature_SalesReports: "تقارير مبيعات المتجر",
    feature_InventoryPredictions: "توقعات مخزون المتجر",
    feature_RecordTransactions: "تسجيل المعاملات (موظف)",
    feature_ApproveOrders: "الموافقة على الطلبات (موظف)",
    feature_DispatchOrders: "إرسال الطلبات (موظف)",
    feature_ManageProductsWholesale: "إدارة منتجات الجملة",
    feature_ViewIncomingOrders: "عرض الطلبات الواردة (جملة)",
    feature_ManageAgents: "إدارة الوكلاء (جملة)",
    feature_ShopProducts: "تسوق المنتجات (عميل)",
    feature_ViewOrders: "عرض الطلبات (عميل)",
    feature_ViewAssignedOrders: "عرض الطلبات المعينة (وكيل)",
    feature_UpdateOrderStatus: "تحديث حالة الطلب (وكيل)",
    feature_UserManagement: "إدارة المستخدمين (مسؤول عام)",
    feature_SubscriptionControl: "التحكم باشتراكات النظام",
    feature_SystemLogs: "سجلات النظام (مسؤول عام)",
    feature_AccessControl: "التحكم بالوصول (مسؤول عام)",
    feature_BackupManagement: "إدارة النسخ الاحتياطي (مسؤول عام)",
    feature_SendAlerts: "إرسال تنبيهات النظام",
  },
  debtsManagement: {
    title: "إدارة الديون",
    description: "تسجيل وإدارة الديون والاعتمادات المستحقة.",
    recordNewDebtCredit: "تسجيل دين/اعتماد جديد",
    formTitleRecord: "تسجيل دين/اعتماد جديد",
    formTitleEdit: "تعديل الدين/الاعتماد",
    partyName: "اسم الطرف",
    partyNamePlaceholder: "مثال: جون العميل أو المورد س",
    amount: "المبلغ (ريال يمني)",
    amountPlaceholder: "0.00",
    type: "النوع",
    selectType: "اختر النوع",
    typeDebt: "دين (مستحق لك)",
    typeCredit: "اعتماد (أنت مدين به)",
    status: "الحالة",
    selectStatus: "اختر الحالة",
    statusUnpaid: "غير مدفوع",
    statusPaid: "مدفوع",
    statusPartiallyPaid: "مدفوع جزئيًا",
    date: "التاريخ",
    dueDateOptional: "تاريخ الاستحقاق (اختياري)",
    pickDueDate: "اختر تاريخ الاستحقاق",
    notesOptional: "ملاحظات (اختياري)",
    notesPlaceholder: "أي تفاصيل إضافية...",
    debtUpdated: "تم تحديث الدين",
    debtUpdatedDesc: "تم تحديث تفاصيل {partyName}.",
    debtRecorded: "تم تسجيل الدين",
    debtRecordedDesc: "تم تسجيل {type} لـ {partyName}.",
    debtRemoved: "تمت إزالة الدين",
    debtRemovedDesc: "تمت إزالة سجل الدين.",
    recordsTitle: "سجلات الديون والائتمانات",
    recordsDescription: "نظرة عامة على جميع الديون والائتمانات المسجلة.",
    filterRecordsPlaceholder: "تصفية حسب اسم الطرف أو الملاحظات...",
    noRecords: "لم يتم تسجيل أي ديون أو ائتمانات بعد.",
    recordsCount: "عرض {count} من {total} سجلات.",
    itemsForDebt: "عناصر الدين",
    selectProduct: "اختر المنتج",
    selectProductPlaceholder: "اختر منتجًا",
    itemQuantity: "الكمية",
    itemPrice: "السعر (ريال يمني)",
    each: "لكل",
    addItemButton: "إضافة عنصر للدين",
    itemsRequiredError: "مطلوب عنصر واحد على الأقل للدين.",
    addItemError: "الرجاء تحديد منتج والتأكد من صحة الكمية/السعر.",
    itemsCount: "العناصر",
    invoiceTitle: "فاتورة",
    invoiceTo: "فاتورة إلى",
    debtTypeLabel: "نوع الدين",
    invoiceNumber: "رقم الفاتورة",
    itemProduct: "المنتج",
    itemSubtotal: "المجموع الفرعي",
    totalAmount: "المبلغ الإجمالي",
    thankYouMessage: "شكرا لتعاملكم معنا!",
    printInvoice: "طباعة الفاتورة",
    printWindowError: "تعذر فتح نافذة الطباعة. يرجى التحقق من إعدادات المتصفح.",
    storeNamePlaceholder: "اسم متجرك",
    storeAddressPlaceholder: "عنوان متجرك، المدينة، الرمز البريدي",
  },
  agentDashboard: {
    title: "لوحة تحكم الوكيل",
    currentTasks: "المهام الحالية",
    newDeliveryTasks: "مهام توصيل جديدة",
    inProgressDeliveries: "التوصيلات قيد التنفيذ",
    recentNotifications: "الإشعارات الأخيرة",
    viewTasks: "عرض المهام",
    quickActions: "إجراءات سريعة",
    quickActionsDescription: "إدارة مهام التوصيل بكفاءة.",
    viewAssignedOrders: "عرض الطلبات المعينة",
    updateOrderStatus: "تحديث حالة الطلب",
    communicationCenter: "مركز الاتصالات",
    myRouteForToday: "مساري لليوم",
    deliveryPerformance: "أداء التوصيل",
    deliveryPerformanceDescription: "إحصائيات التوصيل الأخيرة.",
    completedDeliveriesLast7Days: "التوصيلات المكتملة (آخر 7 أيام): {count}",
    onTimeDeliveryRate: "معدل التوصيل في الوقت المحدد: {rate}%",
    averageDeliveryTime: "متوسط وقت التوصيل: {time} دقيقة",
  },
  agentOrders: {
    title: "الطلبات المعينة",
    description: "إدارة وتحديث حالة مهام التوصيل الخاصة بك.",
    filterSortOrders: "تصفية / فرز الطلبات",
    orderTitle: "طلب {id} - {customerName}",
    openInMaps: "فتح في الخرائط",
    itemsCountSlot: "العناصر: {count} | الفترة: {slot}",
    notesLabel: "ملاحظات: {notes}",
    callCustomer: "اتصال",
    messageCustomer: "مراسلة",
    startDelivery: "بدء التوصيل",
    updateStatus: "تحديث الحالة",
    updateNotes: "تحديث الملاحظات",
    noOrdersWithStatus: "لا توجد طلبات بحالة \"{status}\".",
    statusAssigned: "معين",
    statusOutForDelivery: "قيد التوصيل",
    statusDelivered: "تم التوصيل",
    statusFailedAttempt: "محاولة فاشلة",
    statusAll: "الكل",
  },
  profile: {
    title: "الملف الشخصي",
    description: "إدارة معلوماتك الشخصية وإعدادات الحساب.",
    detailsTitle: "تفاصيل الملف الشخصي",
    detailsDescription: "عرض وتحديث معلومات ملفك الشخصي. دورك هو: {role}",
    updateProfileButton: "تحديث الملف الشخصي",
    changePasswordTitle: "تغيير كلمة المرور",
    changePasswordDescription: "تحديث كلمة مرور حسابك.",
    currentPasswordLabel: "كلمة المرور الحالية",
    currentPasswordPlaceholder: "أدخل كلمة المرور الحالية",
    newPasswordLabel: "كلمة المرور الجديدة",
    newPasswordPlaceholder: "أدخل كلمة المرور الجديدة",
    confirmPasswordLabel: "تأكيد كلمة المرور الجديدة",
    confirmPasswordPlaceholder: "تأكيد كلمة المرور الجديدة",
    changePasswordButton: "تغيير كلمة المرور",
    usernameCannotBeChanged: "لا يمكن تغيير اسم المستخدم.",
  },
  adminLogs: {
    title: "سجلات النظام",
    description: "مراجعة نشاط النظام والأخطاء ومسارات التدقيق.",
    activityLogsTitle: "سجلات النشاط",
    activityLogsDescription: "سجلات مفصلة لعمليات وأحداث النظام.",
    filterUserActionPlaceholder: "تصفية حسب المستخدم أو الإجراء...",
    logLevelSelectPlaceholder: "مستوى السجل",
    allLevels: "جميع المستويات",
    levelInfo: "معلومات",
    levelWarn: "تحذير",
    levelError: "خطأ",
    levelDebug: "تصحيح",
    applyFiltersButton: "تطبيق المرشحات",
    exportLogsButton: "تصدير السجلات",
    timestampHeader: "الطابع الزمني",
    levelHeader: "المستوى",
    userSystemHeader: "المستخدم/النظام",
    actionHeader: "الإجراء",
    detailsHeader: "التفاصيل",
    noLogsFound: "لم يتم العثور على سجلات للمعايير المحددة.",
    filtersAppliedTitle: "تم تطبيق المرشحات",
    filtersAppliedDesc: "تم تحديث عرض السجل بناءً على المرشحات المحددة (محاكاة).",
    exportLogsTitle: "تصدير السجلات",
    exportLogsDescPlaceholder: "بدأت عملية تصدير السجلات (عنصر نائب).",
    showingLogsCount: "عرض {count} سجلات.",
    analyzeLogsButton: "تحليل السجلات",
    // تحليلات السجلات
    systemLogs: "سجلات النظام",
    analytics: "التحليلات",
    totalLogs: "إجمالي السجلات",
    inLast24Hours: "في آخر 24 ساعة",
    errorCount: "عدد الأخطاء",
    warningCount: "عدد التحذيرات",
    systemWarnings: "تحذيرات النظام",
    activeUsers: "المستخدمون النشطون",
    uniqueUsers: "مستخدمون فريدون",
    logLevelDistribution: "توزيع مستويات السجلات",
    distributionOfLogLevels: "توزيع مستويات السجلات عبر جميع الإدخالات",
    hourlyActivity: "النشاط بالساعة",
    activityLast24Hours: "النشاط في آخر 24 ساعة",
    topActiveUsers: "أكثر المستخدمين نشاطاً",
    mostActiveUsers: "المستخدمون الذين لديهم أكثر نشاط في النظام",
    actions: "إجراءات",
    recentCriticalEvents: "الأحداث الحرجة الأخيرة",
    latestErrorsAndWarnings: "أحدث الأخطاء والتحذيرات التي تتطلب انتباه",
    noCriticalEvents: "لم يتم العثور على أحداث حرجة",
    // مراقب النظام
    systemMonitor: "مراقب النظام",
    realTimeSystemMetrics: "مقاييس الأداء والصحة للنظام في الوقت الفعلي",
    refresh: "تحديث",
    cpuUsage: "استخدام المعالج",
    cores: "أنوية",
    memoryUsage: "استخدام الذاكرة",
    diskUsage: "استخدام القرص",
    networkStatus: "حالة الشبكة",
    databaseStatus: "حالة قاعدة البيانات",
    connectionStatus: "حالة الاتصال",
    latency: "زمن الاستجابة",
    bandwidth: "عرض النطاق",
    activeConnections: "الاتصالات النشطة",
    avgQueryTime: "متوسط وقت الاستعلام",
    systemInformation: "معلومات النظام",
    systemUptime: "وقت تشغيل النظام",
    lastUpdated: "آخر تحديث",
    systemHealth: "صحة النظام",
    healthy: "سليم",
    warning: "تحذير",
    statusOnline: "متصل",
    statusSlow: "بطيء",
    statusOffline: "غير متصل",
    currentlyOnline: "متصل حالياً",
  },
  adminSubscriptions: {
    title: "التحكم في الاشتراكات",
    description: "إدارة اشتراكات المستخدمين وخططهم للمالكين وتجار الجملة.",
    manageSubscriptionsTitle: "إدارة الاشتراكات",
    manageSubscriptionsDescription: "عرض أو تعديل أو إدارة اشتراكات المستخدمين.",
    filterPlaceholder: "تصفية حسب المستخدم أو البريد الإلكتروني أو الخطة...",
    noSubscriptionsFound: "لم يتم العثور على اشتراكات نشطة.",
    userHeader: "المستخدم",
    emailHeader: "البريد الإلكتروني",
    roleHeader: "الدور",
    planHeader: "الخطة",
    endDateHeader: "تاريخ الانتهاء",
    statusHeader: "الحالة",
    actionsHeader: "الإجراءات",
    editSubscriptionItem: "تعديل الاشتراك",
    cancelSubscriptionItem: "إلغاء الاشتراك",
    statusActive: "نشط",
    statusExpired: "منتهي الصلاحية",
    statusCancelled: "ملغى",
    statusUnknown: "غير معروف",
    createNewPlanButton: "إنشاء خطة جديدة",
    createNewPlanToastTitle: "إنشاء خطة",
    createNewPlanToastDesc: "وظيفة إنشاء خطط الاشتراك قيد التطوير.",
    paymentTrackingInfo: "ميزات تتبع الدفع قيد التطوير.",
    planManagementInfo: "ميزات إدارة خطط الاشتراك قيد التطوير."
  },
  inventoryCheck: {
    title: "فحص المخزون",
    description: "مراقبة وإدارة مستويات المخزون الحالية.",
    addNewProductButton: "إضافة منتج جديد إلى المخزون",
    totalProducts: "إجمالي المنتجات",
    inStock: "متوفر في المخزون",
    lowStock: "مخزون منخفض",
    outOfStock: "نفذ من المخزون",
    currentStockLevelsTitle: "مستويات المخزون الحالية",
    currentStockLevelsDescription: "عرض مفصل للمنتجات وحالة مخزونها.",
    filterPlaceholder: "تصفية حسب الاسم أو الفئة أو المورد...",
    stockStatusSelectPlaceholder: "حالة المخزون",
    statusAll: "جميع الحالات",
    statusInStock: "متوفر في المخزون",
    statusLowStock: "مخزون منخفض",
    statusOutOfStock: "نفذ من المخزون",
    viewStockHistoryButton: "عرض سجل المخزون",
    headerImage: "صورة",
    headerName: "الاسم",
    headerCategory: "الفئة",
    headerStockQty: "كمية المخزون",
    headerStatus: "الحالة",
    headerSupplier: "المورد",
    headerLastRestock: "آخر إعادة تخزين",
    updateButton: "تحديث",
    noProductsFound: "لا توجد منتجات تطابق الفلتر الخاص بك، أو المخزون فارغ.",
    updateStockModalTitle: "تحديث مخزون: {productName}",
    updateStockModalDescription: "المخزون الحالي: {currentStock}. أدخل مستوى المخزون الإجمالي الجديد.",
    newStockLabel: "المخزون الجديد",
    newStockPlaceholder: "أدخل كمية المخزون الجديدة",
    reasonLabel: "السبب (اختياري)",
    reasonPlaceholder: "مثال: إعادة تخزين أسبوعية، تصحيح",
    toastInvalidStockTitle: "مستوى المخزون غير صالح",
    toastInvalidStockDesc: "يجب أن يكون المخزون رقمًا غير سالب.",
    toastStockUpdatedTitle: "تم تحديث المخزون",
    toastStockUpdatedDesc: "تم تحديث مخزون {productName} إلى {stockValue}.",
    toastNewProductAddedTitle: "تمت إضافة منتج جديد",
    toastNewProductAddedDesc: "{productName} تمت إضافته إلى المخزون. قم بتحديث تفاصيله.",
    loadingProducts: "جارٍ تحميل المنتجات...",
    newProductDefaultName: "منتج جديد",
    uncategorized: "غير مصنف",
    newProductDataAiHint: "منتج جديد",
    productAddedToastTitle: "تمت إضافة المنتج",
    productAddedToastDesc: "{productName} تمت إضافته إلى المخزون. يرجى تحديث تفاصيله.",
    productUpdatedToastTitle: "تم تحديث المنتج",
    productUpdatedToastDesc: "تم تحديث مخزون {productName} إلى {stockValue}.",
    updateErrorToastDesc: "الرجاء تحديد منتج وإدخال مستوى مخزون صالح.",
  },
  ownerStaff: { 
    title: "إدارة المستخدمين", 
    description: "إدارة الموظفين والعملاء المرتبطين بمتجرك.", 
    addNewEmployeeButton: "إضافة مستخدم جديد", 
    employeeListTitle: "قائمة المستخدمين", 
    employeeListDescription: "عرض أو إضافة أو تعديل أو إزالة الموظفين والعملاء.", 
    filterPlaceholder: "تصفية المستخدمين بالاسم أو الدور أو رقم الهاتف...", 
    noStaffMembers: "لم يتم إضافة أي مستخدمين بعد. انقر فوق \"إضافة مستخدم جديد\" للبدء.", 
    rolePositionHeader: "الدور/المنصب",
    phoneNumberHeader: "رقم الهاتف",
    editDetailsItem: "تعديل التفاصيل",
    managePermissionsItem: "إدارة الأذونات",
    removeEmployeeItem: "إزالة المستخدم", 
    roleManager: "مدير",
    roleCashierStaff: "أمين صندوق/موظف", 
    addStaffMemberTitle: "إضافة مستخدم جديد", 
    editStaffMemberTitle: "تعديل بيانات المستخدم", 
    addStaffMemberDesc: "أدخل بيانات المستخدم الجديد.", 
    editStaffMemberDesc: "تحديث بيانات هذا المستخدم.", 
    passwordLeaveBlankDescription: "اترك كلمة المرور فارغة للاحتفاظ بالكلمة الحالية.",
    passwordRequiredForNewStaff: "كلمة المرور مطلوبة للمستخدمين الجدد.",
    staffMemberAddedSuccess: "تمت إضافة المستخدم بنجاح.", 
    staffMemberUpdatedSuccess: "تم تحديث بيانات المستخدم بنجاح.", 
    staffMemberRemovedSuccess: "تمت إزالة المستخدم بنجاح.", 
    confirmRemoveEmployee: "هل أنت متأكد أنك تريد إزالة هذا المستخدم؟", 
    errorFetchingUsers: "فشل في جلب المستخدمين.",
    errorProcessingUser: "فشل في معالجة المستخدم.",
    errorDeletingUser: "فشل في حذف المستخدم.",
    permissionsTitle: "الأذونات",
    permissionsForEmployeeRole: "تعيين أذونات محددة لهذا الموظف.",
    permissionCanReceiveOrders: "استلام طلبات العملاء",
    permissionCanGrantCredit: "منح ائتمان للعملاء",
    permissionCanManageCustomerBalances: "إدارة أرصدة العملاء",
    permissionCanProcessSalesReturns: "معالجة مرتجعات المبيعات",
    permissionCanManageProducts: "إدارة المنتجات (إضافة/تسعير)",
    permissionCanSetCreditLimits: "تقييم حدود ائتمان العملاء",
    permissionCanDispatchOrders: "إرسال الطلبات",
    permissionCanRecordTransactions: "تسجيل المعاملات",
    permissionCanManageShifts: "إدارة الورديات",
    permissionCanViewPerformanceReports: "عرض تقارير الأداء",
    permissionCanAccessCommunicationTools: "الوصول إلى أدوات الاتصال",
    permissionCanViewInventoryReports: "عرض تقارير المخزون",
    permissionCanPrintReports: "طباعة التقارير",
    permissionCanAccessProductLookup: "الوصول إلى البحث عن المنتج",
    permissionCanManageTasks: "إدارة المهام",
  },
  customerDashboard: {
    welcome: "أهلاً بك!",
    tagline: "جاهز للتسوق؟ إليك بعض الروابط السريعة لتبدأ.",
    startShopping: "ابدأ التسوق",
    startShoppingDesc: "تصفح أحدث منتجاتنا وعروضنا.",
    shopNow: "تسوق الآن",
    myOrders: "طلباتي",
    myOrdersDesc: "تتبع طلباتك الحالية واعرض التفاصيل.",
    viewOrders: "عرض الطلبات",
    myDebts: "ديوني",
    myDebtsDesc: "اطلع على ديونك المستحقة للمتجر.",
    viewDebts: "عرض الديون",
    totalOutstandingDebt: "إجمالي الديون المستحقة",
    orderHistory: "سجل الطلبات",
    orderHistoryDesc: "راجع مشترياتك السابقة.",
    seeHistory: "عرض السجل",
    favorites: "المفضلة",
    favoritesDesc: "الوصول إلى العناصر المحفوظة لديك.",
    myFavorites: "مفضلتي",
    featuredCategories: "الفئات المميزة",
    freshProduce: "الفواكه والخضروات الطازجة", // تعديل ليكون أكثر وصفية
    dairyEggs: "الألبان والبيض",
    bakery: "المخبوزات",
    exploreCategory: "تصفح {categoryName}",
    notificationsTitle: "الإشعارات",
    notificationsMessage: "لديك {count} إشعارات جديدة.",
    viewNotifications: "عرض الإشعارات",
    reviewsRatingsTitle: "التقييمات والمراجعات",
    reviewsRatingsMessage: "شارك برأيك في مشترياتك الأخيرة.",
    leaveAReview: "اترك تقييمًا",
  },
  customerOrders: {
    title: "طلباتي",
    description: "تتبع طلباتك الحالية واعرض حالتها.",
    tabActive: "نشطة",
    tabDelivered: "تم التوصيل",
    tabCancelled: "ملغاة",
    tabAllOrders: "جميع الطلبات",
    noOrdersFoundTitle: "لم يتم العثور على طلبات",
    noOrdersWithStatus: "ليس لديك طلبات بالحالة \"{status}\".",
    startShoppingButton: "ابدأ التسوق",
    orderDetails: "التاريخ: {date} | {itemCount} عناصر",
    statusProcessing: "قيد المعالجة",
    statusShipped: "تم الشحن",
    statusDelivered: "تم التوصيل",
    statusCancelled: "ملغى",
    quantityLabel: "الكمية: {qty}",
    andMoreItems: "و {count} عناصر أخرى...",
    totalLabel: "الإجمالي (ريال يمني)",
    trackingLabel: "تتبع",
    viewDetailsButton: "عرض التفاصيل",
    reorderButton: "إعادة الطلب",
    invoiceButton: "فاتورة",
  },
  customerShop: {
    title: "تسوق المنتجات",
    description: "تصفح واشترِ المنتجات من السوبر ماركت الخاص بنا.",
    searchPlaceholder: "ابحث عن المنتجات...",
    filtersButton: "مرشحات",
    specialOffer: "✨ عرض خاص: احصل على خصم 10% على جميع منتجات الألبان هذا الأسبوع! استخدم الكود: DAIRY10 ✨",
    storeUpdatingTitle: "متجرنا الإلكتروني قيد التحديث حاليًا!",
    noProductsAvailable: "لا توجد منتجات متاحة في الوقت الحالي. يرجى التحقق مرة أخرى قريبًا.",
    toggleFavorite: "تبديل المفضلة",
    bestSellerBadge: "الأكثر مبيعًا",
    reviewsCount: "({count} تقييمات)",
    addToCartButton: "أضف إلى السلة",
    outOfStockButton: "نفذ المخزون",
    loadMoreButton: "تحميل المزيد من المنتجات",
    loadingProducts: "جارٍ تحميل المنتجات...",
    userNotAuthenticatedError: "يجب عليك تسجيل الدخول لإضافة منتجات إلى السلة.",
    cannotDetermineStoreError: "لا يمكن تحديد المتجر لهذا المنتج. الرجاء المحاولة مرة أخرى.",
    addedToCartToast: "تمت إضافة {productName} (الكمية: {quantity}) إلى السلة.",
    addToCartErrorToast: "فشل في إضافة المنتج إلى السلة. الرجاء المحاولة مرة أخرى.",
    quantityLabelShort: "الكمية",
    notEnoughStockError: "لا يوجد مخزون كافٍ لـ {productName}. المتوفر: {availableStock}.",
  },
  ownerProductManagement: {
    title: "إدارة المنتجات",
    description: "إدارة مخزون منتجات السوبر ماركت وقوائمها.",
    add_new_product_button: "إضافة منتج جديد",
    product_catalog_title: "كتالوج المنتجات",
    product_catalog_description: "إضافة وتعديل وتصنيف وإدارة تفاصيل المنتجات والمخزون.",
    filter_placeholder: "تصفية المنتجات حسب الاسم أو الفئة...",
    manage_categories_button: "إدارة الفئات",
    sort_by_button: "ترتيب حسب",
    no_products_message: "لم يتم إدراج أي منتجات بعد. انقر فوق \"إضافة منتج جديد\" للبدء أو ضبط المرشحات.",
    table_header_image: "صورة",
    table_header_name: "الاسم",
    table_header_category: "الفئة",
    table_header_price: "السعر (ريال يمني)",
    table_header_stock: "المخزون",
    table_header_status: "الحالة",
    table_header_online: "متصل",
    statusInStock: "متوفر",
    statusLowStock: "مخزون منخفض",
    statusOutOfStock: "نفذ من المخزون",
    dialog_edit_product_title: "تعديل المنتج",
    dialog_add_product_title: "إضافة منتج جديد",
    dialog_edit_product_desc: "تحديث تفاصيل هذا المنتج.",
    dialog_add_product_desc: "املأ تفاصيل المنتج الجديد.",
    form_label_name: "الاسم",
    form_placeholder_name: "اسم المنتج",
    form_label_category: "الفئة",
    form_placeholder_category: "مثال: منتجات طازجة",
    form_label_price: "السعر (ريال يمني)",
    form_label_stock: "كمية المخزون",
    form_label_image_url: "رابط الصورة",
    form_placeholder_image_url: "https://example.com/image.jpg",
    form_alt_current_image: "صورة المنتج الحالية",
    form_label_min_order: "أدنى كمية للطلب",
    form_label_online: "متوفر على الإنترنت",
    validation_message_required_fields: "الاسم والفئة والسعر مطلوبة.",
    toast_product_updated_title: "تم تحديث المنتج",
    toast_product_updated_desc: "تم تحديث {productName}.",
    toast_product_added_title: "تمت إضافة المنتج",
    toast_product_added_desc: "تمت إضافة {productName}.",
    confirm_delete_product: "هل أنت متأكد أنك تريد حذف هذا المنتج؟",
    toast_product_deleted_title: "تم حذف المنتج",
    toast_product_deleted_desc: "تمت إزالة المنتج.",
    product_image_generation_title: "إنشاء صور بالذكاء الاصطناعي",
    product_image_generation_description: "قم بتحميل صورة لتحسينها، أو دع الذكاء الاصطناعي ينشئ واحدة بناءً على تفاصيل المنتج.",
    upload_for_ai_label: "تحميل صورة لمعالجة الذكاء الاصطناعي",
    generate_enhance_ai_button: "إنشاء/تحسين بالذكاء الاصطناعي",
    toast_generating_image_title: "جارٍ إنشاء الصورة",
    toast_generating_image_desc: "الذكاء الاصطناعي يعمل على صورة منتجك...",
    toast_image_generated_success: "تم إنشاء صورة الذكاء الاصطناعي بنجاح!",
    toast_image_generated_error_desc: "فشل إنشاء الصورة بالذكاء الاصطناعي. يرجى المحاولة مرة أخرى أو التحقق من التفاصيل.",
    validation_message_name_category_for_ai: "اسم المنتج والفئة مطلوبان لإنشاء صور الذكاء الاصطناعي.",
    ai_hint_for_generation_label: "تلميح للذكاء الاصطناعي للصورة (اختياري)",
    ai_hint_for_generation_placeholder: "مثال: على خلفية بيضاء، لقطة نمط حياة",
  },
  wholesalerDashboard: {
    quickActions: "إجراءات سريعة",
    quickActionsDescription: "إدارة عمليات البيع بالجملة الخاصة بك.",
    subscriptionStatus: "حالة الاشتراك",
    currentSubscriptionActive: "اشتراكك الحالي نشط.",
    renewsOn: "يتجدد في: {date}",
    manageSubscription: "إدارة الاشتراك",
    monthlyRevenue: "الإيرادات الشهرية", 
    createSpecialOffers: "إنشاء عروض خاصة",
    subscriptionExpiredTitle: "انتهى الاشتراك",
    subscriptionExpiredMessage: "لقد انتهى اشتراكك بالجملة في ماركت سينك. يرجى التجديد لمواصلة استخدام جميع الميزات.",
  },
   ownerPurchases: {
    title: "طلبات العملاء",
    description: "عرض وإدارة ومعالجة طلبات العملاء الواردة.",
    filterOrdersButton: "تصفية الطلبات",
    invoiceTitle: "فاتورة",
    orderIdLabel: "رقم الطلب",
    dateLabel: "التاريخ",
    customerLabel: "العميل",
    addressLabel: "العنوان",
    itemsLabel: "العناصر",
    productHeader: "المنتج",
    quantityHeader: "الكمية",
    priceHeader: "السعر (ريال يمني)",
    totalHeader: "الإجمالي (ريال يمني)",
    totalAmountLabel: "المبلغ الإجمالي (ريال يمني)",
    printError: "لم يتمكن من فتح نافذة الطباعة. يرجى التحقق من إعدادات المتصفح.",
    noOrdersFoundTitle: "لم يتم العثور على طلبات",
    noOrdersInStatus: "لا توجد طلبات بالحالة \"{status}\".",
    itemsCountLabel: "{count} عناصر",
    totalLabel: "الإجمالي (ريال يمني)",
    andMoreItems: "، و {count} عناصر أخرى...",
    viewDetailsButton: "عرض التفاصيل",
    processOrderButton: "معالجة الطلب",
    markAsShippedButton: "وضع علامة كـ مشحون",
    printInvoiceButton: "طباعة الفاتورة",
    tabActive: "نشطة",
    tabPending: "معلقة",
    tabProcessing: "قيد المعالجة",
    tabShipped: "تم الشحن",
    tabDelivered: "تم التوصيل",
    tabCancelled: "ملغاة",
    tabAll: "الكل",
    status_pending: "معلق",
    status_processing: "قيد المعالجة",
    status_shipped: "تم الشحن",
    status_delivered: "تم التوصيل",
    status_cancelled: "ملغى",
    approvedByLabel: "تمت الموافقة بواسطة {name} في {time}",
  },
  ownerOrderDetails: {
    invoiceTitle: "فاتورة",
    storeName: "متجر ماركت سينك",
    storeAddress: "عنوان متجرك، المدينة، الرمز البريدي",
    storeContact: "<EMAIL> | (*************",
    invoiceTo: "فاتورة إلى:",
    invoiceNumber: "رقم الفاتورة",
    orderDate: "تاريخ الطلب",
    paymentStatus: "حالة الدفع",
    paymentStatusPaid: "مدفوع (مثال)",
    item: "العنصر",
    quantity: "الكمية",
    unitPrice: "سعر الوحدة (ريال يمني)",
    total: "الإجمالي (ريال يمني)",
    grandTotal: "المجموع الكلي (ريال يمني)",
    thankYou: "شكرا لتعاملكم معنا!",
    questionsContact: "أسئلة؟ اتصل بنا على <EMAIL>",
    footerText: "ماركت سينك - حلول سوبر ماركت حديثة",
    printError: "لم يتمكن من فتح نافذة الطباعة. يرجى التحقق من إعدادات المتصفح.",
    orderNotFoundTitle: "الطلب غير موجود",
    orderNotFoundDesc: "الطلب بالمعرف \"{orderId}\" لم يتم العثور عليه.",
    goBackButton: "العودة",
    backToOrdersButton: "العودة إلى الطلبات",
    orderIdLabel: "رقم الطلب",
    dateLabel: "التاريخ",
    customerDetailsTitle: "تفاصيل العميل",
    nameLabel: "الاسم",
    addressLabel: "العنوان",
    paymentStatusTitle: "الدفع والحالة",
    totalAmountLabel: "المبلغ الإجمالي (ريال يمني)",
    paymentStatusLabel: "حالة الدفع",
    paymentStatusMock: "مدفوع (وهمي)",
    orderStatusLabel: "حالة الطلب",
    orderItemsTitle: "عناصر الطلب",
    imageHeader: "صورة",
    productHeader: "المنتج",
    quantityHeader: "الكمية",
    unitPriceHeader: "سعر الوحدة (ريال يمني)",
    subtotalHeader: "المجموع الفرعي (ريال يمني)",
    totalLabel: "الإجمالي (ريال يمني)",
    printInvoiceButton: "طباعة الفاتورة",
    processOrderButton: "معالجة الطلب",
    markAsShippedButton: "وضع علامة كـ مشحون",
    modifyOrderButton: "تعديل الطلب",
    contactCustomerButton: "الاتصال بالعميل",
    contactingCustomerPlaceholder: "محاكاة الاتصال بالعميل {customerName}... (الميزة قيد التطوير)",
    status_pending: "معلق",
    status_processing: "قيد المعالجة",
    status_shipped: "تم الشحن",
    status_delivered: "تم التوصيل",
    status_cancelled: "ملغى",
  },
  employeeTransactions: {
    title: "تسجيل المعاملات",
    description: "إدارة مدفوعات العملاء وتسجيل معاملات البيع.",
    newTransactionTitle: "معاملة جديدة",
    newTransactionDescription: "تسجيل دفعة أو عملية بيع جديدة.",
    orderIdLabel: "رقم الطلب / المرجع",
    orderIdPlaceholder: "مثال: ORD12345 أو زائر",
    orderIdPlaceholderOptional: "مثال: ORD12345 (اختياري، يُنشأ تلقائيًا إذا ترك فارغًا)",
    amountLabel: "المبلغ (ريال يمني)",
    paymentMethodLabel: "طريقة الدفع",
    paymentMethodPlaceholder: "اختر الطريقة",
    paymentMethodCash: "نقدًا",
    paymentMethodAlKuraimiBank: "بنك الكريمي",
    paymentMethodEWallet: "حوالة إلى محفظة إلكترونية",
    recordTransactionButton: "تسجيل المعاملة",
    recordCashSaleButton: "تسجيل عملية بيع نقدية",
    recordCreditSaleButton: "تسجيل عملية بيع آجلة (دين)",
    printReceiptButton: "طباعة الإيصال",
    recentTransactionsTitle: "المعاملات الأخيرة",
    recentTransactionsDescription: "قائمة بالمعاملات المسجلة مؤخرًا.",
    viewFullLogButton: "عرض السجل الكامل",
    searchTransactionsPlaceholder: "بحث في المعاملات حسب رقم الطلب أو العميل...",
    noRecentTransactions: "لا توجد معاملات أخيرة.",
    tableHeaderOrderId: "رقم الطلب",
    tableHeaderCustomer: "العميل",
    tableHeaderAmount: "المبلغ (ريال يمني)",
    tableHeaderMethod: "الطريقة",
    tableHeaderMethodOrType: "الطريقة / النوع",
    tableHeaderTime: "الوقت",
    tableHeaderStatus: "الحالة",
    statusCompleted: "مكتمل",
    statusPending: "معلق",
    statusUnpaid: "غير مدفوع",
    transactionTypeLabel: "نوع المعاملة",
    transactionTypePlaceholder: "اختر نوع المعاملة",
    cashSaleTransactionType: "بيع نقدي",
    creditSaleTransactionType: "بيع آجل (دين)",
    customerNameLabel: "اسم العميل (للبيع الآجل)",
    customerNameCashLabel: "اسم العميل (اختياري للبيع النقدي)",
    customerNamePlaceholder: "أدخل اسم العميل الكامل",
    customerNameCashPlaceholder: "اختياري: أدخل اسم العميل",
    notesPlaceholder: "ملاحظات المعاملة (مثل الأصناف المشتراة بالدين)",
    errorOwnerNotAssociated: "الموظف غير مرتبط بمالك. لا يمكن تسجيل المعاملات.",
    errorInvalidAmount: "المبلغ المدخل غير صالح. يجب أن يكون المبلغ أكبر من صفر.",
    errorPaymentMethodRequired: "طريقة الدفع مطلوبة للمبيعات النقدية.",
    errorPermissionDeniedCredit: "ليس لديك صلاحية لمنح الائتمان.",
    permissionDenied: "تم رفض الإذن",
    errorCustomerNameRequired: "اسم العميل مطلوب للمبيعات الآجلة.",
    successCashSaleRecorded: "تم تسجيل البيع النقدي بنجاح.",
    successCreditSaleRecorded: "تم تسجيل البيع الآجل لـ {customerName} كدين.",
    creditSaleItemName: "بيع آجل (مرجع: {orderId})",
    defaultDebtNote: "بيع آجل مسجل عبر صفحة المعاملات. طلب/مرجع: {orderId}",
    receiptTitle: "إيصال المعاملة",
    transactionIdLabel: "معرف المعاملة",
    receiptFooterThanks: "شكرا لتعاملكم معنا!",
    noTransactionToPrint: "لا توجد معاملة محددة أو مسجلة للطباعة.",
  },
  wholesalerProducts: {
    title: "إدارة المنتجات",
    description: "إدراج وتعديل وإدارة المنتجات التي تقدمها للبيع بالجملة.",
    addNewProductButton: "إضافة منتج جديد",
    productListingsTitle: "قوائم منتجاتك",
    productListingsDesc: "الإشراف على كتالوج منتجات البيع بالجملة الخاص بك.",
    filterPlaceholder: "تصفية المنتجات حسب الاسم أو الرقم التعريفي أو الفئة...",
    sortByButton: "ترتيب حسب",
    noProductsMessage: "لم تقم بإدراج أي منتجات بعد أو لا توجد منتجات تطابق الفلتر. انقر فوق \"إضافة منتج جديد\" للبدء.",
    headerImage: "صورة",
    headerName: "الاسم",
    headerCategory: "الفئة",
    headerPricePerUnit: "السعر/الوحدة (ريال يمني)",
    headerMinQty: "أدنى كمية",
    headerStock: "المخزون",
    headerAvailability: "التوفر",
    headerVisibility: "الرؤية",
    openMenu: "فتح القائمة",
    menuEditProduct: "تعديل المنتج",
    menuMakePrivate: "جعله خاصًا",
    menuMakePublic: "جعله عامًا",
    menuViewAnalytics: "عرض التحليلات",
    menuDeleteProduct: "حذف المنتج",
    analyticsTitle: "التحليلات",
    analyticsDesc: "تحليلات لهذا المنتج (غير منفذة).",
    userNotAuthenticated: "المستخدم غير مصادق عليه.",
    newProductDefaultName: "منتج جملة جديد",
    defaultCategory: "سلع بالجملة",
    productAddedTitle: "تمت إضافة المنتج",
    productAddedDesc: "تم إدراج منتج جملة جديد.",
    editProductTitle: "تعديل المنتج",
    editProductDesc: "جاري تعديل {productName}. نموذج التعديل الكامل غير منفذ هنا بعد.",
    productDeletedTitle: "تم حذف المنتج",
    productDeletedDesc: "تمت إزالة قائمة المنتج.",
    visibilityChangedTitle: "تم تغيير الرؤية",
    visibilityChangedDesc: "{productName} الآن {status}.",
    statusPublic: "عام",
    statusPrivate: "خاص",
    stockStatusAvailable: "متوفر",
    stockStatusLowStock: "مخزون منخفض",
    stockStatusUnavailable: "غير متوفر",
    visibilityPublic: "عام",
    visibilityPrivate: "خاص",
  },
  backupManagement: {
    title: "إدارة النسخ الاحتياطي",
    description: "إدارة وإنشاء نسخ احتياطية من بيانات التطبيق الهامة.",
    createBackup: "إنشاء نسخة احتياطية",
    lastBackupLabel: "آخر نسخة احتياطية:",
    backupInProgress: "جارٍ إنشاء النسخة الاحتياطية...",
    backupSuccessful: "تم إنشاء النسخة الاحتياطية بنجاح في {timestamp}.",
    backupFailed: "فشل إنشاء النسخة الاحتياطية. يرجى المحاولة مرة أخرى.",
    noBackupPerformed: "لم يتم إجراء أي نسخ احتياطي حتى الآن.",
    backupDataFor: "سيقوم هذا بنسخ بيانات المالكين وتجار الجملة احتياطيًا (المنتجات، الديون، إلخ).",
    backupNow: "إنشاء نسخة احتياطية الآن",
    backupDetailsTitle: "تفاصيل النسخة الاحتياطية",
    lastBackupTime: "وقت آخر نسخة احتياطية",
    backupSummary: "تم نسخ {productCount} منتجًا و {debtCount} سجل دين احتياطيًا.",
    backupDataNote: "ملاحظة: هذه نسخة احتياطية محاكاة تستخدم التخزين المحلي للمتصفح. للإنتاج، استخدم حلاً خلفيًا قويًا.",
    restoreOperationsTitle: "عمليات الاستعادة",
    restoreOperationsDesc: "استعادة البيانات من نسخة احتياطية سابقة.",
    restoreButton: "استعادة آخر نسخة احتياطية",
    restoreInProgress: "جارٍ الاستعادة...",
    restoreSuccessfulTitle: "تمت الاستعادة بنجاح",
    restoreSuccessfulDesc: "تمت استعادة البيانات من النسخة الاحتياطية بتاريخ {timestamp}.",
    restoreFailedTitle: "فشل الاستعادة",
    noBackupToRestore: "لم يتم العثور على نسخة احتياطية للاستعادة منها.",
    restoreWarning: "ستقوم هذه العملية بالكتابة فوق البيانات الحالية ببيانات النسخة الاحتياطية الأخيرة. استمر بحذر.",
    noBackupToRestoreNote: "لا توجد نسخة احتياطية متاحة للاستعادة.",
    // تبويبات إدارة النسخ الاحتياطية
    manualBackup: "نسخ احتياطي يدوي",
    scheduler: "المجدول",
    history: "السجل",
    // مجدول النسخ الاحتياطية
    backupSchedules: "جداول النسخ الاحتياطية",
    automateBackupProcess: "أتمتة عملية النسخ الاحتياطي مع العمليات المجدولة",
    addSchedule: "إضافة جدولة",
    scheduleName: "اسم الجدولة",
    frequency: "التكرار",
    frequency_daily: "يومي",
    frequency_weekly: "أسبوعي",
    frequency_monthly: "شهري",
    time: "الوقت",
    dayOfWeek: "يوم الأسبوع",
    dayOfMonth: "يوم الشهر",
    dataTypes: "أنواع البيانات",
    nextRun: "التشغيل التالي",
    active: "نشط",
    inactive: "غير نشط",
    editSchedule: "تعديل الجدولة",
    scheduleUpdated: "تم تحديث الجدولة",
    scheduleEnabled: "تم تفعيل الجدولة بنجاح",
    scheduleDisabled: "تم إلغاء تفعيل الجدولة بنجاح",
    confirmDeleteSchedule: "هل أنت متأكد من حذف هذه الجدولة؟",
    scheduleDeleted: "تم حذف الجدولة",
    scheduleDeletedSuccess: "تم حذف الجدولة بنجاح",
    scheduleSaved: "تم حفظ الجدولة",
    scheduleCreated: "تم إنشاء الجدولة بنجاح",
    noSchedulesConfigured: "لا توجد جداول نسخ احتياطية مهيأة",
    createFirstSchedule: "إنشاء أول جدولة",
    notScheduled: "غير مجدول",
    inHours: "خلال {hours} ساعات",
    weekday_0: "الأحد",
    weekday_1: "الاثنين",
    weekday_2: "الثلاثاء",
    weekday_3: "الأربعاء",
    weekday_4: "الخميس",
    weekday_5: "الجمعة",
    weekday_6: "السبت",
    // سجل النسخ الاحتياطية
    totalBackups: "إجمالي النسخ الاحتياطية",
    successful: "ناجحة",
    successRate: "معدل النجاح",
    totalStorage: "إجمالي التخزين",
    usedStorage: "مساحة مستخدمة",
    averageDuration: "متوسط المدة",
    perBackup: "لكل نسخة احتياطية",
    backupHistory: "سجل النسخ الاحتياطية",
    recentBackupOperations: "عمليات النسخ الاحتياطي الأخيرة وحالتها",
    timestamp: "الطابع الزمني",
    type: "النوع",
    size: "الحجم",
    duration: "المدة",
    backupType_manual: "يدوي",
    backupType_scheduled: "مجدول",
    backupType_automatic: "تلقائي",
    completed: "مكتمل",
    failed: "فاشل",
    inProgress: "قيد التنفيذ",
    download: "تحميل",
    restore: "استعادة",
    downloadFailed: "فشل التحميل",
    cannotDownloadIncompleteBackup: "لا يمكن تحميل نسخة احتياطية غير مكتملة",
    downloadStarted: "بدأ التحميل",
    backupDownloadStarted: "بدأ تحميل النسخة الاحتياطية {id}",
    restoreFailed: "فشل الاستعادة",
    cannotRestoreIncompleteBackup: "لا يمكن استعادة نسخة احتياطية غير مكتملة",
    confirmRestoreBackup: "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ ستقوم بالكتابة فوق البيانات الحالية.",
    restoreStarted: "بدأت الاستعادة",
    backupRestoreStarted: "بدأت عملية استعادة النسخة الاحتياطية {id}",
    confirmDeleteBackup: "هل أنت متأكد من حذف هذه النسخة الاحتياطية؟",
    backupDeleted: "تم حذف النسخة الاحتياطية",
    backupDeletedSuccess: "تم حذف النسخة الاحتياطية بنجاح",
    noBackupHistory: "لا يوجد سجل للنسخ الاحتياطية",
  },
  customerDebts: {
    title: "ديوني",
    description: "اطلع على ديونك المستحقة للمتجر.",
    debtRecordsTitle: "سجلات ديونك",
    debtRecordsDescription: "نظرة عامة على ديونك الحالية.",
    filterPlaceholder: "تصفية حسب التاريخ أو الوصف أو العناصر...",
    noDebtsFound: "ليس لديك أي ديون مستحقة لهذا المتجر.",
    showingRecordsCount: "عرض {count} سجلات ديون.",
    descriptionHeader: "الوصف/الملاحظات",
    itemsHeader: "العناصر",
    printStatement: "طباعة كشف الحساب",
    statementOfAccount: "كشف حساب",
    customerNameLabel: "اسم العميل",
    dateIssuedLabel: "تاريخ الإصدار",
    totalOutstandingDebt: "إجمالي الديون المستحقة (ريال يمني)",
    noDebtsAccount: "هذا الحساب غير مرتبط بمتجر لتتبع الديون أو لم يتم تسجيل أي ديون.",
  },
  employeeDashboard: {
    title: "لوحة تحكم الموظف",
    taskNewOrders: "طلبات جديدة للمعالجة",
    taskOrdersToDispatch: "طلبات للإرسال",
    taskPendingTransactions: "معاملات معلقة",
    viewTaskAction: "الانتقال إلى المهمة",
    quickActionsTitle: "إجراءات سريعة",
    quickActionsDesc: "الوصول إلى مهامك اليومية بسرعة.",
    recentActivityTitle: "النشاط الأخير",
    recentActivityDesc: "أحدث إجراءاتك وإشعاراتك.",
    activityItemApprovedOrder: "تمت الموافقة على الطلب #{orderId}",
    activityItemRecordedPayment: "تم تسجيل دفعة نقدية للطلب #{orderId}",
    activityItemDispatchedOrder: "تم إرسال الطلب #{orderId}",
    activityItemNewOrderNotification: "إشعار: تم استلام طلب جديد ذو أولوية عالية.",
  },
  employeeApproveOrders: {
    title: "الموافقة على طلبات العملاء",
    description: "مراجعة ورفض أو الموافقة على طلبات العملاء الواردة.",
    pendingApprovalTitle: "طلبات معلقة للموافقة",
    pendingApprovalDesc: "مراجعة تفاصيل الطلبات الجديدة واتخاذ الإجراء المناسب.",
    filterPlaceholder: "تصفية حسب رقم الطلب أو اسم العميل...",
    orderId: "رقم الطلب",
    customerName: "العميل",
    orderDate: "تاريخ الطلب",
    items: "العناصر",
    totalAmount: "المبلغ الإجمالي (ريال يمني)",
    approveButton: "الموافقة على الطلب",
    rejectButton: "رفض الطلب",
    noPendingOrders: "لا توجد حاليًا طلبات معلقة للموافقة.",
    orderApprovedTitle: "تمت الموافقة على الطلب",
    orderApprovedDesc: "تمت الموافقة على الطلب {orderId} ونقله للمعالجة.",
    orderRejectedTitle: "تم رفض الطلب",
    orderRejectedDesc: "تم رفض الطلب {orderId}.",
    viewOrderDetails: "عرض تفاصيل الطلب",
    permissionDenied: "تم رفض الإذن",
    permissionDeniedMessage: "ليس لديك إذن للموافقة على الطلبات أو رفضها.",
  },
  employeeManageBalances: { 
    description: "عرض وإدارة أرصدة العملاء وحسابات الائتمان.",
    underDevelopmentTitle: "إدارة أرصدة العملاء",
    underDevelopmentMessage: "تتيح هذه الميزة للموظفين عرض وربما تعديل أرصدة العملاء ومعلومات الائتمان.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  employeeProcessReturns: { 
    description: "معالجة مرتجعات العملاء وإدارة تعديلات المخزون ذات الصلة.",
    underDevelopmentTitle: "معالجة مرتجعات المبيعات",
    underDevelopmentMessage: "ستمكّن هذه الميزة الموظفين من التعامل مع مرتجعات العملاء وإصدار المبالغ المستردة أو الاعتمادات وتحديث المخزون وفقًا لذلك.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  employeeManageProducts: {
    title: "إدارة المنتجات",
    description: "إدارة قوائم المنتجات وتحديث الأسعار وتعديل تفاصيل المخزون بناءً على الأذونات.",
    totalProducts: "إجمالي المنتجات",
    lowStockItems: "منتجات منخفضة المخزون",
    outOfStockItems: "منتجات نفد مخزونها",
    activeProducts: "منتجات نشطة",
    productsListTitle: "قائمة المنتجات",
    productsListDesc: "عرض وإدارة المنتجات المتاحة",
    addProduct: "إضافة منتج",
    editProduct: "تعديل منتج",
    productDetails: "تفاصيل المنتج",
    searchProductsPlaceholder: "البحث في المنتجات...",
    filterByCategory: "تصفية حسب الفئة",
    filterByStatus: "تصفية حسب الحالة",
    allCategories: "جميع الفئات",
    allStatuses: "جميع الحالات",
    productName: "اسم المنتج",
    productDescription: "وصف المنتج",
    category: "الفئة",
    price: "السعر",
    cost: "التكلفة",
    stock: "المخزون",
    minStock: "الحد الأدنى للمخزون",
    maxStock: "الحد الأقصى للمخزون",
    barcode: "الباركود",
    sku: "رمز المنتج",
    supplier: "المورد",
    status: "الحالة",
    stockStatus: "حالة المخزون",
    statusActive: "نشط",
    statusInactive: "غير نشط",
    statusDiscontinued: "متوقف",
    inStock: "متوفر",
    lowStock: "مخزون منخفض",
    outOfStock: "نفد المخزون",
    lastUpdated: "آخر تحديث",
    updatedBy: "تم التحديث بواسطة",
    addProductTitle: "إضافة منتج جديد",
    editProductTitle: "تعديل المنتج",
    productAddedSuccess: "تم إضافة المنتج بنجاح",
    productUpdatedSuccess: "تم تحديث المنتج بنجاح",
    productDeletedSuccess: "تم حذف المنتج بنجاح",
    errorAddingProduct: "خطأ في إضافة المنتج",
    errorUpdatingProduct: "خطأ في تحديث المنتج",
    errorDeletingProduct: "خطأ في حذف المنتج",
    noProductsFound: "لم يتم العثور على منتجات",
    loadingProducts: "جاري تحميل المنتجات...",
    confirmDeleteProduct: "هل أنت متأكد من حذف هذا المنتج؟",
    deleteProduct: "حذف المنتج",
    viewProduct: "عرض المنتج",
    duplicateProduct: "نسخ المنتج",
    exportProducts: "تصدير المنتجات",
    importProducts: "استيراد المنتجات",
    bulkActions: "إجراءات مجمعة",
    selectAll: "تحديد الكل",
    selectedItems: "العناصر المحددة",
    categoryManagement: "إدارة الفئات",
    addCategory: "إضافة فئة",
    editCategory: "تعديل فئة",
    deleteCategory: "حذف فئة",
    categoryName: "اسم الفئة",
    categoryDescription: "وصف الفئة",
    productCount: "عدد المنتجات"
  },
  employeeSetCreditLimits: {
    description: "تعيين وإدارة حدود الائتمان للعملاء المعتمدين.",
    underDevelopmentTitle: "تعيين حدود ائتمان العملاء",
    underDevelopmentMessage: "تتيح هذه الميزة للموظفين المعتمدين تحديد وتعديل حدود الائتمان للعملاء.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا.",
    manageCreditLimits: "إدارة حدود الائتمان",
    changeHistory: "سجل التغييرات",
    searchAndFilter: "البحث والتصفية",
    searchPlaceholder: "البحث بالاسم، رقم العميل، أو الهاتف...",
    filterByStatus: "تصفية حسب الحالة",
    allStatuses: "جميع الحالات",
    bulkUpdate: "تحديث جماعي",
    exportData: "تصدير البيانات",
    customersList: "قائمة العملاء وحدود الائتمان",
    showingCustomers: "عرض {filtered} من {total} عميل",
    customerName: "اسم العميل",
    phoneNumber: "رقم الهاتف",
    netBalance: "الرصيد الصافي",
    creditLimit: "حد الائتمان",
    availableCredit: "الائتمان المتاح",
    creditScore: "نقاط الائتمان",
    status: "الحالة",
    actions: "الإجراءات",
    edit: "تعديل",
    editCreditLimit: "تعديل حد الائتمان",
    editCreditLimitFor: "تعديل حد الائتمان للعميل: {customerName}",
    currentCreditLimit: "حد الائتمان الحالي",
    newCreditLimit: "حد الائتمان الجديد (ر.ي)",
    enterNewLimit: "أدخل حد الائتمان الجديد",
    changeReason: "سبب التغيير",
    selectChangeReason: "اختر سبب التغيير",
    currentBalance: "الرصيد الحالي",
    newAvailableCredit: "الائتمان المتاح الجديد",
    confirmationWarning: "تأكد من صحة المبلغ وسبب التغيير قبل التأكيد. هذا الإجراء سيؤثر على حالة العميل.",
    cancel: "إلغاء",
    updateCreditLimit: "تحديث حد الائتمان",
    creditHistoryTitle: "سجل تغييرات حدود الائتمان",
    creditHistoryDesc: "عرض جميع التغييرات التي تمت على حدود الائتمان",
    previousLimit: "الحد السابق",
    newLimit: "الحد الجديد",
    change: "التغيير",
    changedBy: "تم بواسطة",
    changeDate: "تاريخ التغيير",
    noHistoryRecords: "لا توجد تغييرات في سجل حدود الائتمان",
    bulkUpdateTitle: "تحديث جماعي لحدود الائتمان",
    bulkUpdateDesc: "تطبيق تغيير نسبي على حدود الائتمان لمجموعة من العملاء",
    changePercentage: "نسبة التغيير (%)",
    percentageExample: "مثال: 10 للزيادة 10% أو -5 للتقليل 5%",
    percentageNote: "أدخل رقم موجب للزيادة أو سالب للتقليل",
    minLimit: "الحد الأدنى (اختياري)",
    maxLimit: "الحد الأعلى (اختياري)",
    noMaxLimit: "بدون حد أعلى",
    bulkUpdateReason: "سبب التحديث الجماعي",
    selectBulkReason: "اختر سبب التحديث",
    previewUpdate: "معاينة التحديث:",
    affectedCustomers: "العملاء المتأثرون: {count} عميل",
    changePercentageDisplay: "نسبة التغيير: {percentage}%",
    minLimitDisplay: "الحد الأدنى للتطبيق: {limit} ر.ي",
    maxLimitDisplay: "الحد الأعلى للتطبيق: {limit} ر.ي",
    bulkUpdateWarning: "تحذير: هذا الإجراء سيؤثر على عدة عملاء في نفس الوقت. تأكد من صحة البيانات قبل التأكيد.",
    applyBulkUpdate: "تطبيق التحديث الجماعي"
  },
  employeeGrantCredit: {
    description: "إدارة ومنح الائتمان للعملاء.",
    underDevelopmentTitle: "منح ائتمان للعملاء",
    underDevelopmentMessage: "تتيح هذه الميزة للموظفين المعتمدين إدارة خطوط الائتمان للعملاء.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  ownerProcessReturns: {
    description: "معالجة مرتجعات العملاء وإدارة تعديلات المخزون ذات الصلة.",
    underDevelopmentTitle: "معالجة مرتجعات المبيعات",
    underDevelopmentMessage: "ستمكّن هذه الميزة المالكين من التعامل مع مرتجعات العملاء وإصدار المبالغ المستردة أو الاعتمادات وتحديث المخزون وفقًا لذلك.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  ownerSetCreditLimits: {
    description: "تعيين وإدارة حدود الائتمان للعملاء المعتمدين.",
    underDevelopmentTitle: "تعيين حدود ائتمان العملاء",
    underDevelopmentMessage: "تتيح هذه الميزة للمالكين تحديد وتعديل حدود الائتمان للعملاء.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا.",
    manageCreditLimitsAdvanced: "إدارة حدود الائتمان للعملاء",
    manageCreditLimitsDesc: "عرض وتعديل حدود الائتمان مع إعدادات السياسة المتقدمة",
    policySettings: "إعدادات السياسة",
    currentCreditPolicy: "السياسة الحالية للائتمان",
    maxLimit: "الحد الأقصى",
    defaultLimit: "الحد الافتراضي",
    riskThreshold: "عتبة المخاطر",
    autoApproval: "الموافقة التلقائية",
    totalCustomers: "إجمالي العملاء",
    registeredCustomer: "عميل مسجل",
    goodStatus: "حالة جيدة",
    warningStatus: "تحذير",
    overdueStatus: "متأخر",
    customer: "عميل",
    totalCredit: "إجمالي الائتمان",
    editCreditLimitOwner: "تعديل حد الائتمان - صلاحية المالك",
    maxAllowedLimit: "الحد الأقصى المسموح: {limit} ر.ي",
    administrativeDecision: "قرار إداري",
    other: "أخرى",
    ownerFullAuthority: "كمالك، لديك صلاحية كاملة لتعديل حدود الائتمان دون قيود إضافية.",
    creditPolicySettings: "إعدادات سياسة الائتمان",
    creditPolicyDesc: "تحديد القواعد والحدود العامة لسياسة الائتمان في المتجر",
    maxCreditLimit: "الحد الأقصى للائتمان (ر.ي)",
    defaultCreditLimit: "الحد الافتراضي (ر.ي)",
    riskThresholdPercent: "عتبة المخاطر (%)",
    riskThresholdNote: "النسبة المئوية من حد الائتمان التي تؤدي إلى تحذير",
    autoApprovalLimit: "حد الموافقة التلقائية (ر.ي)",
    autoApprovalNote: "الحد الذي يمكن للموظفين الموافقة عليه تلقائياً",
    policyChangeWarning: "تغيير هذه الإعدادات سيؤثر على جميع العمليات المستقبلية للائتمان.",
    saveSettings: "حفظ الإعدادات"
  },
  ownerOnlineStore: {
    title: "إدارة المتجر الإلكتروني",
    description: "تحكم في المنتجات الظاهرة للعملاء في متجرك الإلكتروني.",
    productVisibilityTitle: "رؤية المنتج عبر الإنترنت",
    productVisibilityDesc: "قم بتبديل المنتجات لتظهر أو تختفي من واجهة متجرك الإلكتروني. يتم حفظ التغييرات تلقائيًا. لا يمكن عرض المنتجات غير المتوفرة في المخزون عبر الإنترنت.",
    filterPlaceholder: "تصفية حسب الاسم أو الفئة...",
    visibilityStatusPlaceholder: "حالة الرؤية",
    statusAllProducts: "جميع المنتجات",
    statusOnline: "متصل",
    statusOffline: "غير متصل",
    noProductsMatchFilters: "لا توجد منتجات تطابق مرشحاتك الحالية.",
    headerImage: "صورة",
    headerName: "الاسم",
    headerCategory: "الفئة",
    headerStock: "المخزون",
    headerOnlineVisibility: "الرؤية عبر الإنترنت",
    stockStatusOutOfStock: "نفذ من المخزون",
    stockStatusLowStock: "مخزون منخفض",
    stockStatusInStock: "متوفر في المخزون",
    toggleOnlineStatusAria: "تبديل حالة اتصال {productName}",
    showingProductsCount: "عرض {count} من {total} منتجات.",
    confirmSettingsButton: "تأكيد الإعدادات",
    toastVisibilityUpdatedTitle: "تم تحديث الرؤية",
    toastVisibilityUpdatedDesc: "المنتج {productName} الآن {status}.",
    toastSettingsConfirmedTitle: "تم تأكيد الإعدادات",
    toastSettingsConfirmedDesc: "تم تحديث إعدادات رؤية منتجات المتجر الإلكتروني."
  },
  ownerPredictions: {
    title: "تنبؤات المخزون الذكية",
    description: "استفد من الذكاء الاصطناعي لتوقع الطلب وتحسين مستويات المخزون لديك.",
    formTitle: "توقع الطلب",
    formDescription: "أدخل تفاصيل المنتج والبيانات التاريخية للحصول على توقعات الطلب وتوصيات الطلبات.",
    howItWorksTitle: "كيف يعمل",
    howItWorksP1: "يحلل نموذج الذكاء الاصطناعي لدينا بيانات المبيعات التاريخية ومستويات المخزون الحالية وفترات التسليم والاتجاهات الموسمية لتقديم توقعات دقيقة للطلب.",
    howItWorksP2: "يساعدك هذا على تقليل التخزين الزائد ومنع نفاد المخزون وتحسين إدارة المخزون لديك لربحية أفضل.",
    howItWorksNoteTitle: "ملاحظة",
    howItWorksNoteP: "تعتمد التنبؤات على البيانات المقدمة. كلما كانت بياناتك أكثر دقة وشمولاً، كانت التنبؤات أفضل."
  },
  inventoryPredictionForm: {
    productCategoryLabel: "فئة المنتج",
    productCategoryPlaceholder: "مثال: منتجات الألبان",
    productCategoryDescription: "فئة المنتج الذي تريد توقع الطلب عليه.",
    historicalSalesDataLabel: "بيانات المبيعات التاريخية (JSON)",
    historicalSalesDataPlaceholder: '[{"date": "YYYY-MM-DD", "quantitySold": 0, "price": 0.00}]',
    historicalSalesDataDescription: "قدم بيانات المبيعات التاريخية بتنسيق JSON.",
    currentStockLevelLabel: "مستوى المخزون الحالي",
    currentStockLevelPlaceholder: "مثال: 100",
    leadTimeDaysLabel: "فترة التسليم (أيام)",
    leadTimeDaysPlaceholder: "مثال: 7",
    leadTimeDaysDescription: "الوقت الذي يستغرقه وصول المخزون الجديد.",
    seasonalTrendsLabel: "الاتجاهات الموسمية (JSON، اختياري)",
    seasonalTrendsPlaceholder: 'مثال: [{"period": "Summer", "impactFactor": 1.2}]',
    seasonalTrendsDescription: "أي اتجاهات موسمية معروفة تؤثر على الطلب، بتنسيق JSON.",
    getPredictionButton: "احصل على التوقع",
    toastPredictionGeneratedTitle: "تم إنشاء التوقع",
    toastPredictionGeneratedDesc: "توقع الطلب لـ {productCategory} جاهز.",
    toastPredictionErrorTitle: "خطأ في التوقع",
    toastPredictionErrorDesc: "فشل إنشاء التوقع. يرجى التحقق من مدخلاتك أو المحاولة مرة أخرى لاحقًا.",
    toastUnexpectedError: "حدث خطأ غير متوقع.",
    predictionResultTitle: "نتيجة التوقع لـ: {productCategory}",
    predictedDemandLabel: "الطلب المتوقع",
    recommendedOrderQuantityLabel: "كمية الطلب الموصى بها",
    confidenceLevelLabel: "مستوى الثقة",
    explanationLabel: "الشرح",
    unitsLabel: "وحدات"
  },
  ownerSalesReports: {
    title: "تقارير المبيعات",
    description: "تحليل بيانات المبيعات والإيرادات والاتجاهات لسوبر ماركت الخاص بك.",
    overviewTitle: "نظرة عامة على المبيعات",
    overviewDescription: "المقاييس الرئيسية ومؤشرات الأداء لمبيعاتك.",
    detailedReportsTitle: "تقارير مفصلة",
    detailedReportsDescription: "إنشاء وعرض تقارير مبيعات مفصلة لفترات أو منتجات محددة.",
    comingSoon: "ميزات التقارير المفصلة ستتوفر قريبًا.",
    totalSales: "إجمالي المبيعات (ريال يمني)",
    totalOrders: "إجمالي الطلبات",
    averageOrderValue: "متوسط قيمة الطلب (ريال يمني)",
    fromLastMonth: "عن الشهر الماضي",
    generateDailyReport: "إنشاء تقرير يومي",
    generateWeeklyReport: "إنشاء تقرير أسبوعي",
    generateMonthlyReport: "إنشاء تقرير شهري",
    generateCustomReport: "إنشاء تقرير مخصص",
    bestSellingProducts: "المنتجات الأكثر مبيعاً",
    bestSellingProductsDescription: "أفضل المنتجات بناءً على حجم المبيعات أو الإيرادات.",
    salesCount: "عدد المبيعات",
    revenueGenerated: "الإيرادات (ريال يمني)",
    reportGenerationInProgressTitle: "جارٍ إنشاء التقرير",
    reportGenerationInProgressDesc: "{reportType} الخاص بك قيد الإنشاء. هذه الميزة هي عنصر نائب.",
    customReportsTitle: "تقارير مخصصة",
    customReportsDescription: "إنشاء تقارير مخصصة بناءً على معايير ونقاط بيانات محددة.",
    customReportsComingSoon: "ستتوفر وظيفة إنشاء التقارير المخصصة هنا.",
  },
  employeeTasks: { 
    title: "إدارة المهام",
    description: "عرض وإدارة المهام والمسؤوليات المعينة لك.",
    underDevelopmentTitle: "نظام إدارة المهام",
    underDevelopmentMessage: "سيوفر هذا القسم واجهة مفصلة للموظفين لعرض مهامهم المعينة وتتبعها وتحديثها. ستشمل الميزات تحديد أولويات المهام وتحديثات الحالة والملاحظات.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  employeeCommunication: {
    title: "مركز الاتصالات",
    description: "التواصل مع العملاء أو أعضاء الفريق الآخرين بشأن الطلبات والمهام.",
    contactsTitle: "جهات الاتصال",
    allContacts: "جميع جهات الاتصال",
    customers: "العملاء",
    employees: "الموظفون",
    suppliers: "الموردون",
    searchContacts: "البحث في جهات الاتصال...",
    selectContactToStart: "اختر جهة اتصال لبدء المحادثة",
    typeMessage: "اكتب رسالتك...",
    sendMessage: "إرسال الرسالة",
    messageSent: "تم إرسال الرسالة",
    messageSentTo: "تم إرسال الرسالة إلى {name}",
    failedToSend: "فشل في إرسال الرسالة",
    online: "متصل",
    offline: "غير متصل",
    lastSeen: "آخر ظهور",
    unreadMessages: "رسائل غير مقروءة",
    orderInquiry: "استفسار عن طلب",
    complaint: "شكوى",
    support: "دعم فني",
    generalMessage: "رسالة عامة",
    messageStatus: {
      sent: "تم الإرسال",
      delivered: "تم التسليم",
      read: "تم القراءة"
    },
    contactTypes: {
      customer: "عميل",
      employee: "موظف",
      supplier: "مورد"
    },
    noContactsFound: "لم يتم العثور على جهات اتصال",
    loadingContacts: "جاري تحميل جهات الاتصال...",
    loadingMessages: "جاري تحميل الرسائل...",
    callContact: "اتصال",
    emailContact: "بريد إلكتروني",
    newConversation: "محادثة جديدة",
    markAsRead: "تحديد كمقروء",
    deleteConversation: "حذف المحادثة",
    blockContact: "حظر جهة الاتصال",
    reportContact: "الإبلاغ عن جهة الاتصال"
  },
  employeeNotifications: {
    title: "الإشعارات",
    description: "ابق على اطلاع بالتنبيهات المهمة والمهام ورسائل النظام.",
    refresh: "تحديث",
    refreshSuccess: "تم التحديث",
    notificationsUpdated: "تم تحديث الإشعارات.",
    loadError: "خطأ في التحميل",
    failedToLoadNotifications: "فشل في تحميل الإشعارات. يرجى المحاولة مرة أخرى.",

    // Notification stats and actions
    notificationStats: "{unread} غير مقروءة من أصل {total} إشعار",
    unread: "غير مقروءة",
    read: "مقروءة",
    markAsRead: "تحديد كمقروءة",
    markAllAsRead: "تحديد الكل كمقروءة",
    deleteNotification: "حذف الإشعار",
    clearAll: "مسح الكل",
    clearAllFilters: "مسح جميع المرشحات",

    // Filter and search
    searchPlaceholder: "البحث في الإشعارات...",
    filterByType: "تصفية حسب النوع",
    filterByPriority: "تصفية حسب الأولوية",
    filterByStatus: "تصفية حسب الحالة",
    allTypes: "جميع الأنواع",
    allPriorities: "جميع الأولويات",
    allStatuses: "جميع الحالات",
    sort: "ترتيب",
    sortBy: "ترتيب حسب",
    sortByDate: "التاريخ",
    sortByPriority: "الأولوية",
    sortByType: "النوع",
    sortOrder: "ترتيب الفرز",
    newestFirst: "الأحدث أولاً",
    oldestFirst: "الأقدم أولاً",
    activeFilters: "المرشحات النشطة",
    search: "البحث",
    type: "النوع",
    priority: "الأولوية",
    status: "الحالة",
    clearFilters: "مسح المرشحات",

    // Notification types
    type_info: "معلومات",
    type_warning: "تحذير",
    type_error: "خطأ",
    type_success: "نجح",
    type_system: "النظام",
    type_order: "طلب",
    type_task: "مهمة",
    type_alert: "تنبيه",

    // Priority levels
    priority_low: "منخفضة",
    priority_medium: "متوسطة",
    priority_high: "عالية",
    priority_urgent: "عاجلة",

    // Empty states
    noNotifications: "لا توجد إشعارات",
    noNotificationsMessage: "ليس لديك أي إشعارات حتى الآن. ستظهر التنبيهات والتحديثات الجديدة هنا.",
    noFilteredNotifications: "لا توجد إشعارات مطابقة",
    noFilteredNotificationsMessage: "لا توجد إشعارات تطابق المرشحات الحالية. جرب تعديل معايير البحث.",

    // Actions and feedback
    markedAsRead: "تم تحديدها كمقروءة",
    notificationMarkedAsRead: "تم تحديد الإشعار كمقروء.",
    allMarkedAsRead: "تم تحديد الكل كمقروء",
    allNotificationsMarkedAsRead: "تم تحديد جميع الإشعارات كمقروءة.",
    notificationDeleted: "تم حذف الإشعار",
    notificationDeletedSuccess: "تم حذف الإشعار بنجاح.",
    notificationsCleared: "تم مسح الإشعارات",
    notificationsClearedSuccess: "تم مسح {count} إشعار.",
    showingNotifications: "عرض {count} من أصل {total} إشعار",

    // Notification details
    expiresAt: "ينتهي في {date}",
  },
  employeeProductLookup: { 
    title: "البحث عن منتج",
    description: "العثور بسرعة على معلومات حول المنتجات، بما في ذلك السعر ومستويات المخزون.",
    underDevelopmentTitle: "الوصول إلى معلومات المنتج",
    underDevelopmentMessage: "ستسمح هذه الميزة للموظفين بالبحث عن المنتجات أو مسحها ضوئيًا للحصول على تفاصيل مثل السعر الحالي وتوافر المخزون والمعلومات الأخرى ذات الصلة.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  employeeShifts: { 
    title: "إدارة الورديات",
    description: "عرض جدول عملك وإدارة الأنشطة المتعلقة بالورديات.",
    underDevelopmentTitle: "نظام إدارة الورديات",
    underDevelopmentMessage: "سيتمكن الموظفون من عرض وردياتهم القادمة وطلب التغييرات (إذا أمكن) وتسجيل الحضور والانصراف. قد يكون للمشرفين قدرات إدارية إضافية.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  employeePerformance: { 
    title: "تقارير الأداء",
    description: "عرض مقاييس وتقارير الأداء الفردية الخاصة بك.",
    underDevelopmentTitle: "نظرة عامة على أداء الموظف",
    underDevelopmentMessage: "سيعرض هذا القسم مؤشرات الأداء الرئيسية للموظفين، مثل الطلبات التي تمت معالجتها ودرجات رضا العملاء (إذا أمكن) والمقاييس الأخرى ذات الصلة.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  employeeReturnsManagement: {
    title: "إدارة المرتجعات",
    description: "معالجة مرتجعات العملاء وإدارة المبالغ المستردة وتحديث المخزون.",
    // Main interface
    pendingReturns: "المرتجعات المعلقة",
    allReturns: "جميع المرتجعات",
    newReturn: "مرتجع جديد",
    searchPlaceholder: "البحث برقم المرتجع، اسم العميل، أو رقم الطلب...",
    filterByStatus: "تصفية حسب الحالة",
    filterByReason: "تصفية حسب السبب",
    allStatuses: "جميع الحالات",
    allReasons: "جميع الأسباب",

    // Return statuses
    statusPending: "معلق",
    statusApproved: "موافق عليه",
    statusRejected: "مرفوض",
    statusProcessed: "تم المعالجة",
    statusRefunded: "تم الاسترداد",

    // Return reasons
    reasonDefective: "منتج معيب",
    reasonWrongItem: "منتج خاطئ",
    reasonNotSatisfied: "عدم رضا العميل",
    reasonDamagedShipping: "تلف أثناء الشحن",
    reasonExpired: "منتج منتهي الصلاحية",
    reasonOther: "أخرى",

    // Refund methods
    refundCash: "استرداد نقدي",
    refundCredit: "رصيد حساب",
    refundExchange: "استبدال",
    refundStoreCredit: "رصيد المتجر",

    // Table headers
    returnId: "رقم المرتجع",
    customerName: "اسم العميل",
    orderId: "رقم الطلب",
    returnDate: "تاريخ المرتجع",
    totalAmount: "المبلغ الإجمالي",
    status: "الحالة",
    actions: "الإجراءات",

    // Actions
    view: "عرض",
    process: "معالجة",
    approve: "موافقة",
    reject: "رفض",
    refund: "استرداد",

    // Return details
    returnDetails: "تفاصيل المرتجع",
    customerInfo: "معلومات العميل",
    orderInfo: "معلومات الطلب",
    returnItems: "المنتجات المرتجعة",
    returnReason: "سبب الإرجاع",
    returnReasonDetails: "تفاصيل السبب",
    refundMethod: "طريقة الاسترداد",
    processedBy: "تم المعالجة بواسطة",
    processedAt: "تاريخ المعالجة",
    approvedBy: "تمت الموافقة بواسطة",
    notes: "ملاحظات",
    attachments: "المرفقات",

    // Item details
    productName: "اسم المنتج",
    originalQuantity: "الكمية الأصلية",
    returnQuantity: "كمية الإرجاع",
    originalPrice: "السعر الأصلي",
    returnAmount: "مبلغ الإرجاع",
    condition: "حالة المنتج",
    itemReason: "سبب إرجاع المنتج",

    // Product conditions
    conditionExcellent: "ممتاز",
    conditionGood: "جيد",
    conditionDamaged: "متضرر",
    conditionDefective: "معيب",

    // Process return form
    processReturn: "معالجة المرتجع",
    approveReturn: "الموافقة على المرتجع",
    rejectReturn: "رفض المرتجع",
    refundAmount: "مبلغ الاسترداد",
    restockingFee: "رسوم إعادة التخزين",
    finalRefundAmount: "المبلغ النهائي للاسترداد",
    processingNotes: "ملاحظات المعالجة",
    confirmAction: "تأكيد الإجراء",
    cancel: "إلغاء",

    // New return form
    createNewReturn: "إنشاء مرتجع جديد",
    selectOrder: "اختر الطلب",
    selectCustomer: "اختر العميل",
    orderNotFound: "لم يتم العثور على الطلب",
    selectProducts: "اختر المنتجات للإرجاع",
    enterQuantity: "أدخل الكمية",
    selectCondition: "اختر حالة المنتج",
    enterReason: "أدخل سبب الإرجاع",
    selectRefundMethod: "اختر طريقة الاسترداد",
    additionalNotes: "ملاحظات إضافية",
    submitReturn: "تقديم المرتجع",

    // Messages
    noReturnsFound: "لا توجد مرتجعات",
    noReturnsMessage: "لم يتم العثور على أي مرتجعات تطابق المعايير المحددة.",
    returnProcessedSuccess: "تم معالجة المرتجع بنجاح",
    returnApprovedSuccess: "تمت الموافقة على المرتجع بنجاح",
    returnRejectedSuccess: "تم رفض المرتجع بنجاح",
    returnCreatedSuccess: "تم إنشاء المرتجع بنجاح",
    errorProcessingReturn: "خطأ في معالجة المرتجع",
    errorCreatingReturn: "خطأ في إنشاء المرتجع",

    // Validation messages
    quantityRequired: "الكمية مطلوبة",
    quantityInvalid: "الكمية غير صحيحة",
    reasonRequired: "السبب مطلوب",
    conditionRequired: "حالة المنتج مطلوبة",
    refundMethodRequired: "طريقة الاسترداد مطلوبة",

    // Statistics
    totalReturns: "إجمالي المرتجعات",
    pendingCount: "المعلقة",
    processedCount: "المعالجة",
    refundedAmount: "المبلغ المسترد",
    thisMonth: "هذا الشهر",

    // Permissions
    permissionDenied: "تم رفض الإذن",
    permissionDeniedMessage: "ليس لديك إذن لمعالجة المرتجعات.",

    // Exchange items
    exchangeItems: "منتجات الاستبدال",
    selectExchangeProducts: "اختر منتجات الاستبدال",
    exchangeQuantity: "كمية الاستبدال",
    exchangePrice: "سعر الاستبدال",
    addExchangeItem: "إضافة منتج للاستبدال",
    removeExchangeItem: "إزالة منتج الاستبدال",
    totalExchangeValue: "قيمة الاستبدال الإجمالية",

    // Return policy
    returnPolicy: "سياسة الإرجاع",
    maxReturnDays: "أقصى مدة للإرجاع",
    allowedReasons: "الأسباب المسموحة",
    requiresApproval: "يتطلب موافقة",
    days: "يوم",
    yes: "نعم",
    no: "لا"
  },
  employeeCustomerBalances: { 
    title: "أرصدة العملاء",
    description: "عرض وإدارة أرصدة حسابات العملاء ومعلومات الائتمان.",
    underDevelopmentTitle: "أرصدة حسابات العملاء",
    underDevelopmentMessage: "سيسمح هذا القسم للموظفين المصرح لهم بعرض أرصدة ديون/ائتمان العملاء وسجل الدفع وإدارة معلومات الحساب ذات الصلة.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  employeeInventoryReports: { 
    title: "تقارير المخزون",
    description: "الوصول إلى التقارير المتعلقة بمستويات المخزون وحركاته وعرضها.",
    underDevelopmentTitle: "تقارير المخزون",
    underDevelopmentMessage: "يمكن للموظفين ذوي الأذونات المناسبة عرض تقارير المخزون المختلفة، مثل المخزون المتوفر وتنبيهات انخفاض المخزون وسجل حركة المنتج.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  employeePrintReports: {
    title: "طباعة التقارير",
    description: "إنشاء وطباعة تقارير تشغيلية متنوعة.",
    underDevelopmentTitle: "تسهيلات طباعة التقارير",
    underDevelopmentMessage: "سيوفر هذا القسم خيارات لإنشاء وطباعة أنواع مختلفة من التقارير المتعلقة بمهام الموظفين، مثل ملخصات المبيعات اليومية وقوائم الطلبات وما إلى ذلك.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  employeeManageBalances: {
    title: "إدارة الأرصدة",
    description: "إدارة أرصدة العملاء والديون والائتمان.",
    balanceOverviewTitle: "نظرة عامة على الأرصدة",
    balanceOverviewDesc: "ملخص شامل لجميع أرصدة العملاء",
    totalCustomers: "إجمالي العملاء",
    totalDebt: "إجمالي الديون",
    totalCredit: "إجمالي الائتمان",
    netBalance: "الرصيد الصافي",
    customerBalancesTitle: "أرصدة العملاء",
    customerBalancesDesc: "عرض وإدارة أرصدة العملاء الفردية",
    customerName: "اسم العميل",
    currentBalance: "الرصيد الحالي",
    creditLimit: "حد الائتمان",
    lastPayment: "آخر دفعة",
    balanceStatus: "حالة الرصيد",
    statusGood: "جيد",
    statusWarning: "تحذير",
    statusOverdue: "متأخر",
    statusBlocked: "محظور",
    viewDetailsButton: "عرض التفاصيل",
    adjustBalanceButton: "تعديل الرصيد",
    recordPaymentButton: "تسجيل دفعة",
    searchCustomerPlaceholder: "البحث عن عميل...",
    filterByStatus: "تصفية حسب الحالة",
    allStatuses: "جميع الحالات",
    adjustBalanceTitle: "تعديل رصيد العميل",
    adjustBalanceDesc: "تعديل رصيد العميل مع تسجيل السبب",
    adjustmentAmount: "مبلغ التعديل",
    adjustmentReason: "سبب التعديل",
    adjustmentType: "نوع التعديل",
    typeDebit: "خصم",
    typeCredit: "إضافة",
    reasonSale: "بيع",
    reasonReturn: "إرجاع",
    reasonAdjustment: "تعديل",
    reasonPayment: "دفعة",
    reasonOther: "أخرى",
    confirmAdjustment: "تأكيد التعديل",
    balanceAdjustedSuccess: "تم تعديل الرصيد بنجاح",
    paymentRecordedSuccess: "تم تسجيل الدفعة بنجاح",
    errorAdjustingBalance: "خطأ في تعديل الرصيد",
    errorRecordingPayment: "خطأ في تسجيل الدفعة",
    noCustomersFound: "لم يتم العثور على عملاء",
    loadingBalances: "جاري تحميل الأرصدة...",
    balanceHistory: "تاريخ الرصيد",
    recentTransactions: "المعاملات الأخيرة",
    transactionDate: "تاريخ المعاملة",
    transactionType: "نوع المعاملة",
    transactionAmount: "المبلغ",
    transactionBalance: "الرصيد بعد المعاملة",
    exportBalances: "تصدير الأرصدة",
    printReport: "طباعة التقرير"
  },
  ownerCustomerManagement: {
    title: "إدارة العملاء",
    description: "إدارة قاعدة بيانات عملائك، وعرض سجل الشراء، والتفاعل مع عملائك.",
    underDevelopmentTitle: "إدارة علاقات العملاء (CRM)",
    underDevelopmentMessage: "سيوفر هذا القسم أدوات لإدارة معلومات العملاء، وتتبع سجل شرائهم، وتقسيم العملاء للتسويق، وإدارة برامج الولاء.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  ownerAdvancedAnalytics: {
    title: "التحليلات المتقدمة",
    description: "تعمق في بيانات مبيعاتك وأداء المنتجات وأنماط سلوك العملاء.",
    underDevelopmentTitle: "لوحة تحكم تحليلات البيانات",
    underDevelopmentMessage: "استكشف التحليلات المتقدمة باستخدام مخططات قابلة للتخصيص، وتحليل الاتجاهات، ورؤى أعمق حول أداء السوبر ماركت الخاص بك.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  ownerMarketingTools: {
    title: "أدوات التسويق",
    description: "إنشاء وإدارة الحملات التسويقية والعروض الترويجية والتواصل مع العملاء.",
    underDevelopmentTitle: "إدارة الحملات",
    underDevelopmentMessage: "سيسمح لك هذا القسم بإنشاء عروض ترويجية وإرسال رسائل إخبارية وإدارة حملات تسويقية لجذب العملاء والاحتفاظ بهم.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  ownerSupplierManagement: {
    title: "إدارة الموردين",
    description: "إدارة مورديك وتتبع الطلبات ومراقبة الأسعار والأداء.",

    // Main sections
    suppliersDatabase: "قاعدة بيانات الموردين",
    purchaseOrders: "أوامر الشراء",
    deliveryTracking: "تتبع التوريدات",
    supplierEvaluations: "تقييم الموردين",
    payments: "المدفوعات",
    reports: "التقارير",

    // Suppliers Database
    allSuppliers: "جميع الموردين",
    activeSuppliers: "الموردين النشطين",
    inactiveSuppliers: "الموردين غير النشطين",
    pendingApproval: "في انتظار الموافقة",
    blockedSuppliers: "الموردين المحظورين",

    // Supplier Information
    supplierName: "اسم المورد",
    companyName: "اسم الشركة",
    contactPerson: "الشخص المسؤول",
    email: "البريد الإلكتروني",
    phone: "رقم الهاتف",
    address: "العنوان",
    city: "المدينة",
    country: "البلد",
    taxId: "الرقم الضريبي",
    businessLicense: "رخصة العمل",
    category: "الفئة",
    products: "المنتجات",
    paymentTerms: "شروط الدفع",
    creditLimit: "حد الائتمان",
    currentBalance: "الرصيد الحالي",
    rating: "التقييم",
    status: "الحالة",
    dateAdded: "تاريخ الإضافة",
    lastOrderDate: "تاريخ آخر طلب",
    totalOrders: "إجمالي الطلبات",
    totalPurchaseAmount: "إجمالي المشتريات",
    averageDeliveryTime: "متوسط وقت التسليم",
    qualityRating: "تقييم الجودة",
    reliabilityRating: "تقييم الموثوقية",
    priceCompetitiveness: "تنافسية الأسعار",
    notes: "ملاحظات",

    // Supplier Status
    statusActive: "نشط",
    statusInactive: "غير نشط",
    statusBlocked: "محظور",
    statusPendingApproval: "في انتظار الموافقة",

    // Actions
    addSupplier: "إضافة مورد جديد",
    editSupplier: "تعديل المورد",
    viewSupplier: "عرض المورد",
    deleteSupplier: "حذف المورد",
    blockSupplier: "حظر المورد",
    unblockSupplier: "إلغاء الحظر",
    approveSupplier: "الموافقة على المورد",
    contactSupplier: "التواصل مع المورد",
    createPurchaseOrder: "إنشاء أمر شراء",
    viewPurchaseHistory: "عرض تاريخ الشراء",
    evaluateSupplier: "تقييم المورد",

    // Purchase Orders
    purchaseOrderNumber: "رقم أمر الشراء",
    orderDate: "تاريخ الطلب",
    expectedDeliveryDate: "تاريخ التسليم المتوقع",
    actualDeliveryDate: "تاريخ التسليم الفعلي",
    orderStatus: "حالة الطلب",
    priority: "الأولوية",
    subtotal: "المجموع الفرعي",
    taxAmount: "مبلغ الضريبة",
    shippingCost: "تكلفة الشحن",
    totalAmount: "المبلغ الإجمالي",
    paymentStatus: "حالة الدفع",
    deliveryAddress: "عنوان التسليم",

    // Purchase Order Status
    statusDraft: "مسودة",
    statusSent: "مرسل",
    statusConfirmed: "مؤكد",
    statusPartiallyReceived: "مستلم جزئياً",
    statusCompleted: "مكتمل",
    statusCancelled: "ملغى",

    // Priority Levels
    priorityLow: "منخفضة",
    priorityMedium: "متوسطة",
    priorityHigh: "عالية",
    priorityUrgent: "عاجلة",

    // Payment Status
    paymentPending: "في انتظار الدفع",
    paymentPartial: "دفع جزئي",
    paymentPaid: "مدفوع",
    paymentOverdue: "متأخر",

    // Purchase Order Items
    productName: "اسم المنتج",
    description: "الوصف",
    sku: "رمز المنتج",
    quantity: "الكمية",
    unitPrice: "سعر الوحدة",
    totalPrice: "السعر الإجمالي",
    receivedQuantity: "الكمية المستلمة",
    pendingQuantity: "الكمية المعلقة",
    unit: "الوحدة",
    specifications: "المواصفات",

    // Delivery Tracking
    deliveryDate: "تاريخ التسليم",
    trackingNumber: "رقم التتبع",
    carrier: "شركة الشحن",
    deliveryStatus: "حالة التسليم",
    statusScheduled: "مجدول",
    statusInTransit: "في الطريق",
    statusDelivered: "تم التسليم",
    statusDelayed: "متأخر",
    totalItems: "إجمالي العناصر",
    receivedItems: "العناصر المستلمة",
    damagedItems: "العناصر التالفة",
    missingItems: "العناصر المفقودة",
    deliveryNotes: "ملاحظات التسليم",
    receivedBy: "استلمه",
    inspectedBy: "فحصه",
    inspectionNotes: "ملاحظات الفحص",

    // Supplier Evaluation
    evaluationDate: "تاريخ التقييم",
    evaluationPeriod: "فترة التقييم",
    evaluatedBy: "قيمه",
    overallRating: "التقييم العام",
    qualityRating: "تقييم الجودة",
    deliveryRating: "تقييم التسليم",
    priceRating: "تقييم السعر",
    serviceRating: "تقييم الخدمة",
    communicationRating: "تقييم التواصل",
    onTimeDeliveryRate: "معدل التسليم في الوقت المحدد",
    qualityDefectRate: "معدل عيوب الجودة",
    orderAccuracyRate: "معدل دقة الطلبات",
    responseTime: "وقت الاستجابة",
    strengths: "نقاط القوة",
    weaknesses: "نقاط الضعف",
    improvementSuggestions: "اقتراحات التحسين",
    actionItems: "عناصر العمل",
    nextEvaluationDate: "تاريخ التقييم التالي",

    // Forms
    supplierForm: "نموذج المورد",
    purchaseOrderForm: "نموذج أمر الشراء",
    evaluationForm: "نموذج التقييم",
    requiredField: "حقل مطلوب",
    invalidEmail: "البريد الإلكتروني غير صحيح",
    invalidPhone: "رقم الهاتف غير صحيح",
    save: "حفظ",
    cancel: "إلغاء",
    submit: "إرسال",
    approve: "موافقة",
    reject: "رفض",

    // Search and Filters
    searchSuppliers: "البحث في الموردين...",
    filterByCategory: "تصفية حسب الفئة",
    filterByStatus: "تصفية حسب الحالة",
    filterByRating: "تصفية حسب التقييم",
    sortBy: "ترتيب حسب",
    sortByName: "الاسم",
    sortByRating: "التقييم",
    sortByLastOrder: "آخر طلب",
    sortByTotalAmount: "إجمالي المبلغ",

    // Statistics
    totalSuppliersCount: "إجمالي الموردين",
    activeSuppliersCount: "الموردين النشطين",
    totalPurchaseOrders: "إجمالي أوامر الشراء",
    pendingOrders: "الطلبات المعلقة",
    averageRating: "متوسط التقييم",
    totalSpent: "إجمالي المنفق",

    // Messages
    supplierAdded: "تم إضافة المورد بنجاح",
    supplierUpdated: "تم تحديث المورد بنجاح",
    supplierDeleted: "تم حذف المورد بنجاح",
    supplierBlocked: "تم حظر المورد بنجاح",
    supplierUnblocked: "تم إلغاء حظر المورد بنجاح",
    supplierApproved: "تم الموافقة على المورد بنجاح",
    purchaseOrderCreated: "تم إنشاء أمر الشراء بنجاح",
    purchaseOrderUpdated: "تم تحديث أمر الشراء بنجاح",
    evaluationSaved: "تم حفظ التقييم بنجاح",
    noSuppliersFound: "لم يتم العثور على موردين",
    noPurchaseOrdersFound: "لم يتم العثور على أوامر شراء",
    loadingSuppliers: "جارٍ تحميل الموردين...",
    loadingPurchaseOrders: "جارٍ تحميل أوامر الشراء...",

    // Export
    exportSuppliers: "تصدير الموردين",
    exportPurchaseOrders: "تصدير أوامر الشراء",
    exportEvaluations: "تصدير التقييمات",
    exportToCSV: "تصدير إلى CSV",
    exportToPDF: "تصدير إلى PDF",

    // Bank Details
    bankDetails: "تفاصيل البنك",
    bankName: "اسم البنك",
    accountNumber: "رقم الحساب",
    routingNumber: "رقم التوجيه",
    swiftCode: "رمز SWIFT",

    // Additional Fields
    certifications: "الشهادات",
    minimumOrderAmount: "الحد الأدنى لمبلغ الطلب",
    leadTime: "وقت التنفيذ",
    returnPolicy: "سياسة الإرجاع",
    warrantyTerms: "شروط الضمان",
    currency: "العملة",
    exchangeRate: "سعر الصرف",
    discountAmount: "مبلغ الخصم",
    discountPercentage: "نسبة الخصم",

    // Days
    days: "أيام",
    hours: "ساعات",

    // Condition
    conditionGood: "جيد",
    conditionDamaged: "تالف",
    conditionExpired: "منتهي الصلاحية",
    conditionDefective: "معيب"
  },

  // Biometric Authentication
  biometricAuth: {
    title: "المصادقة البيومترية",
    description: "استخدم البصمة أو التعرف على الوجه لتسجيل دخول آمن وسريع",

    // Setup
    setupTitle: "إعداد المصادقة البيومترية",
    setupDescription: "قم بتفعيل البصمة لتسجيل دخول أسرع وأكثر أماناً",
    registerBiometric: "تسجيل بصمة جديدة",
    biometricSetup: "إعداد البصمة",

    // Login
    loginWithBiometric: "تسجيل الدخول بالبصمة",
    biometricLoginButton: "تسجيل الدخول بالبصمة",
    verifyingBiometric: "جارٍ التحقق من البصمة...",
    registering: "جارٍ التسجيل...",

    // Status
    supported: "مدعوم",
    notSupported: "غير مدعوم",
    available: "متوفر",
    notAvailable: "غير متوفر",
    active: "نشط",
    inactive: "غير نشط",

    // Device Types
    touchId: "Touch ID",
    faceId: "Face ID",
    windowsHello: "Windows Hello",
    fingerprint: "البصمة",
    biometric: "البصمة البيومترية",

    // Messages
    registrationSuccess: "تم تفعيل البصمة بنجاح",
    registrationFailed: "فشل في تفعيل البصمة",
    loginSuccess: "تم تسجيل الدخول بالبصمة",
    loginFailed: "فشل تسجيل الدخول بالبصمة",
    verificationFailed: "فشل في التحقق من البصمة",
    noCredentials: "لا توجد بصمات مسجلة",
    credentialDeleted: "تم حذف البصمة",
    deleteFailed: "فشل في حذف البصمة",

    // Errors
    notSupportedError: "متصفحك أو جهازك لا يدعم المصادقة البيومترية",
    platformNotAvailable: "مصادق الجهاز غير متوفر",
    registrationError: "حدث خطأ أثناء تسجيل البصمة",
    authenticationError: "حدث خطأ أثناء المصادقة",
    unexpectedError: "حدث خطأ غير متوقع",

    // Credentials Management
    credentialsTitle: "البصمات المسجلة",
    credentialsDescription: "إدارة البصمات المسجلة على أجهزتك المختلفة",
    deviceName: "اسم الجهاز",
    registrationDate: "تاريخ التسجيل",
    lastUsed: "آخر استخدام",
    neverUsed: "لم يستخدم بعد",
    status: "الحالة",
    actions: "الإجراءات",
    delete: "حذف",
    unknownDevice: "جهاز غير معروف",

    // Security
    securityNotice: "ملاحظة أمنية",
    securityDescription: "البصمات البيومترية محفوظة بشكل آمن على جهازك ولا يتم إرسالها إلى خوادمنا. يتم استخدام مفاتيح التشفير فقط للتحقق من هويتك.",

    // Browser Support
    browserSupport: "دعم المتصفح",
    platformAuthenticator: "مصادق الجهاز",
    registeredFingerprints: "البصمات المسجلة",

    // Instructions
    instructionsTitle: "كيفية الاستخدام",
    instructionsSteps: [
      "انقر على 'تسجيل بصمة جديدة'",
      "اتبع التعليمات التي تظهر على الشاشة",
      "ضع إصبعك على مستشعر البصمة أو استخدم Face ID",
      "بعد التسجيل، يمكنك استخدام البصمة لتسجيل الدخول"
    ],

    // Compatibility
    compatibilityTitle: "التوافق",
    compatibilityDescription: "تعمل المصادقة البيومترية مع:",
    compatibleDevices: [
      "iPhone و iPad مع Touch ID أو Face ID",
      "أجهزة Mac مع Touch ID",
      "أجهزة Windows مع Windows Hello",
      "أجهزة Android مع مستشعر البصمة"
    ],

    // Settings
    biometricStatus: "حالة البصمة",
    enableBiometric: "تفعيل البصمة",
    disableBiometric: "إلغاء تفعيل البصمة",
    manageBiometric: "إدارة البصمة",

    // Buttons
    close: "إغلاق",
    save: "حفظ",
    cancel: "إلغاء",
    enable: "تفعيل",
    disable: "إلغاء التفعيل",
    register: "تسجيل",
    verify: "تحقق",
    retry: "إعادة المحاولة",

    // Prompts
    touchSensor: "المس مستشعر البصمة",
    lookAtCamera: "انظر إلى الكاميرا",
    followInstructions: "اتبع التعليمات التي تظهر على الشاشة",

    // Privacy
    privacyTitle: "الخصوصية والأمان",
    privacyPoints: [
      "لا يتم تخزين بصماتك على خوادمنا",
      "جميع البيانات البيومترية محفوظة محلياً على جهازك",
      "نستخدم فقط مفاتيح التشفير للتحقق من الهوية",
      "يمكنك حذف بياناتك البيومترية في أي وقت"
    ]
  },
  ownerFinancialManagement: {
    title: "الإدارة المالية",
    description: "تتبع النفقات وإدارة الميزانيات وإنشاء التقارير المالية لسوبر ماركت الخاص بك.",
    underDevelopmentTitle: "نظرة عامة مالية",
    underDevelopmentMessage: "سيوفر هذا القسم أدوات للمحاسبة الأساسية وتتبع النفقات وتخطيط الميزانية وإنشاء البيانات المالية.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  ownerLoyaltyProgram: {
    title: "برنامج الولاء",
    description: "إنشاء وإدارة برنامج ولاء العملاء لمكافأة العملاء الدائمين.",
    underDevelopmentTitle: "إعداد برنامج الولاء",
    underDevelopmentMessage: "تصميم وإدارة برنامج ولاء العملاء الخاص بك، بما في ذلك أنظمة النقاط والمكافآت وتتبع الأعضاء.",
    featureComingSoon: "هذه الوظيفة قيد التطوير حاليًا وستتوفر قريبًا."
  },
  adminAlerts: {
    title: "إرسال تنبيهات النظام",
    description: "إنشاء وإرسال إشعارات وتحديثات مهمة للمستخدمين.",
    formTitle: "إنشاء تنبيه جديد",
    formDescription: "اختر الجمهور المستهدف واكتب رسالة التنبيه الخاصة بك.",
    targetRoleLabel: "الجمهور المستهدف",
    selectTargetRolePlaceholder: "اختر الدور المستهدف",
    targetRoleAll: "جميع المستخدمين",
    notificationTitleLabel: "عنوان التنبيه",
    notificationTitlePlaceholder: "مثال: صيانة النظام المجدولة",
    notificationMessageLabel: "رسالة التنبيه",
    notificationMessagePlaceholder: "أدخل تفاصيل التنبيه هنا...",
    sendButton: "إرسال التنبيه",
    sendingButton: "جارٍ الإرسال...",
    notificationSentTitle: "تم إرسال التنبيه",
    notificationSentDesc: "تم إرسال التنبيه إلى {target} بعنوان \"{title}\".",
    validationError: "العنوان والرسالة مطلوبان.",
  },
  adminSupport: {
    title: "إدارة تذاكر الدعم",
    description: "عرض وإدارة والرد على تذاكر دعم المستخدمين.",
    ticketQueueTitle: "قائمة تذاكر الدعم",
    ticketQueueDesc: "مراجعة ومعالجة طلبات الدعم المعلقة من المستخدمين.",
    filterTicketsPlaceholder: "تصفية حسب رقم التذكرة، المستخدم، أو الموضوع...",
    noTicketsFound: "لم يتم العثور على تذاكر دعم أو لا يوجد ما يطابق الفلتر.",
    ticketIdHeader: "رقم التذكرة",
    subjectHeader: "الموضوع",
    userHeader: "المستخدم",
    dateHeader: "تاريخ الإرسال",
    priorityHeader: "الأولوية",
    viewRespondButton: "عرض/رد",
    resolveButton: "حل",
    archivedButton: "مؤرشف",
    ticketResolvedTitle: "تم حل التذكرة",
    ticketResolvedDesc: "تم وضع علامة على التذكرة {ticketId} كـ محلولة.",
    open: "مفتوحة",
    inprogress: "قيد المعالجة",
    closed: "مغلقة",
    high: "عالية",
    medium: "متوسطة",
    low: "منخفضة",
  },
  adminAppSettings: {
    title: "إعدادات التطبيق",
    description: "تكوين الإعدادات والمعلمات العامة لتطبيق ماركت سينك.",
    generalConfigTitle: "التكوين العام",
    generalConfigDesc: "إدارة إعدادات التطبيق الأساسية.",
    appNameLabel: "اسم التطبيق",
    defaultLanguageLabel: "اللغة الافتراضية",
    maintenanceModeLabel: "وضع الصيانة",
    maintenanceModeDesc: "تعطيل الوصول مؤقتًا للمستخدمين أثناء الصيانة.",
    maxUsersLabel: "الحد الأقصى للمستخدمين",
    maxUsersDesc: "تعيين حد عالمي لعدد المستخدمين.",
    securitySettingsTitle: "إعدادات الأمان",
    securitySettingsDesc: "تكوين المعلمات المتعلقة بالأمان.",
    twoFactorAuthLabel: "تمكين المصادقة الثنائية (2FA)",
    twoFactorAuthDesc: "تعزيز أمان الحساب لجميع المستخدمين.",
    sessionTimeoutLabel: "مهلة الجلسة (بالدقائق)",
    sessionTimeoutPlaceholder: "مثال: 30",
    sessionTimeoutDesc: "سيتم تسجيل خروج جلسات المستخدمين غير النشطة تلقائيًا بعد هذه المدة.",
    apiIntegrationTitle: "واجهة برمجة التطبيقات والتكاملات",
    apiIntegrationDesc: "إدارة مفاتيح API وتكاملات خدمات الطرف الثالث.",
    settingsSavedTitle: "تم حفظ الإعدادات",
    settingsSavedDesc: "تم تحديث إعدادات التطبيق بنجاح.",
  },
  subscriptionManagement: {
    title: "إدارة الاشتراك",
    description: "عرض تفاصيل اشتراكك الحالي وإدارة خطتك.",
    currentPlan: "الخطة الحالية",
    status: "الحالة",
    endDate: "يتجدد/ينتهي في",
    active: "نشط",
    expired: "منتهي الصلاحية",
    cancelled: "ملغى",
    premiumYearly: "بريميوم سنوي",
    standardMonthly: "قياسي شهري",
    basicMonthly: "أساسي شهري",
    manageBilling: "إدارة تفاصيل الفوترة",
    upgradePlan: "ترقية الخطة",
    cancelSubscription: "إلغاء الاشتراك",
    renewSubscription: "تجديد الاشتراك",
    featureUnderDevelopment: "هذه الميزة قيد التطوير حاليًا وستتوفر قريبًا.",
    contactSupport: "اتصل بالدعم للمساعدة.",
  },
  placeholderChart: {
    salesOverviewTitle: "نظرة عامة على المبيعات - بيانات نموذجية",
    salesOverviewDescription: "يناير - يونيو ٢٠٢٤ (بيانات عينة)",
    trendingUpMessage: "ارتفاع بنسبة ٥.٢٪ هذا الشهر",
    showingSalesMessage: "عرض إجمالي المبيعات لآخر ٦ أشهر",
    months: {
      january: "يناير",
      february: "فبراير",
      march: "مارس",
      april: "أبريل",
      may: "مايو",
      june: "يونيو",
    }
  },
  storeUnavailable: {
    title: "المتجر غير متوفر مؤقتًا",
    messageOwnerSubscriptionExpired: "نعتذر، ولكن المتجر الذي تحاول الوصول إليه غير متاح حاليًا لأن اشتراك المالك قد انتهى. يرجى المحاولة مرة أخرى لاحقًا أو الاتصال بالمالك مباشرة.",
    messageGeneral: "نعتذر، المتجر الذي تحاول الوصول إليه غير متاح حاليًا.",
    backToDashboard: "العودة إلى لوحة التحكم"
  },
  ownerBrowseWholesale: {
    title: "تصفح منتجات الجملة",
    description: "ابحث عن منتجات من تجار الجملة المعتمدين لنشاطك التجاري واطلبها.",
    filterProductsPlaceholder: "تصفية حسب اسم المنتج، الفئة، أو المورد...",
    noProductsAvailableTitle: "لا توجد منتجات جملة متاحة",
    noProductsAvailableMessage: "لا توجد حاليًا منتجات جملة تطابق نوع نشاطك التجاري أو مرشحاتك. يرجى التحقق مرة أخرى لاحقًا.",
    productCardCategory: "الفئة: {category}",
    productCardPrice: "السعر: {price} ريال يمني",
    productCardMinOrder: "أدنى طلب: {quantity} وحدات",
    productCardWholesaler: "المورد: {name}",
    requestQuoteButton: "طلب عرض سعر",
    contactSupplierButton: "التواصل مع المورد",
    actionFailedError: "فشل الإجراء، يرجى المحاولة مرة أخرى.",
    loadingWholesaleProducts: "جارٍ تحميل منتجات الجملة...",
  },

  // Owner Advanced Analytics
  ownerAdvancedAnalytics: {
    title: "التحليلات المتقدمة",
    description: "تحليلات شاملة للمبيعات والعملاء والمخزون مع رؤى تفصيلية لاتخاذ قرارات مدروسة.",

    // Main sections
    salesAnalytics: "تحليلات المبيعات",
    customerAnalytics: "تحليلات العملاء",
    inventoryAnalytics: "تحليلات المخزون",
    financialOverview: "نظرة عامة مالية",

    // Sales Analytics
    totalSales: "إجمالي المبيعات",
    totalOrders: "إجمالي الطلبات",
    averageOrderValue: "متوسط قيمة الطلب",
    salesGrowth: "نمو المبيعات",
    topProducts: "المنتجات الأكثر مبيعاً",
    salesByCategory: "المبيعات حسب الفئة",
    salesByTime: "المبيعات حسب الوقت",
    salesByDay: "المبيعات حسب اليوم",

    // Customer Analytics
    totalCustomers: "إجمالي العملاء",
    activeCustomers: "العملاء النشطون",
    newCustomers: "عملاء جدد",
    customerLifetimeValue: "القيمة الدائمة للعميل",
    customerRetention: "معدل الاحتفاظ بالعملاء",
    topCustomers: "أفضل العملاء",
    customerSegments: "شرائح العملاء",
    churnRate: "معدل فقدان العملاء",

    // Inventory Analytics
    totalProducts: "إجمالي المنتجات",
    lowStockItems: "منتجات منخفضة المخزون",
    outOfStockItems: "منتجات نفدت من المخزون",
    inventoryValue: "قيمة المخزون",
    fastMovingItems: "منتجات سريعة الحركة",
    slowMovingItems: "منتجات بطيئة الحركة",
    categoryPerformance: "أداء الفئات",
    turnoverRate: "معدل الدوران",

    // Time periods
    thisMonth: "هذا الشهر",
    lastMonth: "الشهر الماضي",
    thisQuarter: "هذا الربع",
    thisYear: "هذا العام",
    last30Days: "آخر 30 يوم",
    last7Days: "آخر 7 أيام",

    // Chart labels
    revenue: "الإيرادات",
    quantity: "الكمية",
    percentage: "النسبة المئوية",
    amount: "المبلغ",
    count: "العدد",

    // Actions
    exportReport: "تصدير التقرير",
    viewDetails: "عرض التفاصيل",
    refreshData: "تحديث البيانات",
    customizeView: "تخصيص العرض",

    // Status indicators
    increasing: "متزايد",
    decreasing: "متناقص",
    stable: "مستقر",
    critical: "حرج",
    warning: "تحذير",
    good: "جيد",

    // Messages
    noDataAvailable: "لا توجد بيانات متاحة",
    loadingAnalytics: "جارٍ تحميل التحليلات...",
    dataUpdated: "تم تحديث البيانات بنجاح",
    exportSuccess: "تم تصدير التقرير بنجاح"
  },

  // Owner Customers Management
  ownerCustomers: {
    title: "إدارة العملاء",
    description: "إدارة شاملة لقاعدة بيانات العملاء مع تتبع المشتريات والولاء والتفاعل.",

    // Main interface
    allCustomers: "جميع العملاء",
    activeCustomers: "العملاء النشطون",
    newCustomers: "عملاء جدد",
    vipCustomers: "عملاء مميزون",
    inactiveCustomers: "عملاء غير نشطين",

    // Customer info
    customerName: "اسم العميل",
    email: "البريد الإلكتروني",
    phone: "رقم الهاتف",
    address: "العنوان",
    dateJoined: "تاريخ الانضمام",
    lastOrder: "آخر طلب",
    totalOrders: "إجمالي الطلبات",
    totalSpent: "إجمالي المبلغ المنفق",
    averageOrderValue: "متوسط قيمة الطلب",
    loyaltyPoints: "نقاط الولاء",
    creditLimit: "حد الائتمان",
    currentBalance: "الرصيد الحالي",
    status: "الحالة",

    // Customer status
    statusActive: "نشط",
    statusInactive: "غير نشط",
    statusBlocked: "محظور",

    // Actions
    viewProfile: "عرض الملف الشخصي",
    editCustomer: "تعديل العميل",
    viewOrders: "عرض الطلبات",
    addNote: "إضافة ملاحظة",
    blockCustomer: "حظر العميل",
    unblockCustomer: "إلغاء الحظر",
    deleteCustomer: "حذف العميل",

    // Customer details
    customerDetails: "تفاصيل العميل",
    contactInfo: "معلومات الاتصال",
    orderHistory: "تاريخ الطلبات",
    paymentHistory: "تاريخ المدفوعات",
    loyaltyProgram: "برنامج الولاء",
    preferences: "التفضيلات",
    notes: "الملاحظات",

    // Customer segments
    segments: "الشرائح",
    createSegment: "إنشاء شريحة",
    segmentName: "اسم الشريحة",
    segmentDescription: "وصف الشريحة",
    segmentCriteria: "معايير الشريحة",
    customerCount: "عدد العملاء",

    // Filters and search
    searchCustomers: "البحث في العملاء...",
    filterByStatus: "تصفية حسب الحالة",
    filterBySegment: "تصفية حسب الشريحة",
    sortBy: "ترتيب حسب",
    sortByName: "الاسم",
    sortBySpent: "المبلغ المنفق",
    sortByOrders: "عدد الطلبات",
    sortByDate: "التاريخ",

    // Statistics
    totalCustomersCount: "إجمالي العملاء",
    activeCustomersCount: "العملاء النشطون",
    newCustomersThisMonth: "عملاء جدد هذا الشهر",
    averageCustomerValue: "متوسط قيمة العميل",
    customerRetentionRate: "معدل الاحتفاظ بالعملاء",

    // Messages
    noCustomersFound: "لم يتم العثور على عملاء",
    customerUpdated: "تم تحديث بيانات العميل",
    customerDeleted: "تم حذف العميل",
    customerBlocked: "تم حظر العميل",
    customerUnblocked: "تم إلغاء حظر العميل",

    // Forms
    addCustomer: "إضافة عميل جديد",
    editCustomerInfo: "تعديل معلومات العميل",
    customerNameRequired: "اسم العميل مطلوب",
    phoneRequired: "رقم الهاتف مطلوب",
    emailInvalid: "البريد الإلكتروني غير صحيح",
    save: "حفظ",
    cancel: "إلغاء",

    // Export
    exportCustomers: "تصدير العملاء",
    exportToCSV: "تصدير إلى CSV",
    exportToPDF: "تصدير إلى PDF"
  },

  // Owner Financial Management
  ownerFinancials: {
    title: "الإدارة المالية",
    description: "إدارة شاملة للأمور المالية مع تتبع الإيرادات والمصروفات والأرباح والتقارير المالية.",

    // Main sections
    overview: "نظرة عامة",
    income: "الإيرادات",
    expenses: "المصروفات",
    profit: "الأرباح",
    cashFlow: "التدفق النقدي",
    reports: "التقارير",
    budgets: "الميزانيات",

    // Financial overview
    totalRevenue: "إجمالي الإيرادات",
    totalExpenses: "إجمالي المصروفات",
    netProfit: "صافي الربح",
    grossMargin: "الهامش الإجمالي",
    operatingExpenses: "المصروفات التشغيلية",
    taxesPaid: "الضرائب المدفوعة",

    // Transaction types
    transactionType: "نوع المعاملة",
    typeIncome: "إيراد",
    typeExpense: "مصروف",
    typeRefund: "استرداد",
    typeTax: "ضريبة",
    typeFee: "رسوم",

    // Categories
    category: "الفئة",
    salesCategory: "مبيعات",
    purchasesCategory: "مشتريات",
    salariesCategory: "رواتب",
    rentCategory: "إيجار",
    utilitiesCategory: "مرافق",
    marketingCategory: "تسويق",
    maintenanceCategory: "صيانة",
    otherCategory: "أخرى",

    // Transaction details
    amount: "المبلغ",
    description: "الوصف",
    date: "التاريخ",
    paymentMethod: "طريقة الدفع",
    reference: "المرجع",
    status: "الحالة",
    attachments: "المرفقات",

    // Payment methods
    cash: "نقدي",
    card: "بطاقة",
    bankTransfer: "تحويل بنكي",
    digitalWallet: "محفظة رقمية",

    // Status
    statusPending: "معلق",
    statusCompleted: "مكتمل",
    statusCancelled: "ملغي",

    // Actions
    addTransaction: "إضافة معاملة",
    editTransaction: "تعديل المعاملة",
    deleteTransaction: "حذف المعاملة",
    viewDetails: "عرض التفاصيل",
    generateReport: "إنشاء تقرير",
    exportData: "تصدير البيانات",

    // Expense management
    expenseCategories: "فئات المصروفات",
    budgetLimit: "حد الميزانية",
    currentSpent: "المنفق حالياً",
    remainingBudget: "الميزانية المتبقية",
    budgetUtilization: "استخدام الميزانية",

    // Reports
    monthlyReport: "تقرير شهري",
    quarterlyReport: "تقرير ربعي",
    yearlyReport: "تقرير سنوي",
    profitLossReport: "تقرير الأرباح والخسائر",
    cashFlowReport: "تقرير التدفق النقدي",
    expenseReport: "تقرير المصروفات",

    // Time periods
    today: "اليوم",
    thisWeek: "هذا الأسبوع",
    thisMonth: "هذا الشهر",
    thisQuarter: "هذا الربع",
    thisYear: "هذا العام",
    lastMonth: "الشهر الماضي",
    lastQuarter: "الربع الماضي",
    lastYear: "العام الماضي",

    // Charts and analytics
    revenueChart: "مخطط الإيرادات",
    expenseChart: "مخطط المصروفات",
    profitChart: "مخطط الأرباح",
    categoryBreakdown: "تفصيل الفئات",
    monthlyTrend: "الاتجاه الشهري",

    // Messages
    transactionAdded: "تم إضافة المعاملة بنجاح",
    transactionUpdated: "تم تحديث المعاملة بنجاح",
    transactionDeleted: "تم حذف المعاملة بنجاح",
    reportGenerated: "تم إنشاء التقرير بنجاح",
    dataExported: "تم تصدير البيانات بنجاح",

    // Validation
    amountRequired: "المبلغ مطلوب",
    amountInvalid: "المبلغ غير صحيح",
    descriptionRequired: "الوصف مطلوب",
    categoryRequired: "الفئة مطلوبة",
    dateRequired: "التاريخ مطلوب",

    // Forms
    newTransaction: "معاملة جديدة",
    editTransactionForm: "تعديل المعاملة",
    selectCategory: "اختر الفئة",
    selectPaymentMethod: "اختر طريقة الدفع",
    enterAmount: "أدخل المبلغ",
    enterDescription: "أدخل الوصف",
    selectDate: "اختر التاريخ",
    save: "حفظ",
    cancel: "إلغاء",

    // Budget management
    setBudget: "تحديد الميزانية",
    budgetAlert: "تنبيه الميزانية",
    budgetExceeded: "تم تجاوز الميزانية",
    budgetWarning: "تحذير الميزانية",
    budgetOnTrack: "الميزانية على المسار الصحيح"
  }
} as const;
