
"use client";

import { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Undo2, Plus, Settings, BarChart3 } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { ReturnsList } from '@/components/returns/returns-list';
import { ReturnDetails } from '@/components/returns/return-details';
import { ProcessReturnForm } from '@/components/returns/process-return-form';
import { ReturnsStats } from '@/components/returns/returns-stats';
import { NewReturnForm } from '@/components/returns/new-return-form';
import { getMockReturns, getMockReturnPolicies, updateMockReturn } from '@/lib/mock-returns-data';
import type { Return, ReturnPolicy } from '@/types';

type ViewMode = 'list' | 'details' | 'process' | 'new' | 'policy' | 'analytics';

export default function OwnerProcessReturnsPage() {
  const t = useScopedI18n('common'); // For nav title
  const tSpecific = useScopedI18n('ownerProcessReturns'); // Specific scope for page content
  const tReturns = useScopedI18n('employeeReturnsManagement');
  const { user } = useAuth();
  const { toast } = useToast();

  const [returns, setReturns] = useState<Return[]>([]);
  const [returnPolicies, setReturnPolicies] = useState<ReturnPolicy[]>([]);
  const [selectedReturnId, setSelectedReturnId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [activeTab, setActiveTab] = useState('pending');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      const mockReturns = getMockReturns();
      const mockPolicies = getMockReturnPolicies();
      setReturns(mockReturns);
      setReturnPolicies(mockPolicies);
    } catch (error) {
      toast({
        title: 'خطأ في تحميل البيانات',
        description: 'فشل في تحميل بيانات المرتجعات',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewReturn = (returnId: string) => {
    setSelectedReturnId(returnId);
    setViewMode('details');
  };

  const handleProcessReturn = (returnId: string) => {
    setSelectedReturnId(returnId);
    setViewMode('process');
  };

  const handleBackToList = () => {
    setSelectedReturnId(null);
    setViewMode('list');
  };

  const handleProcessSuccess = (action: 'approve' | 'reject' | 'process', updatedReturn: Return) => {
    // Update the return in mock data
    updateMockReturn(updatedReturn.id, updatedReturn);

    // Update local state
    setReturns(prev => prev.map(r => r.id === updatedReturn.id ? updatedReturn : r));

    // Go back to list
    handleBackToList();
  };

  const handleNewReturnSuccess = (newReturn: Return) => {
    // Add to local state
    setReturns(prev => [newReturn, ...prev]);

    // Go back to list
    handleBackToList();
  };

  const selectedReturn = selectedReturnId ? returns.find(r => r.id === selectedReturnId) : null;

  const filteredReturns = returns.filter(returnItem => {
    if (activeTab === 'pending') return returnItem.status === 'pending';
    if (activeTab === 'processed') return ['processed', 'refunded', 'approved'].includes(returnItem.status);
    if (activeTab === 'rejected') return returnItem.status === 'rejected';
    return true; // 'all' tab
  });

  if (isLoading) {
    return (
      <AuthenticatedLayout expectedRole="owner">
        <div className="space-y-6 p-1">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">جارٍ تحميل المرتجعات...</p>
            </div>
          </div>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        {viewMode === 'list' && (
          <>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Undo2 className="h-8 w-8 text-primary" />
                <div>
                  <h1 className="text-3xl font-bold text-foreground">إدارة المرتجعات</h1>
                  <p className="text-muted-foreground">
                    {tSpecific('description')}
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setViewMode('analytics')}
                  className="flex items-center gap-2"
                >
                  <BarChart3 className="h-4 w-4" />
                  التحليلات
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setViewMode('policy')}
                  className="flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  سياسة الإرجاع
                </Button>
                <Button onClick={() => setViewMode('new')} className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  {tReturns('newReturn')}
                </Button>
              </div>
            </div>

            {/* Statistics */}
            <ReturnsStats returns={returns} />

            {/* Returns Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="pending" className="flex items-center gap-2">
                  {tReturns('pendingReturns')}
                  <span className="bg-destructive text-destructive-foreground rounded-full px-2 py-0.5 text-xs">
                    {returns.filter(r => r.status === 'pending').length}
                  </span>
                </TabsTrigger>
                <TabsTrigger value="processed">{tReturns('processedCount')}</TabsTrigger>
                <TabsTrigger value="rejected">المرفوضة</TabsTrigger>
                <TabsTrigger value="all">{tReturns('allReturns')}</TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-6">
                <ReturnsList
                  returns={filteredReturns}
                  onViewReturn={handleViewReturn}
                  onProcessReturn={handleProcessReturn}
                  showActions={true}
                />
              </TabsContent>
            </Tabs>
          </>
        )}

        {viewMode === 'details' && selectedReturn && (
          <ReturnDetails
            returnData={selectedReturn}
            onBack={handleBackToList}
            onProcess={() => setViewMode('process')}
            showProcessButton={selectedReturn.status === 'pending'}
          />
        )}

        {viewMode === 'process' && selectedReturn && (
          <ProcessReturnForm
            returnData={selectedReturn}
            onCancel={handleBackToList}
            onSuccess={handleProcessSuccess}
          />
        )}

        {viewMode === 'new' && (
          <NewReturnForm
            onCancel={handleBackToList}
            onSuccess={handleNewReturnSuccess}
          />
        )}

        {viewMode === 'policy' && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                إدارة سياسة الإرجاع
              </CardTitle>
              <CardDescription>
                تحديد قواعد وشروط إرجاع المنتجات في متجرك
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  إعدادات سياسة الإرجاع ستتوفر قريباً
                </p>
                <Button variant="outline" onClick={handleBackToList} className="mt-4">
                  العودة إلى القائمة
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {viewMode === 'analytics' && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                تحليلات المرتجعات
              </CardTitle>
              <CardDescription>
                تقارير مفصلة حول أنماط المرتجعات والأسباب الشائعة
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  تحليلات المرتجعات المتقدمة ستتوفر قريباً
                </p>
                <Button variant="outline" onClick={handleBackToList} className="mt-4">
                  العودة إلى القائمة
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AuthenticatedLayout>
  );
}
