"use client";

import React from 'react';
import { cn } from '@/lib/utils';

interface NotificationBadgeProps {
  count: number;
  className?: string;
  maxCount?: number;
  showZero?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'destructive' | 'warning' | 'success';
}

export function NotificationBadge({ 
  count, 
  className, 
  maxCount = 99, 
  showZero = false,
  size = 'md',
  variant = 'destructive'
}: NotificationBadgeProps) {
  if (count === 0 && !showZero) {
    return null;
  }

  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

  const sizeClasses = {
    sm: 'h-4 w-4 text-xs min-w-[16px]',
    md: 'h-5 w-5 text-xs min-w-[20px]',
    lg: 'h-6 w-6 text-sm min-w-[24px]'
  };

  const variantClasses = {
    default: 'bg-primary text-primary-foreground',
    destructive: 'bg-destructive text-destructive-foreground',
    warning: 'bg-yellow-500 text-white',
    success: 'bg-green-500 text-white'
  };

  return (
    <span
      className={cn(
        'inline-flex items-center justify-center rounded-full font-medium',
        'absolute -top-1 -right-1 z-10',
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
    >
      {displayCount}
    </span>
  );
}

interface NotificationIconWithBadgeProps {
  icon: React.ReactNode;
  count: number;
  className?: string;
  badgeProps?: Partial<NotificationBadgeProps>;
}

export function NotificationIconWithBadge({ 
  icon, 
  count, 
  className,
  badgeProps 
}: NotificationIconWithBadgeProps) {
  return (
    <div className={cn('relative inline-block', className)}>
      {icon}
      <NotificationBadge count={count} {...badgeProps} />
    </div>
  );
}
