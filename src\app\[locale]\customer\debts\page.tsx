
"use client";

import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from '@/components/ui/badge';
import { BookText, Filter, Printer } from 'lucide-react';
import { Input } from '@/components/ui/input';
import type { Debt } from '@/types';
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

const DEBTS_STORAGE_KEY = 'marketSyncOwnerDebts'; // Same key as owner's page

const getStoredDebts = (): Debt[] => {
  if (typeof window === 'undefined') return [];
  const stored = localStorage.getItem(DEBTS_STORAGE_KEY);
  return stored ? JSON.parse(stored) : [];
};

export default function CustomerDebtsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [customerDebts, setCustomerDebts] = useState<Debt[]>([]);
  const [filterText, setFilterText] = useState('');
  const t = useScopedI18n('customerDebts');
  const tCommon = useScopedI18n('common');
  const tDebtsOwner = useScopedI18n('debtsManagement'); // Reuse some keys

  useEffect(() => {
    if (user && user.createdById) { // Customer must be linked to an owner
      const allDebts = getStoredDebts();
      const debtsForThisCustomer = allDebts.filter(
        debt => debt.ownerId === user.createdById && 
                debt.partyName === (user.name || user.username) && // Match by customer name/username
                debt.type === 'debt' // Only show debts owed by customer
      );
      setCustomerDebts(debtsForThisCustomer);
    }
  }, [user]);

  const filteredDebts = customerDebts.filter(debt =>
    (debt.notes && debt.notes.toLowerCase().includes(filterText.toLowerCase())) ||
    format(new Date(debt.date), "PP").toLowerCase().includes(filterText.toLowerCase()) ||
    debt.items.some(item => item.productName.toLowerCase().includes(filterText.toLowerCase()))
  );

  const getStatusBadgeVariant = (status: Debt['status']) => {
    if (status === 'paid') return 'default';
    if (status === 'partially_paid') return 'secondary';
    return 'destructive'; // unpaid
  };
  
  const handlePrintStatement = () => {
    if (!user) return;
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const direction = tCommon('language') === 'Arabic' ? 'rtl' : 'ltr';
      let statementHTML = `<html><head><title>${t('statementOfAccount')}</title><style>`;
      statementHTML += `
        body { font-family: Arial, sans-serif; margin: 20px; color: #333; direction: ${direction}; }
        .statement-box { max-width: 800px; margin: auto; padding: 30px; border: 1px solid #eee; box-shadow: 0 0 10px rgba(0, 0, 0, 0.15); font-size: 16px; line-height: 24px; }
        .statement-header { text-align: center; margin-bottom: 20px; }
        .statement-header h1 { margin: 0; font-size: 2em; color: hsl(var(--primary)); }
        .details-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }
        .party-details strong, .statement-details strong { display: block; margin-bottom: 5px; color: hsl(var(--foreground)); }
        .item-table { width: 100%; line-height: inherit; text-align: ${direction === 'rtl' ? 'right' : 'left'}; border-collapse: collapse; }
        .item-table td, .item-table th { padding: 8px; border-bottom: 1px solid #eee; }
        .item-table tr:last-child td { border-bottom: none; }
        .item-table th { background: hsl(var(--muted)); font-weight: bold; color: hsl(var(--foreground)); }
        .total-section { text-align: ${direction === 'rtl' ? 'left' : 'right'}; margin-top: 20px; }
        .footer { text-align: center; margin-top: 40px; font-size: 0.8em; color: #777; }
      `;
      statementHTML += `</style></head><body><div class="statement-box">`;
      statementHTML += `<div class="statement-header"><h1>${t('statementOfAccount')}</h1><p>${tDebtsOwner('storeNamePlaceholder')}</p></div>`;
      statementHTML += `<div class="details-grid">`;
      statementHTML += `<div><strong>${t('customerNameLabel')}:</strong> ${user.name || user.username}</div>`;
      statementHTML += `<div style="text-align: ${direction === 'rtl' ? 'left' : 'right'};"><strong>${t('dateIssuedLabel')}:</strong> ${format(new Date(), "PP")}</div>`;
      statementHTML += `</div>`;
      
      statementHTML += `<table class="item-table"><thead><tr>`;
      statementHTML += `<th>${tDebtsOwner('date')}</th><th>${t('descriptionHeader')}</th><th>${t('itemsHeader')}</th><th style="text-align: ${direction === 'rtl' ? 'left' : 'right'};">${tDebtsOwner('amount')}</th><th>${tDebtsOwner('status')}</th>`;
      statementHTML += `</tr></thead><tbody>`;

      let totalOutstanding = 0;
      filteredDebts.forEach(debt => {
        const itemsSummary = debt.items.map(i => `${i.productName} (x${i.quantity})`).join(', ');
        statementHTML += `<tr>`;
        statementHTML += `<td>${format(new Date(debt.date), "PP")}</td>`;
        statementHTML += `<td>${debt.notes || tCommon('N_A')}</td>`;
        statementHTML += `<td>${itemsSummary || tCommon('N_A')}</td>`;
        statementHTML += `<td style="text-align: ${direction === 'rtl' ? 'left' : 'right'};">YER ${debt.amount.toFixed(2)}</td>`;
        statementHTML += `<td><span style="padding: 3px 6px; border-radius: 4px; background-color: ${debt.status === 'paid' ? '#d1fae5' : debt.status === 'partially_paid' ? '#fef3c7' : '#fee2e2'}; color: ${debt.status === 'paid' ? '#065f46' : debt.status === 'partially_paid' ? '#92400e' : '#991b1b'};">${tDebtsOwner(`status${debt.status.charAt(0).toUpperCase() + debt.status.slice(1).replace('_', '')}` as any, undefined, debt.status.replace('_', ' '))}</span></td>`;
        statementHTML += `</tr>`;
        if (debt.status !== 'paid') {
          totalOutstanding += debt.amount;
        }
      });
      statementHTML += `</tbody></table>`;

      statementHTML += `<div class="total-section" style="margin-top: 30px;"><strong>${t('totalOutstandingDebt')}: YER ${totalOutstanding.toFixed(2)}</strong></div>`;
      statementHTML += `<div class="footer">${tDebtsOwner('thankYouMessage')}</div>`;
      statementHTML += `</div></body></html>`;
      printWindow.document.write(statementHTML);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    } else {
      toast({ title: tCommon('error'), description: tDebtsOwner('printWindowError'), variant: 'destructive' });
    }
  };


  if (!user) {
    return (
      <AuthenticatedLayout expectedRole="customer">
        <div className="p-4 text-center text-muted-foreground">{tCommon('loading')}</div>
      </AuthenticatedLayout>
    );
  }
  
  if (!user.createdById) {
     return (
      <AuthenticatedLayout expectedRole="customer">
        <div className="p-4 text-center text-muted-foreground">{t('noDebtsAccount')}</div>
      </AuthenticatedLayout>
    );
  }


  return (
    <AuthenticatedLayout expectedRole="customer">
      <div className="space-y-6 p-1">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center space-x-3">
            <BookText className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
              <p className="text-muted-foreground">
                {t('description')}
              </p>
            </div>
          </div>
           <Button onClick={handlePrintStatement} variant="outline">
            <Printer className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('printStatement')}
          </Button>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{t('debtRecordsTitle')}</CardTitle>
            <CardDescription>
              {t('debtRecordsDescription')}
            </CardDescription>
            <div className="mt-4">
              <Input
                placeholder={t('filterPlaceholder')}
                value={filterText}
                onChange={(e) => setFilterText(e.target.value)}
                className="max-w-sm"
              />
            </div>
          </CardHeader>
          <CardContent>
            {filteredDebts.length === 0 ? (
              <p className="text-muted-foreground text-center py-6">{t('noDebtsFound')}</p>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{tDebtsOwner('date')}</TableHead>
                      <TableHead>{t('descriptionHeader')}</TableHead>
                      <TableHead>{t('itemsHeader')}</TableHead>
                      <TableHead className="text-right rtl:text-left">{tDebtsOwner('amount')}</TableHead>
                      <TableHead>{tDebtsOwner('status')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDebts.map((debt) => (
                      <TableRow key={debt.id}>
                        <TableCell>{format(new Date(debt.date), "PP")}</TableCell>
                        <TableCell className="max-w-xs truncate" title={debt.notes}>{debt.notes || tCommon('N_A')}</TableCell>
                        <TableCell className="max-w-xs truncate" title={debt.items.map(i => `${i.productName} (x${i.quantity})`).join(', ')}>
                            {debt.items.length > 0 ? debt.items.map(i => i.productName).join(', ') : tCommon('N_A')}
                        </TableCell>
                        <TableCell className="text-right rtl:text-left">YER {debt.amount.toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(debt.status) as any} className="capitalize">
                            {tDebtsOwner(`status${debt.status.charAt(0).toUpperCase() + debt.status.slice(1).replace('_', '')}` as any, undefined, debt.status.replace('_', ' '))}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
          <CardFooter className="text-sm text-muted-foreground">
            {t('showingRecordsCount', { count: filteredDebts.length.toString() })}
          </CardFooter>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}
