"use client";

import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  X, 
  CheckCircle2, 
  Circle,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import type { Notification } from '@/types';

export interface NotificationFilters {
  search: string;
  type: string;
  priority: string;
  isRead: string;
  sortBy: 'date' | 'priority' | 'type';
  sortOrder: 'asc' | 'desc';
}

interface NotificationFiltersProps {
  filters: NotificationFilters;
  onFiltersChange: (filters: NotificationFilters) => void;
  notifications: Notification[];
  onMarkAllAsRead: () => void;
  onClearAll: () => void;
}

const notificationTypes: Notification['type'][] = [
  'info', 'warning', 'error', 'success', 'system', 'order', 'task', 'alert'
];

const priorityLevels: Notification['priority'][] = [
  'low', 'medium', 'high', 'urgent'
];

export function NotificationFilters({
  filters,
  onFiltersChange,
  notifications,
  onMarkAllAsRead,
  onClearAll
}: NotificationFiltersProps) {
  const t = useScopedI18n('employeeNotifications');

  const updateFilter = (key: keyof NotificationFilters, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      search: '',
      type: 'all',
      priority: 'all',
      isRead: 'all',
      sortBy: 'date',
      sortOrder: 'desc'
    });
  };

  const hasActiveFilters = 
    filters.search !== '' || 
    filters.type !== 'all' || 
    filters.priority !== 'all' || 
    filters.isRead !== 'all';

  const unreadCount = notifications.filter(n => !n.isRead).length;
  const totalCount = notifications.length;

  return (
    <div className="space-y-4">
      {/* Stats and Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="text-sm text-muted-foreground">
            {t('notificationStats', { 
              total: totalCount, 
              unread: unreadCount 
            })}
          </div>
          {unreadCount > 0 && (
            <Badge variant="destructive" className="text-xs">
              {unreadCount} {t('unread')}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {unreadCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={onMarkAllAsRead}
              className="text-xs"
            >
              <CheckCircle2 className="h-3 w-3 mr-1" />
              {t('markAllAsRead')}
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={onClearAll}
            className="text-xs text-destructive hover:text-destructive"
          >
            <X className="h-3 w-3 mr-1" />
            {t('clearAll')}
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-3">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('searchPlaceholder')}
            value={filters.search}
            onChange={(e) => updateFilter('search', e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Type Filter */}
        <Select value={filters.type} onValueChange={(value) => updateFilter('type', value)}>
          <SelectTrigger className="w-full sm:w-[140px]">
            <SelectValue placeholder={t('filterByType')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('allTypes')}</SelectItem>
            {notificationTypes.map((type) => (
              <SelectItem key={type} value={type}>
                {t(`type_${type}`)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Priority Filter */}
        <Select value={filters.priority} onValueChange={(value) => updateFilter('priority', value)}>
          <SelectTrigger className="w-full sm:w-[140px]">
            <SelectValue placeholder={t('filterByPriority')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('allPriorities')}</SelectItem>
            {priorityLevels.map((priority) => (
              <SelectItem key={priority} value={priority}>
                {t(`priority_${priority}`)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Read Status Filter */}
        <Select value={filters.isRead} onValueChange={(value) => updateFilter('isRead', value)}>
          <SelectTrigger className="w-full sm:w-[140px]">
            <SelectValue placeholder={t('filterByStatus')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('allStatuses')}</SelectItem>
            <SelectItem value="unread">
              <div className="flex items-center gap-2">
                <Circle className="h-3 w-3" />
                {t('unread')}
              </div>
            </SelectItem>
            <SelectItem value="read">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-3 w-3" />
                {t('read')}
              </div>
            </SelectItem>
          </SelectContent>
        </Select>

        {/* Sort Options */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="w-full sm:w-auto">
              {filters.sortOrder === 'asc' ? (
                <SortAsc className="h-4 w-4 mr-2" />
              ) : (
                <SortDesc className="h-4 w-4 mr-2" />
              )}
              {t('sort')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel>{t('sortBy')}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuCheckboxItem
              checked={filters.sortBy === 'date'}
              onCheckedChange={() => updateFilter('sortBy', 'date')}
            >
              {t('sortByDate')}
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={filters.sortBy === 'priority'}
              onCheckedChange={() => updateFilter('sortBy', 'priority')}
            >
              {t('sortByPriority')}
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={filters.sortBy === 'type'}
              onCheckedChange={() => updateFilter('sortBy', 'type')}
            >
              {t('sortByType')}
            </DropdownMenuCheckboxItem>
            <DropdownMenuSeparator />
            <DropdownMenuLabel>{t('sortOrder')}</DropdownMenuLabel>
            <DropdownMenuCheckboxItem
              checked={filters.sortOrder === 'desc'}
              onCheckedChange={() => updateFilter('sortOrder', 'desc')}
            >
              {t('newestFirst')}
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={filters.sortOrder === 'asc'}
              onCheckedChange={() => updateFilter('sortOrder', 'asc')}
            >
              {t('oldestFirst')}
            </DropdownMenuCheckboxItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Active Filters */}
      {hasActiveFilters && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">{t('activeFilters')}:</span>
          {filters.search && (
            <Badge variant="secondary" className="text-xs">
              {t('search')}: "{filters.search}"
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => updateFilter('search', '')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          {filters.type !== 'all' && (
            <Badge variant="secondary" className="text-xs">
              {t('type')}: {t(`type_${filters.type}`)}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => updateFilter('type', 'all')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          {filters.priority !== 'all' && (
            <Badge variant="secondary" className="text-xs">
              {t('priority')}: {t(`priority_${filters.priority}`)}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => updateFilter('priority', 'all')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          {filters.isRead !== 'all' && (
            <Badge variant="secondary" className="text-xs">
              {t('status')}: {t(filters.isRead)}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => updateFilter('isRead', 'all')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-xs text-muted-foreground hover:text-foreground"
          >
            {t('clearAllFilters')}
          </Button>
        </div>
      )}
    </div>
  );
}
