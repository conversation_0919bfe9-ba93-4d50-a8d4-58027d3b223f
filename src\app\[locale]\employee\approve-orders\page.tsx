"use client";

import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, ListChecks, Filter, Info, Package, CalendarDays } from 'lucide-react';
import type { Order } from '@/types';
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/contexts/auth-context';
import { getMockOrders, updateMockOrder } from '@/lib/mock-order-data';
import { format } from 'date-fns';


export default function ApproveOrdersPage() {
  const t = useScopedI18n('employeeApproveOrders');
  const tCommon = useScopedI18n('common');
  const { toast } = useToast();
  const { user: authUser } = useAuth();
  const [pendingOrders, setPendingOrders] = useState<Order[]>([]);
  const [filterText, setFilterText] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  const canApproveOrders = authUser?.permissions?.canReceiveOrders || false; // Assuming canReceiveOrders implies approval

  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = () => {
    setIsLoading(true);
    const allOrders = getMockOrders();
    // Assuming employees only see orders associated with their owner (if applicable)
    // or all orders if no owner association. For now, let's show all 'pending' orders.
    // Further filtering by ownerId can be added here if needed.
    const ordersForOwner = authUser?.createdById 
      ? allOrders.filter(order => {
          // This logic needs to be more robust. How are orders linked to owners?
          // For now, assuming orders might have an ownerId or products in items have ownerId
          return true; // Placeholder, needs refinement
      }) 
      : allOrders;

    setPendingOrders(ordersForOwner.filter(order => order.status === 'pending'));
    setIsLoading(false);
  };

  const handleApproveOrder = (orderId: string) => {
    if (!authUser) {
      toast({ title: tCommon('error'), description: "Authentication error.", variant: "destructive" });
      return;
    }
    if (!canApproveOrders) {
      toast({ title: tCommon('error'), description: t('permissionDenied'), variant: "destructive" });
      return;
    }
    const updates: Partial<Order> = {
      status: 'processing',
      approvedByEmployeeId: authUser.id,
      approvedByEmployeeName: authUser.name || authUser.username,
      approvedAt: new Date().toISOString(),
    };
    updateMockOrder(orderId, updates);
    loadOrders(); // Refresh the list
    toast({
      title: t('orderApprovedTitle'),
      description: t('orderApprovedDesc', { orderId }),
      action: <CheckCircle className="h-5 w-5 text-green-500" />,
    });
  };

  const handleRejectOrder = (orderId: string) => {
    if (!canApproveOrders) {
      toast({ title: tCommon('error'), description: t('permissionDenied'), variant: "destructive" });
      return;
    }
    updateMockOrder(orderId, { status: 'cancelled' });
    loadOrders(); // Refresh the list
    toast({
      title: t('orderRejectedTitle'),
      description: t('orderRejectedDesc', { orderId }),
      variant: 'destructive',
      action: <XCircle className="h-5 w-5 text-white" />,
    });
  };

  const filteredOrders = pendingOrders.filter(order =>
    (order.id.toLowerCase().includes(filterText.toLowerCase()) ||
     (order.customerName && order.customerName.toLowerCase().includes(filterText.toLowerCase())) ||
     order.customerId.toLowerCase().includes(filterText.toLowerCase()))
  );
  
  if (isLoading) {
    return (
      <AuthenticatedLayout expectedRole="employee">
        <div className="p-4 text-center text-muted-foreground">{tCommon('loading')}</div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-6 p-1">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <ListChecks className="h-8 w-8 text-primary" />
            <div>
                <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
                <p className="text-muted-foreground">
                {t('description')}
                </p>
            </div>
            </div>
        </div>
        
        <Card className="shadow-lg">
            <CardHeader>
                <CardTitle>{t('pendingApprovalTitle')}</CardTitle>
                <CardDescription>{t('pendingApprovalDesc')}</CardDescription>
                <div className="flex items-center space-x-2 pt-2 rtl:space-x-reverse">
                    <Filter className="h-5 w-5 text-muted-foreground" />
                    <Input 
                        placeholder={t('filterPlaceholder')} 
                        className="max-w-sm"
                        value={filterText}
                        onChange={(e) => setFilterText(e.target.value)}
                    />
                </div>
            </CardHeader>
            <CardContent>
                {!canApproveOrders && (
                    <p className="text-destructive text-center py-6">{t('permissionDeniedMessage')}</p>
                )}
                {canApproveOrders && filteredOrders.length === 0 ? (
                    <p className="text-muted-foreground text-center py-6">{t('noPendingOrders')}</p>
                ) : canApproveOrders && (
                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                        <TableRow>
                            <TableHead>{t('orderId')}</TableHead>
                            <TableHead>{t('customerName')}</TableHead>
                            <TableHead className="hidden md:table-cell">{t('orderDate')}</TableHead>
                            <TableHead>{t('items')}</TableHead>
                            <TableHead className="text-right rtl:text-left">{t('totalAmount')}</TableHead>
                            <TableHead className="text-center">{tCommon('actions')}</TableHead>
                        </TableRow>
                        </TableHeader>
                        <TableBody>
                        {filteredOrders.map((order) => (
                            <TableRow key={order.id}>
                            <TableCell className="font-medium">
                                <Button variant="link" asChild className="p-0 h-auto">
                                  {/* Assuming order details page exists at /employee/orders/[orderId] or similar */}
                                  {/* For now, a non-functional link or just text */}
                                  <span className="hover:underline">{order.id}</span>
                                  {/* <Link href={`/employee/orders/${order.id}`}>{order.id}</Link> */}
                                </Button>
                            </TableCell>
                            <TableCell>{order.customerName || order.customerId}</TableCell>
                            <TableCell className="hidden md:table-cell">
                                <div className="flex items-center">
                                    <CalendarDays className="mr-2 h-4 w-4 text-muted-foreground rtl:ml-2 rtl:mr-0" />
                                    {format(new Date(order.createdAt), "PPpp")}
                                </div>
                            </TableCell>
                            <TableCell>
                                <ul className="list-disc list-inside text-xs">
                                    {order.items.map(item => (
                                        <li key={item.productId}>{item.productName} (x{item.quantity})</li>
                                    ))}
                                </ul>
                                ({order.items.reduce((sum, item) => sum + item.quantity, 0)} {tCommon('items', {count: order.items.reduce((sum, item) => sum + item.quantity, 0)})} )
                            </TableCell>
                            <TableCell className="text-right rtl:text-left">YER {order.totalAmount.toFixed(2)}</TableCell>
                            <TableCell className="text-center space-x-1 rtl:space-x-reverse">
                                <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    className="text-green-600 hover:text-green-700"
                                    onClick={() => handleApproveOrder(order.id)}
                                    aria-label={t('approveButton')}
                                    disabled={!canApproveOrders}
                                >
                                    <CheckCircle className="h-5 w-5" />
                                </Button>
                                <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    className="text-red-600 hover:text-red-700"
                                    onClick={() => handleRejectOrder(order.id)}
                                    aria-label={t('rejectButton')}
                                    disabled={!canApproveOrders}
                                >
                                    <XCircle className="h-5 w-5" />
                                </Button>
                                 <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    className="text-muted-foreground hover:text-primary"
                                    asChild
                                    aria-label={t('viewOrderDetails')}
                                    disabled // Link to order details page needs to be implemented
                                >
                                  {/* <Link href={`/employee/orders/${order.id}`}> */}
                                    <Info className="h-5 w-5" />
                                  {/* </Link> */}
                                </Button>
                            </TableCell>
                            </TableRow>
                        ))}
                        </TableBody>
                    </Table>
                </div>
                )}
            </CardContent>
        </Card>

      </div>
    </AuthenticatedLayout>
  );
}

