"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Download, 
  Trash2, 
  CheckCircle, 
  XCircle, 
  Clock, 
  HardDrive,
  FileText,
  Calendar,
  MoreHorizontal,
  RotateCcw
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';

interface BackupRecord {
  id: string;
  timestamp: string;
  type: 'manual' | 'scheduled' | 'automatic';
  status: 'completed' | 'failed' | 'in_progress';
  size: number; // in bytes
  duration: number; // in seconds
  dataTypes: string[];
  description?: string;
  error?: string;
  downloadUrl?: string;
}

const BACKUP_HISTORY_KEY = 'marketSyncBackupHistory';

const mockBackupHistory: BackupRecord[] = [
  {
    id: 'backup-001',
    timestamp: '2024-01-20T02:00:00Z',
    type: 'scheduled',
    status: 'completed',
    size: 15728640, // 15MB
    duration: 45,
    dataTypes: ['products', 'inventory', 'users'],
    description: 'Daily automated backup'
  },
  {
    id: 'backup-002',
    timestamp: '2024-01-19T14:30:00Z',
    type: 'manual',
    status: 'completed',
    size: 8388608, // 8MB
    duration: 23,
    dataTypes: ['products', 'orders'],
    description: 'Manual backup before system update'
  },
  {
    id: 'backup-003',
    timestamp: '2024-01-19T02:00:00Z',
    type: 'scheduled',
    status: 'failed',
    size: 0,
    duration: 12,
    dataTypes: ['products', 'inventory'],
    error: 'Database connection timeout',
    description: 'Daily automated backup'
  },
  {
    id: 'backup-004',
    timestamp: '2024-01-18T02:00:00Z',
    type: 'scheduled',
    status: 'completed',
    size: 12582912, // 12MB
    duration: 38,
    dataTypes: ['products', 'inventory', 'users', 'settings'],
    description: 'Daily automated backup'
  },
  {
    id: 'backup-005',
    timestamp: '2024-01-17T16:45:00Z',
    type: 'manual',
    status: 'completed',
    size: 20971520, // 20MB
    duration: 67,
    dataTypes: ['products', 'inventory', 'users', 'orders', 'debts'],
    description: 'Full system backup'
  }
];

export function BackupHistory() {
  const t = useScopedI18n('backupManagement');
  const tCommon = useScopedI18n('common');
  const { toast } = useToast();

  const [backupHistory, setBackupHistory] = useState<BackupRecord[]>(mockBackupHistory);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(BACKUP_HISTORY_KEY);
      if (stored) {
        try {
          const parsedHistory = JSON.parse(stored);
          setBackupHistory([...parsedHistory, ...mockBackupHistory]);
        } catch (error) {
          console.error('Error loading backup history:', error);
        }
      }
    }
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            {t('completed')}
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            {t('failed')}
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            {t('inProgress')}
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    const variants = {
      manual: 'default',
      scheduled: 'secondary',
      automatic: 'outline'
    } as const;

    return (
      <Badge variant={variants[type as keyof typeof variants]}>
        {t(`backupType_${type}`)}
      </Badge>
    );
  };

  const handleDownloadBackup = (backup: BackupRecord) => {
    if (backup.status !== 'completed') {
      toast({
        title: t('downloadFailed'),
        description: t('cannotDownloadIncompleteBackup'),
        variant: 'destructive'
      });
      return;
    }

    // Simulate download
    toast({
      title: t('downloadStarted'),
      description: t('backupDownloadStarted', { id: backup.id }),
    });
  };

  const handleRestoreBackup = (backup: BackupRecord) => {
    if (backup.status !== 'completed') {
      toast({
        title: t('restoreFailed'),
        description: t('cannotRestoreIncompleteBackup'),
        variant: 'destructive'
      });
      return;
    }

    if (!window.confirm(t('confirmRestoreBackup'))) return;

    setIsLoading(true);
    
    // Simulate restore process
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: t('restoreStarted'),
        description: t('backupRestoreStarted', { id: backup.id }),
      });
    }, 2000);
  };

  const handleDeleteBackup = (backupId: string) => {
    if (!window.confirm(t('confirmDeleteBackup'))) return;

    const newHistory = backupHistory.filter(backup => backup.id !== backupId);
    setBackupHistory(newHistory);

    if (typeof window !== 'undefined') {
      localStorage.setItem(BACKUP_HISTORY_KEY, JSON.stringify(newHistory));
    }

    toast({
      title: t('backupDeleted'),
      description: t('backupDeletedSuccess'),
    });
  };

  // Calculate statistics
  const stats = {
    total: backupHistory.length,
    completed: backupHistory.filter(b => b.status === 'completed').length,
    failed: backupHistory.filter(b => b.status === 'failed').length,
    totalSize: backupHistory
      .filter(b => b.status === 'completed')
      .reduce((acc, b) => acc + b.size, 0),
    averageDuration: backupHistory
      .filter(b => b.status === 'completed')
      .reduce((acc, b, _, arr) => acc + b.duration / arr.length, 0)
  };

  const successRate = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('totalBackups')}
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.completed} {t('successful')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('successRate')}
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{successRate.toFixed(1)}%</div>
            <Progress value={successRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('totalStorage')}
            </CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatFileSize(stats.totalSize)}</div>
            <p className="text-xs text-muted-foreground">
              {t('usedStorage')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('averageDuration')}
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatDuration(Math.round(stats.averageDuration))}</div>
            <p className="text-xs text-muted-foreground">
              {t('perBackup')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Backup History Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            {t('backupHistory')}
          </CardTitle>
          <CardDescription>
            {t('recentBackupOperations')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('timestamp')}</TableHead>
                  <TableHead>{t('type')}</TableHead>
                  <TableHead>{t('status')}</TableHead>
                  <TableHead>{t('size')}</TableHead>
                  <TableHead>{t('duration')}</TableHead>
                  <TableHead>{t('dataTypes')}</TableHead>
                  <TableHead className="text-right">{tCommon('actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {backupHistory.map((backup) => (
                  <TableRow key={backup.id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {new Date(backup.timestamp).toLocaleDateString()}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(backup.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{getTypeBadge(backup.type)}</TableCell>
                    <TableCell>{getStatusBadge(backup.status)}</TableCell>
                    <TableCell>{formatFileSize(backup.size)}</TableCell>
                    <TableCell>{formatDuration(backup.duration)}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {backup.dataTypes.slice(0, 2).map(type => (
                          <Badge key={type} variant="outline" className="text-xs">
                            {type}
                          </Badge>
                        ))}
                        {backup.dataTypes.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{backup.dataTypes.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>{tCommon('actions')}</DropdownMenuLabel>
                          <DropdownMenuItem 
                            onClick={() => handleDownloadBackup(backup)}
                            disabled={backup.status !== 'completed'}
                          >
                            <Download className="mr-2 h-4 w-4" />
                            {t('download')}
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleRestoreBackup(backup)}
                            disabled={backup.status !== 'completed' || isLoading}
                          >
                            <RotateCcw className="mr-2 h-4 w-4" />
                            {t('restore')}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleDeleteBackup(backup.id)}
                            className="text-destructive focus:text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            {tCommon('delete')}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {backupHistory.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>{t('noBackupHistory')}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
