
"use client"; // Make this a client component

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { InventoryPredictionForm } from '@/components/owner/inventory-prediction-form';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { BarChart3 } from 'lucide-react';
import { useScopedI18n, useI18n } from '@/lib/i18n/client';


export default function InventoryPredictionsPage() {
  const t = useI18n();
  const tPredictions = useScopedI18n('ownerPredictions');

  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <BarChart3 className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{tPredictions('title')}</h1>
            <p className="text-muted-foreground">
              {tPredictions('description')}
            </p>
          </div>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{tPredictions('formTitle')}</CardTitle>
            <CardDescription>
              {tPredictions('formDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <InventoryPredictionForm />
          </CardContent>
        </Card>
        
        <Card className="shadow-md">
            <CardHeader>
                <CardTitle>{tPredictions('howItWorksTitle')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm text-muted-foreground">
                <p>{tPredictions('howItWorksP1')}</p>
                <p>{tPredictions('howItWorksP2')}</p>
                <p><strong>{tPredictions('howItWorksNoteTitle')}:</strong> {tPredictions('howItWorksNoteP')}</p>
            </CardContent>
        </Card>

      </div>
    </AuthenticatedLayout>
  );
}

