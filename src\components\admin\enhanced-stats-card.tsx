"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LucideIcon, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EnhancedStatsCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo';
  href?: string;
  description?: string;
  trend?: number[];
  loading?: boolean;
}

const colorClasses = {
  blue: {
    icon: 'text-blue-500',
    bg: 'bg-blue-50 dark:bg-blue-950',
    border: 'border-blue-200 dark:border-blue-800',
    gradient: 'from-blue-500/10 to-blue-600/10'
  },
  green: {
    icon: 'text-green-500',
    bg: 'bg-green-50 dark:bg-green-950',
    border: 'border-green-200 dark:border-green-800',
    gradient: 'from-green-500/10 to-green-600/10'
  },
  yellow: {
    icon: 'text-yellow-500',
    bg: 'bg-yellow-50 dark:bg-yellow-950',
    border: 'border-yellow-200 dark:border-yellow-800',
    gradient: 'from-yellow-500/10 to-yellow-600/10'
  },
  red: {
    icon: 'text-red-500',
    bg: 'bg-red-50 dark:bg-red-950',
    border: 'border-red-200 dark:border-red-800',
    gradient: 'from-red-500/10 to-red-600/10'
  },
  purple: {
    icon: 'text-purple-500',
    bg: 'bg-purple-50 dark:bg-purple-950',
    border: 'border-purple-200 dark:border-purple-800',
    gradient: 'from-purple-500/10 to-purple-600/10'
  },
  indigo: {
    icon: 'text-indigo-500',
    bg: 'bg-indigo-50 dark:bg-indigo-950',
    border: 'border-indigo-200 dark:border-indigo-800',
    gradient: 'from-indigo-500/10 to-indigo-600/10'
  }
};

export function EnhancedStatsCard({
  title,
  value,
  icon: Icon,
  change,
  changeType = 'neutral',
  color = 'blue',
  href,
  description,
  trend,
  loading = false
}: EnhancedStatsCardProps) {
  const colorClass = colorClasses[color];

  const getChangeIcon = () => {
    switch (changeType) {
      case 'positive':
        return TrendingUp;
      case 'negative':
        return TrendingDown;
      default:
        return Minus;
    }
  };

  const getChangeColor = () => {
    switch (changeType) {
      case 'positive':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'negative':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const ChangeIcon = getChangeIcon();

  const CardWrapper = href ? 'a' : 'div';
  const cardProps = href ? { href } : {};

  if (loading) {
    return (
      <Card className="shadow-lg border-0 overflow-hidden">
        <div className={cn('h-1', `bg-gradient-to-r ${colorClass.gradient}`)} />
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-3 flex-1">
              <div className="h-4 bg-muted rounded animate-pulse" />
              <div className="h-8 bg-muted rounded animate-pulse w-20" />
              <div className="h-3 bg-muted rounded animate-pulse w-16" />
            </div>
            <div className={cn('p-3 rounded-full', colorClass.bg)}>
              <div className="h-6 w-6 bg-muted rounded animate-pulse" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <CardWrapper {...cardProps}>
      <Card className={cn(
        'shadow-lg border-0 overflow-hidden transition-all duration-300 hover:shadow-xl',
        href && 'cursor-pointer hover:scale-105'
      )}>
        <div className={cn('h-1', `bg-gradient-to-r ${colorClass.gradient}`)} />
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2 flex-1">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-muted-foreground">
                  {title}
                </h3>
                {change && (
                  <Badge 
                    variant="secondary" 
                    className={cn('text-xs font-medium', getChangeColor())}
                  >
                    <ChangeIcon className="h-3 w-3 mr-1" />
                    {change}
                  </Badge>
                )}
              </div>
              
              <div className="flex items-baseline space-x-2">
                <span className="text-3xl font-bold text-foreground">
                  {typeof value === 'number' ? value.toLocaleString() : value}
                </span>
              </div>
              
              {description && (
                <p className="text-xs text-muted-foreground">
                  {description}
                </p>
              )}
              
              {trend && trend.length > 0 && (
                <div className="flex items-center space-x-1 mt-2">
                  <div className="flex space-x-0.5">
                    {trend.slice(-7).map((value, index) => (
                      <div
                        key={index}
                        className={cn('w-1 rounded-full', colorClass.icon.replace('text-', 'bg-'))}
                        style={{ height: `${Math.max(4, (value / Math.max(...trend)) * 16)}px` }}
                      />
                    ))}
                  </div>
                  <span className="text-xs text-muted-foreground">7 أيام</span>
                </div>
              )}
            </div>
            
            <div className={cn('p-3 rounded-full', colorClass.bg, colorClass.border, 'border')}>
              <Icon className={cn('h-6 w-6', colorClass.icon)} />
            </div>
          </div>
        </CardContent>
      </Card>
    </CardWrapper>
  );
}

// Grid component for multiple stats cards
interface StatsGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  className?: string;
}

export function StatsGrid({ children, columns = 4, className }: StatsGridProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
  };

  return (
    <div className={cn('grid gap-6', gridCols[columns], className)}>
      {children}
    </div>
  );
}

// Preset stats cards for common admin metrics
export const AdminStatsPresets = {
  totalUsers: (count: number, change?: string) => (
    <EnhancedStatsCard
      title="إجمالي المستخدمين"
      value={count}
      icon={require('lucide-react').Users}
      change={change}
      changeType={change?.startsWith('+') ? 'positive' : change?.startsWith('-') ? 'negative' : 'neutral'}
      color="blue"
      description="جميع المستخدمين المسجلين"
    />
  ),
  
  activeSubscriptions: (count: number, change?: string) => (
    <EnhancedStatsCard
      title="الاشتراكات النشطة"
      value={count}
      icon={require('lucide-react').DollarSign}
      change={change}
      changeType={change?.startsWith('+') ? 'positive' : change?.startsWith('-') ? 'negative' : 'neutral'}
      color="green"
      description="الاشتراكات المدفوعة والنشطة"
    />
  ),
  
  systemLogs: (count: number, change?: string) => (
    <EnhancedStatsCard
      title="سجلات النظام"
      value={count}
      icon={require('lucide-react').FileText}
      change={change}
      changeType="neutral"
      color="yellow"
      description="إجمالي السجلات المسجلة"
    />
  ),
  
  supportTickets: (count: number, change?: string) => (
    <EnhancedStatsCard
      title="تذاكر الدعم"
      value={count}
      icon={require('lucide-react').MessageSquare}
      change={change}
      changeType={change?.startsWith('-') ? 'positive' : change?.startsWith('+') ? 'negative' : 'neutral'}
      color="red"
      description="التذاكر المعلقة والمفتوحة"
    />
  )
};
