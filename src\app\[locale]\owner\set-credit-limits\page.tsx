
"use client";

import { useState } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth-context';
import { useScopedI18n } from '@/lib/i18n/client';
import {
  Landmark,
  Search,
  Edit,
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Shield,
  Settings,
  Save
} from 'lucide-react';

// Mock data
const mockCustomerBalances = [
  {
    id: '1',
    customerName: 'أحمد محمد علي',
    customerId: 'customer001',
    totalDebt: 15000,
    totalCredit: 2000,
    netBalance: 13000,
    creditLimit: 20000,
    lastPayment: '2024-01-15',
    lastTransaction: '2024-01-20',
    status: 'good' as const,
    phone: '*********',
    email: '<EMAIL>',
    creditScore: 85,
    accountOpenDate: '2023-06-15'
  },
  {
    id: '2',
    customerName: 'فاطمة حسن',
    customerId: 'customer002',
    totalDebt: 25000,
    totalCredit: 1000,
    netBalance: 24000,
    creditLimit: 20000,
    lastPayment: '2024-01-10',
    lastTransaction: '2024-01-22',
    status: 'warning' as const,
    phone: '*********',
    email: '<EMAIL>',
    creditScore: 72,
    accountOpenDate: '2023-08-20'
  },
  {
    id: '3',
    customerName: 'محمد أحمد',
    customerId: 'customer003',
    totalDebt: 35000,
    totalCredit: 500,
    netBalance: 34500,
    creditLimit: 30000,
    lastPayment: '2024-01-05',
    lastTransaction: '2024-01-25',
    status: 'overdue' as const,
    phone: '*********',
    email: '<EMAIL>',
    creditScore: 58,
    accountOpenDate: '2023-04-10'
  },
  {
    id: '4',
    customerName: 'سارة علي',
    customerId: 'customer004',
    totalDebt: 8000,
    totalCredit: 3000,
    netBalance: 5000,
    creditLimit: 15000,
    lastPayment: '2024-01-18',
    lastTransaction: '2024-01-23',
    status: 'good' as const,
    phone: '*********',
    email: '<EMAIL>',
    creditScore: 92,
    accountOpenDate: '2023-09-05'
  }
];

export default function OwnerSetCreditLimitsPage() {
  const t = useScopedI18n('common');
  const tSpecific = useScopedI18n('ownerSetCreditLimits');
  const { user } = useAuth();
  const { toast } = useToast();

  const [customers, setCustomers] = useState(mockCustomerBalances);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isPolicyDialogOpen, setIsPolicyDialogOpen] = useState(false);
  const [newCreditLimit, setNewCreditLimit] = useState('');
  const [changeReason, setChangeReason] = useState('');
  const [creditPolicySettings, setCreditPolicySettings] = useState({
    maxCreditLimit: '100000',
    defaultCreditLimit: '10000',
    riskThreshold: '80',
    autoApprovalLimit: '5000'
  });

  // Filter customers
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.customerId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone?.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || customer.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'good':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />جيد</Badge>;
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-500"><Clock className="w-3 h-3 mr-1" />تحذير</Badge>;
      case 'overdue':
        return <Badge variant="destructive"><AlertTriangle className="w-3 h-3 mr-1" />متأخر</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };
  // Handle credit limit update
  const handleUpdateCreditLimit = () => {
    if (!selectedCustomer || !newCreditLimit || !changeReason) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال حد ائتمان صحيح وسبب التغيير',
        variant: 'destructive'
      });
      return;
    }

    const limitValue = parseFloat(newCreditLimit);
    const maxLimit = parseFloat(creditPolicySettings.maxCreditLimit);

    if (limitValue < 0) {
      toast({
        title: 'خطأ',
        description: 'حد الائتمان يجب أن يكون أكبر من أو يساوي صفر',
        variant: 'destructive'
      });
      return;
    }

    if (limitValue > maxLimit) {
      toast({
        title: 'تحذير',
        description: `حد الائتمان يتجاوز الحد الأقصى المسموح (${maxLimit.toLocaleString()} ر.ي)`,
        variant: 'destructive'
      });
      return;
    }

    // Update customer credit limit
    const updatedCustomers = customers.map(customer => {
      if (customer.id === selectedCustomer.id) {
        const updatedCustomer = { ...customer, creditLimit: limitValue };

        // Update status based on new credit limit
        if (updatedCustomer.netBalance > updatedCustomer.creditLimit) {
          updatedCustomer.status = 'overdue';
        } else if (updatedCustomer.netBalance > updatedCustomer.creditLimit * 0.8) {
          updatedCustomer.status = 'warning';
        } else {
          updatedCustomer.status = 'good';
        }

        return updatedCustomer;
      }
      return customer;
    });

    setCustomers(updatedCustomers);
    setSelectedCustomer(updatedCustomers.find(c => c.id === selectedCustomer.id) || null);

    toast({
      title: 'تم بنجاح',
      description: `تم تحديث حد الائتمان للعميل ${selectedCustomer.customerName} من ${selectedCustomer.creditLimit.toLocaleString()} إلى ${limitValue.toLocaleString()} ر.ي`
    });

    setNewCreditLimit('');
    setChangeReason('');
    setIsEditDialogOpen(false);
  };

  // Handle policy update
  const handleUpdatePolicy = () => {
    toast({
      title: 'تم بنجاح',
      description: 'تم تحديث سياسة الائتمان بنجاح'
    });
    setIsPolicyDialogOpen(false);
  };

  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <Landmark className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold text-foreground">{t('nav_setCreditLimitsOwner')}</h1>
              <p className="text-muted-foreground">
                {tSpecific('description')}
              </p>
            </div>
          </div>
          <Button
            onClick={() => setIsPolicyDialogOpen(true)}
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            إعدادات السياسة
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي العملاء</CardTitle>
              <Users className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{customers.length}</div>
              <p className="text-xs text-muted-foreground">عميل مسجل</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">حالة جيدة</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {customers.filter(c => c.status === 'good').length}
              </div>
              <p className="text-xs text-muted-foreground">عميل</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">تحذير</CardTitle>
              <Clock className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {customers.filter(c => c.status === 'warning').length}
              </div>
              <p className="text-xs text-muted-foreground">عميل</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">متأخر</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {customers.filter(c => c.status === 'overdue').length}
              </div>
              <p className="text-xs text-muted-foreground">عميل</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الائتمان</CardTitle>
              <Shield className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {customers.reduce((sum, c) => sum + c.creditLimit, 0).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">ر.ي</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Card>
          <CardHeader>
            <CardTitle>إدارة حدود الائتمان للعملاء</CardTitle>
            <CardDescription>
              عرض وتعديل حدود الائتمان مع إعدادات السياسة المتقدمة
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search and Filter */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Label htmlFor="search">البحث</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="البحث بالاسم، رقم العميل، أو الهاتف..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full md:w-48">
                <Label htmlFor="status-filter">تصفية حسب الحالة</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="good">جيد</SelectItem>
                    <SelectItem value="warning">تحذير</SelectItem>
                    <SelectItem value="overdue">متأخر</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Current Policy Summary */}
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Shield className="h-5 w-5 text-blue-600" />
                <span className="font-semibold text-blue-800">السياسة الحالية للائتمان</span>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">الحد الأقصى:</span>
                  <div className="font-semibold">{parseFloat(creditPolicySettings.maxCreditLimit).toLocaleString()} ر.ي</div>
                </div>
                <div>
                  <span className="text-muted-foreground">الحد الافتراضي:</span>
                  <div className="font-semibold">{parseFloat(creditPolicySettings.defaultCreditLimit).toLocaleString()} ر.ي</div>
                </div>
                <div>
                  <span className="text-muted-foreground">عتبة المخاطر:</span>
                  <div className="font-semibold">{creditPolicySettings.riskThreshold}%</div>
                </div>
                <div>
                  <span className="text-muted-foreground">الموافقة التلقائية:</span>
                  <div className="font-semibold">{parseFloat(creditPolicySettings.autoApprovalLimit).toLocaleString()} ر.ي</div>
                </div>
              </div>
            </div>

            {/* Customers Table */}
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>اسم العميل</TableHead>
                  <TableHead>رقم الهاتف</TableHead>
                  <TableHead>الرصيد الصافي</TableHead>
                  <TableHead>حد الائتمان</TableHead>
                  <TableHead>الائتمان المتاح</TableHead>
                  <TableHead>نقاط الائتمان</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCustomers.map((customer) => (
                  <TableRow key={customer.id}>
                    <TableCell className="font-medium">{customer.customerName}</TableCell>
                    <TableCell>{customer.phone}</TableCell>
                    <TableCell className={customer.netBalance > 0 ? "text-red-600" : "text-green-600"}>
                      {customer.netBalance.toLocaleString()} ر.ي
                    </TableCell>
                    <TableCell className="font-semibold">
                      {customer.creditLimit.toLocaleString()} ر.ي
                    </TableCell>
                    <TableCell className="text-blue-600">
                      {(customer.creditLimit - customer.netBalance).toLocaleString()} ر.ي
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className={`font-semibold ${
                          (customer.creditScore || 0) >= 80 ? 'text-green-600' :
                          (customer.creditScore || 0) >= 60 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {customer.creditScore || 'غير محدد'}
                        </span>
                        {(customer.creditScore || 0) >= 80 && <TrendingUp className="h-4 w-4 text-green-500" />}
                        {(customer.creditScore || 0) < 60 && <TrendingDown className="h-4 w-4 text-red-500" />}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(customer.status)}</TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedCustomer(customer);
                          setNewCreditLimit(customer.creditLimit.toString());
                          setIsEditDialogOpen(true);
                        }}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        تعديل
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
        {/* Edit Credit Limit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>تعديل حد الائتمان - صلاحية المالك</DialogTitle>
              <DialogDescription>
                تعديل حد الائتمان للعميل: {selectedCustomer?.customerName}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label htmlFor="current-limit">حد الائتمان الحالي</Label>
                <Input
                  id="current-limit"
                  value={selectedCustomer?.creditLimit.toLocaleString() + ' ر.ي'}
                  disabled
                />
              </div>

              <div>
                <Label htmlFor="new-limit">حد الائتمان الجديد (ر.ي)</Label>
                <Input
                  id="new-limit"
                  type="number"
                  placeholder="أدخل حد الائتمان الجديد"
                  value={newCreditLimit}
                  onChange={(e) => setNewCreditLimit(e.target.value)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  الحد الأقصى المسموح: {parseFloat(creditPolicySettings.maxCreditLimit).toLocaleString()} ر.ي
                </p>
              </div>

              <div>
                <Label htmlFor="change-reason">سبب التغيير</Label>
                <Select value={changeReason} onValueChange={setChangeReason}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر سبب التغيير" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="تحسن في سجل الدفع">تحسن في سجل الدفع</SelectItem>
                    <SelectItem value="تأخر في السداد">تأخر في السداد</SelectItem>
                    <SelectItem value="زيادة حجم التعامل">زيادة حجم التعامل</SelectItem>
                    <SelectItem value="تقليل المخاطر">تقليل المخاطر</SelectItem>
                    <SelectItem value="طلب العميل">طلب العميل</SelectItem>
                    <SelectItem value="مراجعة دورية">مراجعة دورية</SelectItem>
                    <SelectItem value="قرار إداري">قرار إداري</SelectItem>
                    <SelectItem value="أخرى">أخرى</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {selectedCustomer && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span>الرصيد الحالي:</span>
                      <span className={selectedCustomer.netBalance > 0 ? "text-red-600" : "text-green-600"}>
                        {selectedCustomer.netBalance.toLocaleString()} ر.ي
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>نقاط الائتمان:</span>
                      <span className={`font-semibold ${
                        (selectedCustomer.creditScore || 0) >= 80 ? 'text-green-600' :
                        (selectedCustomer.creditScore || 0) >= 60 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {selectedCustomer.creditScore || 'غير محدد'}
                      </span>
                    </div>
                    {newCreditLimit && (
                      <div className="flex justify-between font-semibold">
                        <span>الائتمان المتاح الجديد:</span>
                        <span className="text-blue-600">
                          {(parseFloat(newCreditLimit) - selectedCustomer.netBalance).toLocaleString()} ر.ي
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-sm text-green-800">
                  <Shield className="h-4 w-4 inline mr-1" />
                  كمالك، لديك صلاحية كاملة لتعديل حدود الائتمان دون قيود إضافية.
                </p>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleUpdateCreditLimit}>
                تحديث حد الائتمان
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Credit Policy Settings Dialog */}
        <Dialog open={isPolicyDialogOpen} onOpenChange={setIsPolicyDialogOpen}>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                إعدادات سياسة الائتمان
              </DialogTitle>
              <DialogDescription>
                تحديد القواعد والحدود العامة لسياسة الائتمان في المتجر
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="max-limit">الحد الأقصى للائتمان (ر.ي)</Label>
                  <Input
                    id="max-limit"
                    type="number"
                    value={creditPolicySettings.maxCreditLimit}
                    onChange={(e) => setCreditPolicySettings(prev => ({ ...prev, maxCreditLimit: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="default-limit">الحد الافتراضي (ر.ي)</Label>
                  <Input
                    id="default-limit"
                    type="number"
                    value={creditPolicySettings.defaultCreditLimit}
                    onChange={(e) => setCreditPolicySettings(prev => ({ ...prev, defaultCreditLimit: e.target.value }))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="risk-threshold">عتبة المخاطر (%)</Label>
                  <Input
                    id="risk-threshold"
                    type="number"
                    min="0"
                    max="100"
                    value={creditPolicySettings.riskThreshold}
                    onChange={(e) => setCreditPolicySettings(prev => ({ ...prev, riskThreshold: e.target.value }))}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    النسبة المئوية من حد الائتمان التي تؤدي إلى تحذير
                  </p>
                </div>
                <div>
                  <Label htmlFor="auto-approval">حد الموافقة التلقائية (ر.ي)</Label>
                  <Input
                    id="auto-approval"
                    type="number"
                    value={creditPolicySettings.autoApprovalLimit}
                    onChange={(e) => setCreditPolicySettings(prev => ({ ...prev, autoApprovalLimit: e.target.value }))}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    الحد الذي يمكن للموظفين الموافقة عليه تلقائياً
                  </p>
                </div>
              </div>

              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <AlertTriangle className="h-4 w-4 inline mr-1" />
                  تغيير هذه الإعدادات سيؤثر على جميع العمليات المستقبلية للائتمان.
                </p>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsPolicyDialogOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleUpdatePolicy} className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                حفظ الإعدادات
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AuthenticatedLayout>
  );
}