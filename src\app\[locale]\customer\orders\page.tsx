
"use client"; 

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Package, ShoppingBag, Truck, Info, RotateCcw, FileText } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useScopedI18n } from '@/lib/i18n/client';


const mockOrders = [
  { id: 'ORD12345', date: '2024-07-20', total: 45.99, status: 'processing', items: [{name: 'Apples', qty: 2}, {name: 'Milk', qty:1}] },
  { id: 'ORD12346', date: '2024-07-18', total: 78.50, status: 'shipped', items: [{name: 'Chicken Breast', qty:1}, {name:'Bread', qty:2}, {name:'Orange Juice', qty:1}], trackingNumber: 'TRK987654321' },
  { id: 'ORD12347', date: '2024-07-15', total: 22.00, status: 'delivered', items: [{name: 'Cereal', qty:1}, {name:'Yogurt', qty:4}] },
  { id: 'ORD12348', date: '2024-07-10', total: 35.60, status: 'cancelled', items: [{name: 'Soda Pack', qty:1}, {name:'Chips', qty:2}] },
];

type OrderStatusKey = 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'all' | 'active';

const tabConfig: { value: OrderStatusKey; labelKey: keyof ReturnType<typeof useScopedI18n<'customerOrders'>> }[] = [
  { value: 'active', labelKey: 'tabActive' },
  { value: 'delivered', labelKey: 'tabDelivered' },
  { value: 'cancelled', labelKey: 'tabCancelled' },
  { value: 'all', labelKey: 'tabAllOrders' },
];


export default function CustomerOrdersPage() {
  const t = useScopedI18n('customerOrders');
  const tCommon = useScopedI18n('common');

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'processing': return 'secondary';
      case 'shipped': return 'default'; 
      case 'delivered': return 'outline'; 
      case 'cancelled': return 'destructive';
      default: return 'secondary';
    }
  };
  
  const getStatusTranslation = (status: string) => {
    const key = `status${status.charAt(0).toUpperCase() + status.slice(1)}` as any;
    return t(key, undefined, status);
  }

  const filterOrdersByStatus = (statusFilter: OrderStatusKey) => {
    if (statusFilter === 'all') return mockOrders;
    if (statusFilter === 'active') return mockOrders.filter(o => o.status === 'processing' || o.status === 'shipped');
    return mockOrders.filter(o => o.status.toLowerCase() === statusFilter.toLowerCase());
  }
  
  return (
    <AuthenticatedLayout expectedRole="customer">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Package className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        <Tabs defaultValue="active" className="w-full">
          <TabsList className="grid w-full grid-cols-2 md:grid-cols-4 mb-4">
            {tabConfig.map(tab => (
                 <TabsTrigger 
                    key={tab.value} 
                    value={tab.value}
                    className={tab.value === 'cancelled' ? 'hidden md:inline-flex' : ''}
                  >
                    {t(tab.labelKey)}
                </TabsTrigger>
            ))}
          </TabsList>

          {tabConfig.map(tabInfo => (
            <TabsContent key={tabInfo.value} value={tabInfo.value}>
              {filterOrdersByStatus(tabInfo.value).length === 0 ? (
                <Card className="shadow-md">
                  <CardHeader>
                    <CardTitle>{t('noOrdersFoundTitle')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground mb-4">{t('noOrdersWithStatus', { status: t(tabInfo.labelKey) })}</p>
                    {tabInfo.value === 'active' && (
                      <Button asChild>
                        <Link href="/customer/shop">
                          <ShoppingBag className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('startShoppingButton')}
                        </Link>
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {filterOrdersByStatus(tabInfo.value).map(order => (
                    <Card key={order.id} className="shadow-sm hover:shadow-md transition-shadow">
                      <CardHeader className="pb-4">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                          <div>
                            <CardTitle className="text-xl hover:underline">
                                <Link href={`/customer/orders/${order.id}`}>{order.id}</Link>
                            </CardTitle>
                            <CardDescription>{t('orderDetails', { date: order.date, itemCount: order.items.reduce((sum, item) => sum + item.qty, 0) })}</CardDescription>
                          </div>
                          <Badge variant={getStatusBadgeVariant(order.status) as any} className="text-sm whitespace-nowrap mt-1 sm:mt-0 self-start sm:self-auto capitalize">
                            {getStatusTranslation(order.status)}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                         <div className="text-sm text-muted-foreground">
                            {order.items.slice(0, 2).map(item => `${item.name} (${t('quantityLabel', {qty: item.qty})})`).join(', ')}
                            {order.items.length > 2 ? `, ${t('andMoreItems', { count: order.items.length - 2 })}` : ''}
                         </div>
                        <p className="font-semibold text-lg">{t('totalLabel')}: YER {order.total.toFixed(2)}</p>
                        {order.trackingNumber && (
                          <p className="text-sm text-primary flex items-center">
                            <Truck className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('trackingLabel')}: {order.trackingNumber}
                          </p>
                        )}
                        <div className="flex flex-wrap gap-2 pt-2">
                            <Button variant="outline" size="sm" asChild>
                                <Link href={`/customer/orders/${order.id}`}>
                                    <Info className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('viewDetailsButton')}
                                </Link>
                            </Button>
                            {order.status === 'delivered' && (
                                 <Button variant="outline" size="sm">
                                    <RotateCcw className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('reorderButton')}
                                </Button>
                            )}
                             <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-primary">
                                <FileText className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('invoiceButton')}
                            </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </AuthenticatedLayout>
  );
}

