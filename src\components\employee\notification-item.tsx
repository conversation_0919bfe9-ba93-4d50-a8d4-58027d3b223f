"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  X, 
  ExternalLink,
  Clock,
  User
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, formatDistanceToNow } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import type { Notification } from '@/types';
import { useRouter } from 'next/navigation';
import { useScopedI18n } from '@/lib/i18n/client';
import { useI18n } from '@/lib/i18n/client';

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
  className?: string;
}

const getNotificationIcon = (type: Notification['type']) => {
  switch (type) {
    case 'error':
    case 'warning':
      return AlertTriangle;
    case 'success':
      return CheckCircle;
    case 'info':
    case 'system':
    case 'order':
    case 'task':
      return Info;
    default:
      return Bell;
  }
};

const getNotificationColor = (type: Notification['type'], priority: Notification['priority']) => {
  if (priority === 'urgent') return 'border-red-500 bg-red-50 dark:bg-red-950/20';
  if (priority === 'high') return 'border-orange-500 bg-orange-50 dark:bg-orange-950/20';
  
  switch (type) {
    case 'error':
      return 'border-red-500 bg-red-50 dark:bg-red-950/20';
    case 'warning':
      return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950/20';
    case 'success':
      return 'border-green-500 bg-green-50 dark:bg-green-950/20';
    case 'order':
      return 'border-blue-500 bg-blue-50 dark:bg-blue-950/20';
    case 'task':
      return 'border-purple-500 bg-purple-50 dark:bg-purple-950/20';
    case 'system':
      return 'border-gray-500 bg-gray-50 dark:bg-gray-950/20';
    default:
      return 'border-gray-200 bg-white dark:bg-gray-950';
  }
};

const getPriorityBadgeVariant = (priority: Notification['priority']) => {
  switch (priority) {
    case 'urgent':
      return 'destructive';
    case 'high':
      return 'default';
    case 'medium':
      return 'secondary';
    case 'low':
      return 'outline';
    default:
      return 'secondary';
  }
};

export function NotificationItem({ 
  notification, 
  onMarkAsRead, 
  onDelete, 
  className 
}: NotificationItemProps) {
  const router = useRouter();
  const t = useScopedI18n('employeeNotifications');
  const currentLocale = useI18n().locale;
  const dateLocale = currentLocale === 'ar' ? ar : enUS;
  
  const Icon = getNotificationIcon(notification.type);
  const colorClasses = getNotificationColor(notification.type, notification.priority);
  
  const handleAction = () => {
    if (!notification.isRead) {
      onMarkAsRead(notification.id);
    }
    if (notification.actionUrl) {
      router.push(notification.actionUrl);
    }
  };

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMarkAsRead(notification.id);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(notification.id);
  };

  const formatNotificationDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return formatDistanceToNow(date, { addSuffix: true, locale: dateLocale });
    } else {
      return format(date, 'MMM dd, yyyy HH:mm', { locale: dateLocale });
    }
  };

  return (
    <Card 
      className={cn(
        'transition-all duration-200 hover:shadow-md cursor-pointer',
        colorClasses,
        !notification.isRead && 'ring-2 ring-primary/20',
        className
      )}
      onClick={handleAction}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Icon */}
          <div className={cn(
            'flex-shrink-0 p-2 rounded-full',
            notification.priority === 'urgent' ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400' :
            notification.priority === 'high' ? 'bg-orange-100 text-orange-600 dark:bg-orange-900 dark:text-orange-400' :
            'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
          )}>
            <Icon className="h-4 w-4" />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2 mb-2">
              <h3 className={cn(
                'font-semibold text-sm leading-tight',
                !notification.isRead && 'text-foreground',
                notification.isRead && 'text-muted-foreground'
              )}>
                {notification.title}
              </h3>
              
              <div className="flex items-center gap-1 flex-shrink-0">
                <Badge variant={getPriorityBadgeVariant(notification.priority)} className="text-xs">
                  {t(`priority_${notification.priority}`)}
                </Badge>
                {!notification.isRead && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleMarkAsRead}
                    className="h-6 w-6 p-0 hover:bg-primary/10"
                    title={t('markAsRead')}
                  >
                    <CheckCircle className="h-3 w-3" />
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDelete}
                  className="h-6 w-6 p-0 hover:bg-destructive/10 hover:text-destructive"
                  title={t('deleteNotification')}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>

            <p className={cn(
              'text-sm mb-3 leading-relaxed',
              !notification.isRead ? 'text-foreground' : 'text-muted-foreground'
            )}>
              {notification.message}
            </p>

            {/* Metadata */}
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>{formatNotificationDate(notification.createdAt)}</span>
                </div>
                {notification.senderName && (
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    <span>{notification.senderName}</span>
                  </div>
                )}
              </div>

              {notification.actionUrl && notification.actionLabel && (
                <Button
                  variant="outline"
                  size="sm"
                  className="h-7 text-xs"
                  onClick={handleAction}
                >
                  {notification.actionLabel}
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              )}
            </div>

            {/* Expiry warning */}
            {notification.expiresAt && new Date(notification.expiresAt) > new Date() && (
              <div className="mt-2 text-xs text-amber-600 dark:text-amber-400">
                {t('expiresAt', { 
                  date: formatNotificationDate(notification.expiresAt) 
                })}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
