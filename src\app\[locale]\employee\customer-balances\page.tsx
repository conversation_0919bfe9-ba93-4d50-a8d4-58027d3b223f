
"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { useState, useEffect } from 'react';
import { Search, Plus, Eye, Edit, DollarSign, Calendar, User, CreditCard, AlertTriangle, CheckCircle, Clock, TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { CustomerBalanceReport } from '@/components/employee/customer-balance-report';
import { CustomerBalanceStats } from '@/components/employee/customer-balance-stats';

// Mock data for customer balances
interface CustomerBalance {
  id: string;
  customerName: string;
  customerId: string;
  totalDebt: number;
  totalCredit: number;
  netBalance: number;
  creditLimit: number;
  lastPayment: string;
  lastTransaction: string;
  status: 'good' | 'warning' | 'overdue';
  phone?: string;
  email?: string;
}

interface Transaction {
  id: string;
  customerId: string;
  type: 'debt' | 'credit' | 'payment';
  amount: number;
  description: string;
  date: string;
  employeeId: string;
  employeeName: string;
}

const mockCustomerBalances: CustomerBalance[] = [
  {
    id: '1',
    customerName: 'أحمد محمد علي',
    customerId: 'customer001',
    totalDebt: 15000,
    totalCredit: 2000,
    netBalance: 13000,
    creditLimit: 20000,
    lastPayment: '2024-01-15',
    lastTransaction: '2024-01-20',
    status: 'good',
    phone: '770123456',
    email: '<EMAIL>'
  },
  {
    id: '2',
    customerName: 'فاطمة حسن',
    customerId: 'customer002',
    totalDebt: 25000,
    totalCredit: 1000,
    netBalance: 24000,
    creditLimit: 20000,
    lastPayment: '2024-01-10',
    lastTransaction: '2024-01-22',
    status: 'warning',
    phone: '770234567',
    email: '<EMAIL>'
  },
  {
    id: '3',
    customerName: 'محمد عبدالله',
    customerId: 'customer003',
    totalDebt: 35000,
    totalCredit: 500,
    netBalance: 34500,
    creditLimit: 25000,
    lastPayment: '2023-12-20',
    lastTransaction: '2024-01-25',
    status: 'overdue',
    phone: '770345678',
    email: '<EMAIL>'
  },
  {
    id: '4',
    customerName: 'عائشة سالم',
    customerId: 'customer004',
    totalDebt: 8000,
    totalCredit: 3000,
    netBalance: 5000,
    creditLimit: 15000,
    lastPayment: '2024-01-18',
    lastTransaction: '2024-01-21',
    status: 'good',
    phone: '770456789',
    email: '<EMAIL>'
  }
];

const mockTransactions: Transaction[] = [
  {
    id: '1',
    customerId: 'customer001',
    type: 'debt',
    amount: 5000,
    description: 'شراء مواد غذائية',
    date: '2024-01-20',
    employeeId: 'emp001',
    employeeName: 'أحمد الموظف'
  },
  {
    id: '2',
    customerId: 'customer001',
    type: 'payment',
    amount: 3000,
    description: 'دفعة نقدية',
    date: '2024-01-15',
    employeeId: 'emp001',
    employeeName: 'أحمد الموظف'
  },
  {
    id: '3',
    customerId: 'customer002',
    type: 'debt',
    amount: 8000,
    description: 'شراء أدوات منزلية',
    date: '2024-01-22',
    employeeId: 'emp002',
    employeeName: 'فاطمة الموظفة'
  }
];

export default function EmployeeCustomerBalancesPage() {
  const t = useScopedI18n('employee');
  const tCommon = useScopedI18n('common');
  const { user } = useAuth();
  const { toast } = useToast();

  const [customers, setCustomers] = useState<CustomerBalance[]>(mockCustomerBalances);
  const [transactions, setTransactions] = useState<Transaction[]>(mockTransactions);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerBalance | null>(null);
  const [isAddTransactionOpen, setIsAddTransactionOpen] = useState(false);
  const [isEditCreditLimitOpen, setIsEditCreditLimitOpen] = useState(false);
  const [newTransaction, setNewTransaction] = useState({
    type: 'payment' as 'debt' | 'credit' | 'payment',
    amount: '',
    description: ''
  });
  const [newCreditLimit, setNewCreditLimit] = useState('');

  // Check if user has permission
  const hasPermission = user?.permissions?.canManageCustomerBalances;

  if (!hasPermission) {
    return (
      <AuthenticatedLayout expectedRole="employee">
        <div className="space-y-6 p-1">
          <Card className="shadow-md">
            <CardHeader className="text-center">
              <AlertTriangle className="h-16 w-16 mx-auto text-destructive mb-4" />
              <CardTitle>{tCommon('accessDenied')}</CardTitle>
              <CardDescription>
                ليس لديك صلاحية للوصول إلى هذه الصفحة
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </AuthenticatedLayout>
    );
  }

  const filteredCustomers = customers.filter(customer =>
    customer.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone?.includes(searchTerm) ||
    customer.customerId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'good':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />جيد</Badge>;
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-500"><Clock className="w-3 h-3 mr-1" />تحذير</Badge>;
      case 'overdue':
        return <Badge variant="destructive"><AlertTriangle className="w-3 h-3 mr-1" />متأخر</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleAddTransaction = () => {
    if (!selectedCustomer || !newTransaction.amount || !newTransaction.description) {
      toast({
        title: 'خطأ',
        description: 'يرجى ملء جميع الحقول المطلوبة',
        variant: 'destructive'
      });
      return;
    }

    const transaction: Transaction = {
      id: Date.now().toString(),
      customerId: selectedCustomer.customerId,
      type: newTransaction.type,
      amount: parseFloat(newTransaction.amount),
      description: newTransaction.description,
      date: new Date().toISOString().split('T')[0],
      employeeId: user?.id || '',
      employeeName: user?.name || user?.username || ''
    };

    setTransactions([transaction, ...transactions]);

    // Update customer balance
    const updatedCustomers = customers.map(customer => {
      if (customer.id === selectedCustomer.id) {
        let updatedCustomer = { ...customer };

        if (transaction.type === 'debt') {
          updatedCustomer.totalDebt += transaction.amount;
          updatedCustomer.netBalance += transaction.amount;
        } else if (transaction.type === 'credit') {
          updatedCustomer.totalCredit += transaction.amount;
          updatedCustomer.netBalance -= transaction.amount;
        } else if (transaction.type === 'payment') {
          updatedCustomer.netBalance -= transaction.amount;
        }

        updatedCustomer.lastTransaction = transaction.date;
        if (transaction.type === 'payment') {
          updatedCustomer.lastPayment = transaction.date;
        }

        // Update status based on new balance
        if (updatedCustomer.netBalance > updatedCustomer.creditLimit) {
          updatedCustomer.status = 'overdue';
        } else if (updatedCustomer.netBalance > updatedCustomer.creditLimit * 0.8) {
          updatedCustomer.status = 'warning';
        } else {
          updatedCustomer.status = 'good';
        }

        return updatedCustomer;
      }
      return customer;
    });

    setCustomers(updatedCustomers);
    setSelectedCustomer(updatedCustomers.find(c => c.id === selectedCustomer.id) || null);

    toast({
      title: 'تم بنجاح',
      description: 'تم إضافة المعاملة بنجاح'
    });

    setNewTransaction({ type: 'payment', amount: '', description: '' });
    setIsAddTransactionOpen(false);
  };

  const handleUpdateCreditLimit = () => {
    if (!selectedCustomer || !newCreditLimit) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال حد ائتمان صحيح',
        variant: 'destructive'
      });
      return;
    }

    const updatedCustomers = customers.map(customer => {
      if (customer.id === selectedCustomer.id) {
        const updatedCustomer = { ...customer, creditLimit: parseFloat(newCreditLimit) };

        // Update status based on new credit limit
        if (updatedCustomer.netBalance > updatedCustomer.creditLimit) {
          updatedCustomer.status = 'overdue';
        } else if (updatedCustomer.netBalance > updatedCustomer.creditLimit * 0.8) {
          updatedCustomer.status = 'warning';
        } else {
          updatedCustomer.status = 'good';
        }

        return updatedCustomer;
      }
      return customer;
    });

    setCustomers(updatedCustomers);
    setSelectedCustomer(updatedCustomers.find(c => c.id === selectedCustomer.id) || null);

    toast({
      title: 'تم بنجاح',
      description: 'تم تحديث حد الائتمان بنجاح'
    });

    setNewCreditLimit('');
    setIsEditCreditLimitOpen(false);
  };

  const getCustomerTransactions = (customerId: string) => {
    return transactions.filter(t => t.customerId === customerId).slice(0, 10);
  };

  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-6 p-1">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-foreground">أرصدة العملاء</h1>
        </div>

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي العملاء</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{customers.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الديون</CardTitle>
              <TrendingUp className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {customers.reduce((sum, c) => sum + c.totalDebt, 0).toLocaleString()} ر.ي
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الائتمان</CardTitle>
              <TrendingDown className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {customers.reduce((sum, c) => sum + c.totalCredit, 0).toLocaleString()} ر.ي
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الرصيد الصافي</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {customers.reduce((sum, c) => sum + c.netBalance, 0).toLocaleString()} ر.ي
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>البحث والتصفية</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <Label htmlFor="search">البحث</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="البحث بالاسم، رقم الهاتف، أو رقم العميل..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Tabs */}
        <Tabs defaultValue="customers" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="customers">إدارة العملاء</TabsTrigger>
            <TabsTrigger value="stats">الإحصائيات</TabsTrigger>
            <TabsTrigger value="reports">التقارير</TabsTrigger>
          </TabsList>

          <TabsContent value="customers" className="space-y-6">
            {/* Customer Balances Table */}
            <Card>
              <CardHeader>
                <CardTitle>قائمة العملاء وأرصدتهم</CardTitle>
                <CardDescription>
                  عرض تفصيلي لأرصدة جميع العملاء ومعلومات الائتمان
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>اسم العميل</TableHead>
                      <TableHead>رقم الهاتف</TableHead>
                      <TableHead>إجمالي الديون</TableHead>
                      <TableHead>إجمالي الائتمان</TableHead>
                      <TableHead>الرصيد الصافي</TableHead>
                      <TableHead>حد الائتمان</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>آخر دفعة</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredCustomers.map((customer) => (
                      <TableRow key={customer.id}>
                        <TableCell className="font-medium">{customer.customerName}</TableCell>
                        <TableCell>{customer.phone}</TableCell>
                        <TableCell className="text-red-600">{customer.totalDebt.toLocaleString()} ر.ي</TableCell>
                        <TableCell className="text-green-600">{customer.totalCredit.toLocaleString()} ر.ي</TableCell>
                        <TableCell className={customer.netBalance > 0 ? "text-red-600" : "text-green-600"}>
                          {customer.netBalance.toLocaleString()} ر.ي
                        </TableCell>
                        <TableCell>{customer.creditLimit.toLocaleString()} ر.ي</TableCell>
                        <TableCell>{getStatusBadge(customer.status)}</TableCell>
                        <TableCell>{customer.lastPayment}</TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedCustomer(customer)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            عرض التفاصيل
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stats" className="space-y-6">
            <CustomerBalanceStats customers={customers} />
          </TabsContent>

          <TabsContent value="reports" className="space-y-6">
            <CustomerBalanceReport customers={customers} />
          </TabsContent>
        </Tabs>

        {/* Customer Details Dialog */}
        {selectedCustomer && (
          <Dialog open={!!selectedCustomer} onOpenChange={() => setSelectedCustomer(null)}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>تفاصيل العميل: {selectedCustomer.customerName}</DialogTitle>
                <DialogDescription>
                  عرض تفصيلي لحساب العميل والمعاملات المالية
                </DialogDescription>
              </DialogHeader>

              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
                  <TabsTrigger value="transactions">المعاملات</TabsTrigger>
                  <TabsTrigger value="actions">الإجراءات</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">معلومات العميل</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span className="font-medium">الاسم:</span>
                          <span>{selectedCustomer.customerName}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">رقم العميل:</span>
                          <span>{selectedCustomer.customerId}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">رقم الهاتف:</span>
                          <span>{selectedCustomer.phone}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">البريد الإلكتروني:</span>
                          <span>{selectedCustomer.email}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">الحالة:</span>
                          {getStatusBadge(selectedCustomer.status)}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">الملخص المالي</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span className="font-medium">إجمالي الديون:</span>
                          <span className="text-red-600">{selectedCustomer.totalDebt.toLocaleString()} ر.ي</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">إجمالي الائتمان:</span>
                          <span className="text-green-600">{selectedCustomer.totalCredit.toLocaleString()} ر.ي</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">الرصيد الصافي:</span>
                          <span className={selectedCustomer.netBalance > 0 ? "text-red-600" : "text-green-600"}>
                            {selectedCustomer.netBalance.toLocaleString()} ر.ي
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">حد الائتمان:</span>
                          <span>{selectedCustomer.creditLimit.toLocaleString()} ر.ي</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">الائتمان المتاح:</span>
                          <span className="text-blue-600">
                            {(selectedCustomer.creditLimit - selectedCustomer.netBalance).toLocaleString()} ر.ي
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">آخر دفعة:</span>
                          <span>{selectedCustomer.lastPayment}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">آخر معاملة:</span>
                          <span>{selectedCustomer.lastTransaction}</span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="transactions" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>آخر المعاملات</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>التاريخ</TableHead>
                            <TableHead>النوع</TableHead>
                            <TableHead>المبلغ</TableHead>
                            <TableHead>الوصف</TableHead>
                            <TableHead>الموظف</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {getCustomerTransactions(selectedCustomer.customerId).map((transaction) => (
                            <TableRow key={transaction.id}>
                              <TableCell>{transaction.date}</TableCell>
                              <TableCell>
                                <Badge variant={
                                  transaction.type === 'debt' ? 'destructive' :
                                  transaction.type === 'credit' ? 'secondary' : 'default'
                                }>
                                  {transaction.type === 'debt' ? 'دين' :
                                   transaction.type === 'credit' ? 'ائتمان' : 'دفعة'}
                                </Badge>
                              </TableCell>
                              <TableCell className={
                                transaction.type === 'debt' ? 'text-red-600' : 'text-green-600'
                              }>
                                {transaction.amount.toLocaleString()} ر.ي
                              </TableCell>
                              <TableCell>{transaction.description}</TableCell>
                              <TableCell>{transaction.employeeName}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="actions" className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <Card>
                      <CardHeader>
                        <CardTitle>إضافة معاملة جديدة</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <Button
                          onClick={() => setIsAddTransactionOpen(true)}
                          className="w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          إضافة معاملة
                        </Button>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>تعديل حد الائتمان</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <Button
                          onClick={() => {
                            setNewCreditLimit(selectedCustomer.creditLimit.toString());
                            setIsEditCreditLimitOpen(true);
                          }}
                          variant="outline"
                          className="w-full"
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          تعديل حد الائتمان
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            </DialogContent>
          </Dialog>
        )}

        {/* Add Transaction Dialog */}
        <Dialog open={isAddTransactionOpen} onOpenChange={setIsAddTransactionOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>إضافة معاملة جديدة</DialogTitle>
              <DialogDescription>
                إضافة معاملة مالية جديدة للعميل: {selectedCustomer?.customerName}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label htmlFor="transaction-type">نوع المعاملة</Label>
                <Select
                  value={newTransaction.type}
                  onValueChange={(value: 'debt' | 'credit' | 'payment') =>
                    setNewTransaction({...newTransaction, type: value})
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر نوع المعاملة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="payment">دفعة</SelectItem>
                    <SelectItem value="debt">دين</SelectItem>
                    <SelectItem value="credit">ائتمان</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="transaction-amount">المبلغ (ر.ي)</Label>
                <Input
                  id="transaction-amount"
                  type="number"
                  placeholder="أدخل المبلغ"
                  value={newTransaction.amount}
                  onChange={(e) => setNewTransaction({...newTransaction, amount: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="transaction-description">الوصف</Label>
                <Textarea
                  id="transaction-description"
                  placeholder="أدخل وصف المعاملة"
                  value={newTransaction.description}
                  onChange={(e) => setNewTransaction({...newTransaction, description: e.target.value})}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddTransactionOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleAddTransaction}>
                إضافة المعاملة
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Credit Limit Dialog */}
        <Dialog open={isEditCreditLimitOpen} onOpenChange={setIsEditCreditLimitOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>تعديل حد الائتمان</DialogTitle>
              <DialogDescription>
                تعديل حد الائتمان للعميل: {selectedCustomer?.customerName}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label htmlFor="current-limit">حد الائتمان الحالي</Label>
                <Input
                  id="current-limit"
                  value={selectedCustomer?.creditLimit.toLocaleString() + ' ر.ي'}
                  disabled
                />
              </div>

              <div>
                <Label htmlFor="new-limit">حد الائتمان الجديد (ر.ي)</Label>
                <Input
                  id="new-limit"
                  type="number"
                  placeholder="أدخل حد الائتمان الجديد"
                  value={newCreditLimit}
                  onChange={(e) => setNewCreditLimit(e.target.value)}
                />
              </div>

              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <AlertTriangle className="h-4 w-4 inline mr-1" />
                  تأكد من صحة المبلغ قبل التأكيد. هذا الإجراء سيؤثر على حالة العميل.
                </p>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditCreditLimitOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleUpdateCreditLimit}>
                تحديث حد الائتمان
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AuthenticatedLayout>
  );
}
