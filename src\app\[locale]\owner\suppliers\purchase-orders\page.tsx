"use client";

import { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  ShoppingCart,
  Calendar,
  DollarSign,
  Package,
  Truck,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import { PurchaseOrder } from '@/types';
import { getPurchaseOrders } from '@/lib/mock-supplier-data';

export default function PurchaseOrdersPage() {
  const t = useScopedI18n('ownerSupplierManagement');
  const { user } = useAuth();
  const { toast } = useToast();

  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    loadPurchaseOrders();
  }, []);

  const loadPurchaseOrders = async () => {
    setIsLoading(true);
    try {
      const ordersData = await getPurchaseOrders();
      setPurchaseOrders(ordersData);
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في تحميل أوامر الشراء',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filteredOrders = purchaseOrders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.supplierName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: t('statusDraft'), variant: 'secondary' as const, icon: Clock },
      sent: { label: t('statusSent'), variant: 'outline' as const, icon: Package },
      confirmed: { label: t('statusConfirmed'), variant: 'default' as const, icon: CheckCircle },
      partially_received: { label: t('statusPartiallyReceived'), variant: 'outline' as const, icon: Truck },
      completed: { label: t('statusCompleted'), variant: 'default' as const, icon: CheckCircle },
      cancelled: { label: t('statusCancelled'), variant: 'destructive' as const, icon: XCircle }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config?.icon || Clock;
    
    return (
      <Badge variant={config?.variant} className="flex items-center space-x-1">
        <Icon className="h-3 w-3" />
        <span>{config?.label}</span>
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: t('priorityLow'), variant: 'secondary' as const },
      medium: { label: t('priorityMedium'), variant: 'outline' as const },
      high: { label: t('priorityHigh'), variant: 'default' as const },
      urgent: { label: t('priorityUrgent'), variant: 'destructive' as const }
    };
    
    const config = priorityConfig[priority as keyof typeof priorityConfig];
    return <Badge variant={config?.variant}>{config?.label}</Badge>;
  };

  const getPaymentStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: t('paymentPending'), variant: 'outline' as const },
      partial: { label: t('paymentPartial'), variant: 'secondary' as const },
      paid: { label: t('paymentPaid'), variant: 'default' as const },
      overdue: { label: t('paymentOverdue'), variant: 'destructive' as const }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig];
    return <Badge variant={config?.variant}>{config?.label}</Badge>;
  };

  const stats = {
    totalOrders: purchaseOrders.length,
    draftOrders: purchaseOrders.filter(o => o.status === 'draft').length,
    pendingOrders: purchaseOrders.filter(o => o.status === 'sent' || o.status === 'confirmed').length,
    completedOrders: purchaseOrders.filter(o => o.status === 'completed').length,
    totalValue: purchaseOrders.reduce((sum, o) => sum + o.totalAmount, 0),
    pendingPayments: purchaseOrders.filter(o => o.paymentStatus === 'pending' || o.paymentStatus === 'overdue').length
  };

  if (isLoading) {
    return (
      <AuthenticatedLayout expectedRole="owner">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">{t('loadingPurchaseOrders')}</p>
          </div>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        {/* Header */}
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <ShoppingCart className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('purchaseOrders')}</h1>
            <p className="text-muted-foreground">
              إدارة أوامر الشراء وتتبع حالة الطلبات
            </p>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('totalPurchaseOrders')}</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalOrders}</div>
              <p className="text-xs text-muted-foreground">
                {stats.pendingOrders} {t('pendingOrders')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المسودات</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.draftOrders}</div>
              <p className="text-xs text-muted-foreground">
                في انتظار الإرسال
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">مكتملة</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.completedOrders}</div>
              <p className="text-xs text-muted-foreground">
                تم الاستلام بالكامل
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">القيمة الإجمالية</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.totalValue.toLocaleString()} ريال
              </div>
              <p className="text-xs text-muted-foreground">
                {stats.pendingPayments} دفعات معلقة
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="البحث في أوامر الشراء..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center space-x-2">
                  <Filter className="h-4 w-4" />
                  <span>{t('filterByStatus')}</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                  جميع الحالات
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('draft')}>
                  {t('statusDraft')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('sent')}>
                  {t('statusSent')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('confirmed')}>
                  {t('statusConfirmed')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('completed')}>
                  {t('statusCompleted')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button className="flex items-center space-x-2">
              <Plus className="h-4 w-4" />
              <span>إنشاء أمر شراء جديد</span>
            </Button>
          </div>
        </div>

        {/* Purchase Orders Table */}
        <Card>
          <CardHeader>
            <CardTitle>أوامر الشراء</CardTitle>
            <CardDescription>
              قائمة بجميع أوامر الشراء وحالتها الحالية
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredOrders.length === 0 ? (
              <div className="text-center py-8">
                <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">{t('noPurchaseOrdersFound')}</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('purchaseOrderNumber')}</TableHead>
                    <TableHead>{t('supplierName')}</TableHead>
                    <TableHead>{t('orderDate')}</TableHead>
                    <TableHead>{t('totalAmount')}</TableHead>
                    <TableHead>{t('priority')}</TableHead>
                    <TableHead>{t('orderStatus')}</TableHead>
                    <TableHead>{t('paymentStatus')}</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>
                        <div className="font-medium">{order.orderNumber}</div>
                        <div className="text-sm text-muted-foreground">
                          {order.items.length} منتج
                        </div>
                      </TableCell>
                      <TableCell>{order.supplierName}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{new Date(order.orderDate).toLocaleDateString('ar-YE')}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {order.totalAmount.toLocaleString()} ريال
                        </div>
                      </TableCell>
                      <TableCell>{getPriorityBadge(order.priority)}</TableCell>
                      <TableCell>{getStatusBadge(order.status)}</TableCell>
                      <TableCell>{getPaymentStatusBadge(order.paymentStatus)}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                            <DropdownMenuItem>
                              عرض التفاصيل
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              تعديل الطلب
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              تتبع التسليم
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              طباعة الطلب
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              إرسال للمورد
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}
