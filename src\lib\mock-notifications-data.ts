// src/lib/mock-notifications-data.ts
import type { Notification, NotificationPreferences } from '@/types';

const NOTIFICATIONS_STORAGE_KEY = 'marketSyncEmployeeNotifications';
const NOTIFICATION_PREFERENCES_KEY = 'marketSyncNotificationPreferences';

// Mock notification data
export const initialMockNotifications: Omit<Notification, 'id'>[] = [
  {
    title: "New High Priority Order",
    message: "Order #ORD-2024-001 requires immediate processing. Customer: <PERSON>",
    type: "order",
    priority: "high",
    targetRole: "employee",
    senderId: "system",
    senderName: "System",
    isRead: false,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    actionUrl: "/employee/approve-orders?orderId=ORD-2024-001",
    actionLabel: "Process Order",
    metadata: {
      orderId: "ORD-2024-001",
      customerId: "customer001",
      amount: 250.75
    }
  },
  {
    title: "Low Stock Alert",
    message: "Product 'Fresh Milk 1L' is running low (5 units remaining). Consider restocking.",
    type: "warning",
    priority: "medium",
    targetRole: "employee",
    senderId: "system",
    senderName: "Inventory System",
    isRead: false,
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
    actionUrl: "/employee/manage-products?productId=PROD-001",
    actionLabel: "View Product",
    metadata: {
      productId: "PROD-001",
      currentStock: 5,
      minThreshold: 10
    }
  },
  {
    title: "System Maintenance Scheduled",
    message: "System maintenance is scheduled for tonight at 2:00 AM. Expected downtime: 30 minutes.",
    type: "system",
    priority: "medium",
    targetRole: "all",
    senderId: "admin001",
    senderName: "System Administrator",
    isRead: true,
    createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
    readAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
    expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(), // Expires in 18 hours
  },
  {
    title: "Customer Return Request",
    message: "Customer Fatima Hassan has requested a return for Order #ORD-2024-002. Reason: Damaged product.",
    type: "info",
    priority: "medium",
    targetRole: "employee",
    senderId: "system",
    senderName: "Returns System",
    isRead: true,
    createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago
    readAt: new Date(Date.now() - 7 * 60 * 60 * 1000).toISOString(),
    actionUrl: "/employee/returns-management?returnId=RET-001",
    actionLabel: "Process Return",
    metadata: {
      orderId: "ORD-2024-002",
      customerId: "customer002",
      returnId: "RET-001"
    }
  },
  {
    title: "Payment Received",
    message: "Payment of $150.00 received for Order #ORD-2024-003 from customer Omar Khalil.",
    type: "success",
    priority: "low",
    targetRole: "employee",
    senderId: "system",
    senderName: "Payment System",
    isRead: true,
    createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago
    readAt: new Date(Date.now() - 11 * 60 * 60 * 1000).toISOString(),
    metadata: {
      orderId: "ORD-2024-003",
      customerId: "customer003",
      amount: 150.00
    }
  },
  {
    title: "New Task Assigned",
    message: "You have been assigned a new task: 'Update product prices for dairy section'",
    type: "task",
    priority: "medium",
    targetUserId: "employee001",
    senderId: "owner001",
    senderName: "Store Manager",
    isRead: false,
    createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
    actionUrl: "/employee/tasks?taskId=TASK-001",
    actionLabel: "View Task",
    metadata: {
      taskId: "TASK-001",
      category: "pricing"
    }
  },
  {
    title: "Shift Reminder",
    message: "Your shift starts in 30 minutes. Don't forget to clock in!",
    type: "info",
    priority: "medium",
    targetUserId: "employee001",
    senderId: "system",
    senderName: "Shift Management",
    isRead: false,
    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
    actionUrl: "/employee/shifts",
    actionLabel: "View Schedule",
    metadata: {
      shiftId: "SHIFT-001",
      startTime: new Date(Date.now() + 30 * 60 * 1000).toISOString()
    }
  }
];

// Default notification preferences
export const defaultNotificationPreferences: NotificationPreferences = {
  userId: '',
  emailNotifications: true,
  pushNotifications: true,
  inAppNotifications: true,
  notificationTypes: {
    orders: true,
    tasks: true,
    system: true,
    alerts: true,
    lowStock: true,
    newCustomers: true,
  }
};

// Storage functions
export const getStoredNotifications = (userId?: string): Notification[] => {
  if (typeof window === 'undefined') return [];
  
  const stored = localStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
  let allNotifications: Notification[] = [];
  
  if (stored) {
    allNotifications = JSON.parse(stored);
  } else {
    // Initialize with mock data
    allNotifications = initialMockNotifications.map((notification, index) => ({
      ...notification,
      id: `notif_${Date.now()}_${index}`,
    }));
    localStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(allNotifications));
  }
  
  // Filter notifications for specific user or role
  if (userId) {
    return allNotifications.filter(notification => 
      notification.targetUserId === userId || 
      notification.targetRole === 'employee' || 
      notification.targetRole === 'all'
    );
  }
  
  return allNotifications;
};

export const saveNotifications = (notifications: Notification[]): void => {
  if (typeof window === 'undefined') return;
  localStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(notifications));
};

export const markNotificationAsRead = (notificationId: string, userId?: string): void => {
  const notifications = getStoredNotifications();
  const updatedNotifications = notifications.map(notification => 
    notification.id === notificationId 
      ? { ...notification, isRead: true, readAt: new Date().toISOString() }
      : notification
  );
  saveNotifications(updatedNotifications);
};

export const markAllNotificationsAsRead = (userId?: string): void => {
  const notifications = getStoredNotifications();
  const updatedNotifications = notifications.map(notification => {
    const isTargeted = userId ? 
      (notification.targetUserId === userId || 
       notification.targetRole === 'employee' || 
       notification.targetRole === 'all') : true;
    
    return isTargeted && !notification.isRead
      ? { ...notification, isRead: true, readAt: new Date().toISOString() }
      : notification;
  });
  saveNotifications(updatedNotifications);
};

export const deleteNotification = (notificationId: string): void => {
  const notifications = getStoredNotifications();
  const updatedNotifications = notifications.filter(notification => notification.id !== notificationId);
  saveNotifications(updatedNotifications);
};

export const getUnreadNotificationsCount = (userId?: string): number => {
  const notifications = getStoredNotifications(userId);
  return notifications.filter(notification => !notification.isRead).length;
};

export const getNotificationPreferences = (userId: string): NotificationPreferences => {
  if (typeof window === 'undefined') return { ...defaultNotificationPreferences, userId };
  
  const stored = localStorage.getItem(`${NOTIFICATION_PREFERENCES_KEY}_${userId}`);
  if (stored) {
    return JSON.parse(stored);
  }
  
  return { ...defaultNotificationPreferences, userId };
};

export const saveNotificationPreferences = (preferences: NotificationPreferences): void => {
  if (typeof window === 'undefined') return;
  localStorage.setItem(`${NOTIFICATION_PREFERENCES_KEY}_${preferences.userId}`, JSON.stringify(preferences));
};

// Helper function to add a new notification (for testing or admin use)
export const addNotification = (notification: Omit<Notification, 'id'>): void => {
  const notifications = getStoredNotifications();
  const newNotification: Notification = {
    ...notification,
    id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  };
  notifications.unshift(newNotification); // Add to beginning
  saveNotifications(notifications);
};
