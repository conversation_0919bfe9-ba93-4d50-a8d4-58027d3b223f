"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  Calendar, 
  Settings, 
  Save, 
  AlertCircle,
  CheckCircle,
  Trash2,
  Plus
} from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';

interface BackupSchedule {
  id: string;
  name: string;
  frequency: 'daily' | 'weekly' | 'monthly';
  time: string; // HH:MM format
  dayOfWeek?: number; // 0-6 for weekly
  dayOfMonth?: number; // 1-31 for monthly
  enabled: boolean;
  lastRun?: string;
  nextRun?: string;
  dataTypes: string[];
}

const BACKUP_SCHEDULES_KEY = 'marketSyncBackupSchedules';

const defaultSchedules: BackupSchedule[] = [
  {
    id: 'daily-products',
    name: 'Daily Products Backup',
    frequency: 'daily',
    time: '02:00',
    enabled: true,
    dataTypes: ['products', 'inventory'],
    lastRun: '2024-01-20T02:00:00Z',
    nextRun: '2024-01-21T02:00:00Z'
  },
  {
    id: 'weekly-full',
    name: 'Weekly Full Backup',
    frequency: 'weekly',
    time: '01:00',
    dayOfWeek: 0, // Sunday
    enabled: true,
    dataTypes: ['products', 'users', 'orders', 'debts', 'settings'],
    lastRun: '2024-01-14T01:00:00Z',
    nextRun: '2024-01-21T01:00:00Z'
  }
];

export function BackupScheduler() {
  const t = useScopedI18n('backupManagement');
  const tCommon = useScopedI18n('common');
  const { toast } = useToast();

  const [schedules, setSchedules] = useState<BackupSchedule[]>(defaultSchedules);
  const [isEditing, setIsEditing] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<BackupSchedule | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(BACKUP_SCHEDULES_KEY);
      if (stored) {
        try {
          setSchedules(JSON.parse(stored));
        } catch (error) {
          console.error('Error loading backup schedules:', error);
        }
      }
    }
  }, []);

  const saveSchedules = (newSchedules: BackupSchedule[]) => {
    setSchedules(newSchedules);
    if (typeof window !== 'undefined') {
      localStorage.setItem(BACKUP_SCHEDULES_KEY, JSON.stringify(newSchedules));
    }
  };

  const handleToggleSchedule = (id: string) => {
    const newSchedules = schedules.map(schedule =>
      schedule.id === id ? { ...schedule, enabled: !schedule.enabled } : schedule
    );
    saveSchedules(newSchedules);
    
    const schedule = newSchedules.find(s => s.id === id);
    toast({
      title: t('scheduleUpdated'),
      description: schedule?.enabled ? t('scheduleEnabled') : t('scheduleDisabled'),
    });
  };

  const handleDeleteSchedule = (id: string) => {
    if (!window.confirm(t('confirmDeleteSchedule'))) return;
    
    const newSchedules = schedules.filter(schedule => schedule.id !== id);
    saveSchedules(newSchedules);
    
    toast({
      title: t('scheduleDeleted'),
      description: t('scheduleDeletedSuccess'),
    });
  };

  const handleAddSchedule = () => {
    const newSchedule: BackupSchedule = {
      id: `schedule-${Date.now()}`,
      name: 'New Backup Schedule',
      frequency: 'daily',
      time: '03:00',
      enabled: false,
      dataTypes: ['products']
    };
    setEditingSchedule(newSchedule);
    setIsEditing(true);
  };

  const handleEditSchedule = (schedule: BackupSchedule) => {
    setEditingSchedule({ ...schedule });
    setIsEditing(true);
  };

  const handleSaveSchedule = () => {
    if (!editingSchedule) return;

    const isNew = !schedules.find(s => s.id === editingSchedule.id);
    let newSchedules;

    if (isNew) {
      newSchedules = [...schedules, editingSchedule];
    } else {
      newSchedules = schedules.map(s => 
        s.id === editingSchedule.id ? editingSchedule : s
      );
    }

    saveSchedules(newSchedules);
    setIsEditing(false);
    setEditingSchedule(null);

    toast({
      title: t('scheduleSaved'),
      description: isNew ? t('scheduleCreated') : t('scheduleUpdated'),
    });
  };

  const getFrequencyBadge = (frequency: string) => {
    const variants = {
      daily: 'default',
      weekly: 'secondary',
      monthly: 'outline'
    } as const;
    
    return (
      <Badge variant={variants[frequency as keyof typeof variants]}>
        {t(`frequency_${frequency}`)}
      </Badge>
    );
  };

  const getStatusBadge = (enabled: boolean) => {
    return enabled ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        <CheckCircle className="h-3 w-3 mr-1" />
        {t('active')}
      </Badge>
    ) : (
      <Badge variant="secondary">
        <AlertCircle className="h-3 w-3 mr-1" />
        {t('inactive')}
      </Badge>
    );
  };

  const formatNextRun = (nextRun?: string) => {
    if (!nextRun) return t('notScheduled');
    
    const date = new Date(nextRun);
    const now = new Date();
    const diffHours = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60));
    
    if (diffHours < 24) {
      return t('inHours', { hours: diffHours.toString() });
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                {t('backupSchedules')}
              </CardTitle>
              <CardDescription>
                {t('automateBackupProcess')}
              </CardDescription>
            </div>
            <Button onClick={handleAddSchedule} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              {t('addSchedule')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {schedules.map((schedule) => (
              <div key={schedule.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h4 className="font-medium">{schedule.name}</h4>
                    {getFrequencyBadge(schedule.frequency)}
                    {getStatusBadge(schedule.enabled)}
                  </div>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div className="flex items-center gap-4">
                      <span>
                        <Clock className="h-3 w-3 inline mr-1" />
                        {t('time')}: {schedule.time}
                      </span>
                      {schedule.frequency === 'weekly' && schedule.dayOfWeek !== undefined && (
                        <span>
                          <Calendar className="h-3 w-3 inline mr-1" />
                          {t('dayOfWeek')}: {t(`weekday_${schedule.dayOfWeek}`)}
                        </span>
                      )}
                      {schedule.frequency === 'monthly' && schedule.dayOfMonth && (
                        <span>
                          <Calendar className="h-3 w-3 inline mr-1" />
                          {t('dayOfMonth')}: {schedule.dayOfMonth}
                        </span>
                      )}
                    </div>
                    <div>
                      {t('dataTypes')}: {schedule.dataTypes.join(', ')}
                    </div>
                    <div>
                      {t('nextRun')}: {formatNextRun(schedule.nextRun)}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={schedule.enabled}
                    onCheckedChange={() => handleToggleSchedule(schedule.id)}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditSchedule(schedule)}
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteSchedule(schedule.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
            
            {schedules.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>{t('noSchedulesConfigured')}</p>
                <Button onClick={handleAddSchedule} className="mt-4">
                  {t('createFirstSchedule')}
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Edit Schedule Dialog would go here - simplified for now */}
      {isEditing && editingSchedule && (
        <Card>
          <CardHeader>
            <CardTitle>{t('editSchedule')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="schedule-name">{t('scheduleName')}</Label>
              <Input
                id="schedule-name"
                value={editingSchedule.name}
                onChange={(e) => setEditingSchedule({
                  ...editingSchedule,
                  name: e.target.value
                })}
              />
            </div>
            
            <div>
              <Label htmlFor="frequency">{t('frequency')}</Label>
              <Select
                value={editingSchedule.frequency}
                onValueChange={(value: 'daily' | 'weekly' | 'monthly') =>
                  setEditingSchedule({ ...editingSchedule, frequency: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">{t('frequency_daily')}</SelectItem>
                  <SelectItem value="weekly">{t('frequency_weekly')}</SelectItem>
                  <SelectItem value="monthly">{t('frequency_monthly')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="time">{t('time')}</Label>
              <Input
                id="time"
                type="time"
                value={editingSchedule.time}
                onChange={(e) => setEditingSchedule({
                  ...editingSchedule,
                  time: e.target.value
                })}
              />
            </div>

            <div className="flex gap-2">
              <Button onClick={handleSaveSchedule}>
                <Save className="h-4 w-4 mr-2" />
                {tCommon('save')}
              </Button>
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                {tCommon('cancel')}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
