
"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Settings as SettingsIcon, Store, Bell, Palette, Save, Phone, Mail, MapPin, DollarSign, Puzzle } from 'lucide-react';
import { useTheme } from 'next-themes';
import React from 'react'; 
import { useScopedI18n } from '@/lib/i18n/client';

export default function OwnerSettingsPage() {
  const { theme, setTheme } = useTheme();
  const t = useScopedI18n('ownerSettings');
  const tCommon = useScopedI18n('common');

  const [storeName, setStoreName] = React.useState("My Supermarket");
  const [storeAddress, setStoreAddress] = React.useState("123 Market St, Anytown");
  const [contactEmail, setContactEmail] = React.useState("<EMAIL>");
  const [contactPhone, setContactPhone] = React.useState("555-0101");
  const [deliveryFee, setDeliveryFee] = React.useState("500"); // YER example
  const [minOrderValue, setMinOrderValue] = React.useState("2000"); // YER example
  const [openingHours, setOpeningHours] = React.useState("Mon-Fri: 9 AM - 9 PM, Sat-Sun: 10 AM - 7 PM");


  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <SettingsIcon className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <div className="flex items-center space-x-2">
                <Store className="h-6 w-6 text-primary" />
                <CardTitle>{t('storeInformation')}</CardTitle>
            </div>
            <CardDescription>
              {t('storeInfoDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="storeName">{t('storeName')}</Label>
              <Input id="storeName" value={storeName} onChange={(e) => setStoreName(e.target.value)} placeholder={t('storeNamePlaceholder')} />
            </div>
            <div>
              <Label htmlFor="storeAddress">{t('storeAddress')}</Label>
              <div className="relative">
                <MapPin className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground rtl:right-2.5 rtl:left-auto" />
                <Input id="storeAddress" value={storeAddress} onChange={(e) => setStoreAddress(e.target.value)} placeholder={t('storeAddressPlaceholder')} className="pl-8 rtl:pr-8 rtl:pl-3" />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <Label htmlFor="contactEmail">{t('contactEmail')}</Label>
                    <div className="relative">
                        <Mail className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground rtl:right-2.5 rtl:left-auto" />
                        <Input id="contactEmail" type="email" value={contactEmail} onChange={(e) => setContactEmail(e.target.value)} placeholder={t('contactEmailPlaceholder')} className="pl-8 rtl:pr-8 rtl:pl-3" />
                    </div>
                </div>
                <div>
                    <Label htmlFor="contactPhone">{t('contactPhone')}</Label>
                     <div className="relative">
                        <Phone className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground rtl:right-2.5 rtl:left-auto" />
                        <Input id="contactPhone" type="tel" value={contactPhone} onChange={(e) => setContactPhone(e.target.value)} placeholder={t('contactPhonePlaceholder')} className="pl-8 rtl:pr-8 rtl:pl-3" />
                    </div>
                </div>
            </div>
            <div>
                <Label htmlFor="storeLogo">{t('storeLogo')}</Label>
                <Input id="storeLogo" type="file" className="file:text-primary file:font-medium file:mr-4 rtl:file:ml-4 rtl:file:mr-0"/>
                <p className="text-xs text-muted-foreground mt-1">{t('storeLogoHelp')}</p>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <div className="flex items-center space-x-2">
                <SettingsIcon className="h-6 w-6 text-primary" />
                <CardTitle>{t('operationalSettings')}</CardTitle>
            </div>
            <CardDescription>{t('operationalSettingsDescription')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <Label htmlFor="deliveryFee">{t('defaultDeliveryFee')} (YER)</Label>
                    <div className="relative">
                        <DollarSign className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground rtl:right-2.5 rtl:left-auto" />
                        <Input id="deliveryFee" type="number" value={deliveryFee} onChange={(e) => setDeliveryFee(e.target.value)} placeholder="0" className="pl-8 rtl:pr-8 rtl:pl-3" />
                    </div>
                </div>
                <div>
                    <Label htmlFor="minOrderValue">{t('minOrderValueForDelivery')} (YER)</Label>
                     <div className="relative">
                        <DollarSign className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground rtl:right-2.5 rtl:left-auto" />
                        <Input id="minOrderValue" type="number" value={minOrderValue} onChange={(e) => setMinOrderValue(e.target.value)} placeholder="0" className="pl-8 rtl:pr-8 rtl:pl-3" />
                    </div>
                </div>
            </div>
             <div>
                <Label htmlFor="openingHours">{t('openingHours')}</Label>
                <Input id="openingHours" value={openingHours} onChange={(e)=> setOpeningHours(e.target.value)} placeholder={t('openingHoursPlaceholder')} />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
             <div className="flex items-center space-x-2">
                <Palette className="h-6 w-6 text-primary" />
                <CardTitle>{t('appearanceSettings')}</CardTitle>
            </div>
            <CardDescription>{t('appearanceSettingsDescription')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="theme" className="text-base">{t('theme')}</Label>
                <p className="text-sm text-muted-foreground">{t('themeDescription')}</p>
              </div>
              <Select value={theme} onValueChange={(value) => setTheme(value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder={t('selectTheme')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">{t('themeLight')}</SelectItem>
                  <SelectItem value="dark">{t('themeDark')}</SelectItem>
                  <SelectItem value="system">{t('themeSystem')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <div className="flex items-center space-x-2">
                <Bell className="h-6 w-6 text-primary" />
                <CardTitle>{t('notificationSettings')}</CardTitle>
            </div>
            <CardDescription>{t('notificationSettingsDescription')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailNotifications" className="font-medium">{t('emailNotifications')}</Label>
                <p className="text-sm text-muted-foreground">{t('emailNotificationsHelp')}</p>
              </div>
              <Switch id="emailNotifications" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="newOrderNotifications" className="font-medium">{t('newOrderNotifications')}</Label>
                <p className="text-sm text-muted-foreground">{t('newOrderNotificationsHelp')}</p>
              </div>
              <Switch id="newOrderNotifications" defaultChecked />
            </div>
             <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="lowStockAlerts" className="font-medium">{t('lowStockAlerts')}</Label>
                <p className="text-sm text-muted-foreground">{t('lowStockAlertsHelp')}</p>
              </div>
              <Switch id="lowStockAlerts" defaultChecked/>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <div className="flex items-center space-x-2">
                <Puzzle className="h-6 w-6 text-primary" />
                <CardTitle>{t('integrationsTitle')}</CardTitle>
            </div>
            <CardDescription>{t('integrationsDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-center py-6">
              {t('integrationsComingSoon')}
            </p>
          </CardContent>
        </Card>
        
        <div className="flex justify-end mt-6">
            <Button className="bg-accent hover:bg-accent/90 text-accent-foreground">
                <Save className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('saveAllSettings')}
            </Button>
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
