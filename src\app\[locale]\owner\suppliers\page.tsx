
"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { SupplierManagement } from '@/components/owner/supplier-management';
import { Factory } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';

export default function OwnerSupplierManagementPage() {
  const tSpecific = useScopedI18n('ownerSupplierManagement');

  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Factory className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{tSpecific('title')}</h1>
            <p className="text-muted-foreground">
              {tSpecific('description')}
            </p>
          </div>
        </div>

        <SupplierManagement />
      </div>
    </AuthenticatedLayout>
  );
}

    