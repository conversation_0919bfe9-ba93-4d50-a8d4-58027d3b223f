// src/lib/mock-analytics-data.ts
import type { 
  Customer, 
  CustomerSegment, 
  FinancialTransaction, 
  FinancialSummary, 
  ExpenseCategory,
  SalesAnalytics,
  InventoryAnalytics,
  CustomerAnalytics
} from '@/types';

const MOCK_CUSTOMERS_KEY = 'marketSyncMockCustomers';
const MOCK_FINANCIAL_TRANSACTIONS_KEY = 'marketSyncMockFinancialTransactions';
const MOCK_EXPENSE_CATEGORIES_KEY = 'marketSyncMockExpenseCategories';

// Mock Customers Data
const mockCustomers: Customer[] = [
  {
    id: 'customer001',
    name: 'أحمد محمد علي',
    email: '<EMAIL>',
    phone: '770123456',
    address: 'شارع الزبيري، صنعاء',
    dateJoined: '2023-06-15',
    totalOrders: 45,
    totalSpent: 125000,
    averageOrderValue: 2778,
    lastOrderDate: '2024-01-20',
    status: 'active',
    loyaltyPoints: 1250,
    preferredCategories: ['منتجات غذائية', 'مشروبات'],
    notes: 'عميل مميز، يفضل المنتجات العضوية',
    creditLimit: 20000,
    currentBalance: 13000,
    ownerId: 'owner001'
  },
  {
    id: 'customer002',
    name: 'فاطمة حسن',
    email: '<EMAIL>',
    phone: '770234567',
    address: 'شارع الستين، صنعاء',
    dateJoined: '2023-08-20',
    totalOrders: 32,
    totalSpent: 89000,
    averageOrderValue: 2781,
    lastOrderDate: '2024-01-18',
    status: 'active',
    loyaltyPoints: 890,
    preferredCategories: ['منتجات تنظيف', 'مستحضرات تجميل'],
    notes: 'تفضل التسوق في المساء',
    creditLimit: 15000,
    currentBalance: 5000,
    ownerId: 'owner001'
  },
  {
    id: 'customer003',
    name: 'محمد عبدالله',
    email: '<EMAIL>',
    phone: '770345678',
    address: 'شارع الحصبة، صنعاء',
    dateJoined: '2023-12-10',
    totalOrders: 18,
    totalSpent: 45000,
    averageOrderValue: 2500,
    lastOrderDate: '2024-01-15',
    status: 'active',
    loyaltyPoints: 450,
    preferredCategories: ['إلكترونيات', 'أدوات منزلية'],
    creditLimit: 10000,
    currentBalance: 2000,
    ownerId: 'owner001'
  },
  {
    id: 'customer004',
    name: 'سارة أحمد',
    email: '<EMAIL>',
    phone: '770456789',
    address: 'شارع الثورة، صنعاء',
    dateJoined: '2023-09-05',
    totalOrders: 28,
    totalSpent: 67000,
    averageOrderValue: 2393,
    lastOrderDate: '2024-01-12',
    status: 'active',
    loyaltyPoints: 670,
    preferredCategories: ['ملابس', 'إكسسوارات'],
    ownerId: 'owner001'
  },
  {
    id: 'customer005',
    name: 'علي حسين',
    email: '<EMAIL>',
    phone: '770567890',
    address: 'شارع الجمهورية، صنعاء',
    dateJoined: '2023-11-20',
    totalOrders: 12,
    totalSpent: 28000,
    averageOrderValue: 2333,
    lastOrderDate: '2023-12-25',
    status: 'inactive',
    loyaltyPoints: 280,
    preferredCategories: ['كتب', 'قرطاسية'],
    notes: 'لم يتسوق منذ شهر',
    ownerId: 'owner001'
  }
];

// Mock Customer Segments
const mockCustomerSegments: CustomerSegment[] = [
  {
    id: 'segment001',
    name: 'العملاء المميزون',
    description: 'العملاء الذين أنفقوا أكثر من 100,000 ريال',
    criteria: { minTotalSpent: 100000 },
    customerCount: 2,
    ownerId: 'owner001'
  },
  {
    id: 'segment002',
    name: 'العملاء النشطون',
    description: 'العملاء الذين لديهم أكثر من 20 طلب',
    criteria: { minOrders: 20 },
    customerCount: 4,
    ownerId: 'owner001'
  },
  {
    id: 'segment003',
    name: 'العملاء الجدد',
    description: 'العملاء الذين انضموا في آخر 3 أشهر',
    criteria: { lastOrderDays: 90 },
    customerCount: 2,
    ownerId: 'owner001'
  }
];

// Mock Financial Transactions
const mockFinancialTransactions: FinancialTransaction[] = [
  {
    id: 'fin001',
    type: 'income',
    category: 'مبيعات',
    amount: 15000,
    description: 'مبيعات يوم 20 يناير',
    date: '2024-01-20',
    paymentMethod: 'cash',
    reference: 'ORD12345',
    employeeId: 'employee001',
    customerId: 'customer001',
    ownerId: 'owner001',
    status: 'completed'
  },
  {
    id: 'fin002',
    type: 'expense',
    category: 'مشتريات',
    amount: 8000,
    description: 'شراء بضائع من المورد',
    date: '2024-01-19',
    paymentMethod: 'bank_transfer',
    reference: 'PO001',
    supplierId: 'supplier001',
    ownerId: 'owner001',
    status: 'completed'
  },
  {
    id: 'fin003',
    type: 'expense',
    category: 'رواتب',
    amount: 25000,
    description: 'رواتب الموظفين - يناير',
    date: '2024-01-31',
    paymentMethod: 'bank_transfer',
    ownerId: 'owner001',
    status: 'completed'
  },
  {
    id: 'fin004',
    type: 'expense',
    category: 'إيجار',
    amount: 12000,
    description: 'إيجار المحل - يناير',
    date: '2024-01-01',
    paymentMethod: 'cash',
    ownerId: 'owner001',
    status: 'completed'
  },
  {
    id: 'fin005',
    type: 'income',
    category: 'مبيعات',
    amount: 22000,
    description: 'مبيعات يوم 21 يناير',
    date: '2024-01-21',
    paymentMethod: 'card',
    ownerId: 'owner001',
    status: 'completed'
  }
];

// Mock Expense Categories
const mockExpenseCategories: ExpenseCategory[] = [
  {
    id: 'exp001',
    name: 'مشتريات',
    description: 'شراء البضائع والمنتجات',
    budgetLimit: 50000,
    currentSpent: 35000,
    ownerId: 'owner001'
  },
  {
    id: 'exp002',
    name: 'رواتب',
    description: 'رواتب الموظفين',
    budgetLimit: 30000,
    currentSpent: 25000,
    ownerId: 'owner001'
  },
  {
    id: 'exp003',
    name: 'إيجار',
    description: 'إيجار المحل والمخازن',
    budgetLimit: 15000,
    currentSpent: 12000,
    ownerId: 'owner001'
  },
  {
    id: 'exp004',
    name: 'مرافق',
    description: 'كهرباء، ماء، إنترنت',
    budgetLimit: 5000,
    currentSpent: 3200,
    ownerId: 'owner001'
  }
];

// Mock Analytics Data
const mockSalesAnalytics: SalesAnalytics = {
  period: '2024-01',
  totalSales: 450000,
  totalOrders: 180,
  averageOrderValue: 2500,
  topProducts: [
    { productId: 'prod001', productName: 'حليب طازج', quantity: 120, revenue: 96000 },
    { productId: 'prod002', productName: 'خبز أبيض', quantity: 200, revenue: 40000 },
    { productId: 'prod003', productName: 'عصير برتقال', quantity: 80, revenue: 120000 }
  ],
  salesByCategory: [
    { category: 'منتجات غذائية', amount: 200000, percentage: 44.4 },
    { category: 'مشروبات', amount: 150000, percentage: 33.3 },
    { category: 'منتجات تنظيف', amount: 100000, percentage: 22.2 }
  ],
  salesByHour: [
    { hour: 8, sales: 15000 }, { hour: 9, sales: 25000 }, { hour: 10, sales: 35000 },
    { hour: 11, sales: 45000 }, { hour: 12, sales: 40000 }, { hour: 13, sales: 30000 },
    { hour: 14, sales: 35000 }, { hour: 15, sales: 50000 }, { hour: 16, sales: 45000 },
    { hour: 17, sales: 55000 }, { hour: 18, sales: 60000 }, { hour: 19, sales: 55000 },
    { hour: 20, sales: 40000 }, { hour: 21, sales: 15000 }
  ],
  salesByDay: [
    { day: 'الأحد', sales: 65000 }, { day: 'الاثنين', sales: 70000 },
    { day: 'الثلاثاء', sales: 60000 }, { day: 'الأربعاء', sales: 75000 },
    { day: 'الخميس', sales: 80000 }, { day: 'الجمعة', sales: 55000 },
    { day: 'السبت', sales: 45000 }
  ],
  customerMetrics: {
    newCustomers: 15,
    returningCustomers: 85,
    customerRetentionRate: 85
  }
};

const mockInventoryAnalytics: InventoryAnalytics = {
  totalProducts: 150,
  lowStockItems: 12,
  outOfStockItems: 3,
  totalInventoryValue: 250000,
  fastMovingItems: [
    { productId: 'prod001', productName: 'حليب طازج', turnoverRate: 15 },
    { productId: 'prod002', productName: 'خبز أبيض', turnoverRate: 20 },
    { productId: 'prod003', productName: 'عصير برتقال', turnoverRate: 12 }
  ],
  slowMovingItems: [
    { productId: 'prod010', productName: 'منتج بطيء الحركة', daysInStock: 45 },
    { productId: 'prod011', productName: 'منتج آخر بطيء', daysInStock: 60 }
  ],
  categoryPerformance: [
    { category: 'منتجات غذائية', revenue: 200000, margin: 25 },
    { category: 'مشروبات', revenue: 150000, margin: 30 },
    { category: 'منتجات تنظيف', revenue: 100000, margin: 20 }
  ]
};

const mockCustomerAnalytics: CustomerAnalytics = {
  totalCustomers: 150,
  activeCustomers: 120,
  newCustomersThisMonth: 15,
  customerLifetimeValue: 85000,
  topCustomers: [
    { customerId: 'customer001', customerName: 'أحمد محمد علي', totalSpent: 125000 },
    { customerId: 'customer002', customerName: 'فاطمة حسن', totalSpent: 89000 },
    { customerId: 'customer004', customerName: 'سارة أحمد', totalSpent: 67000 }
  ],
  customersBySegment: [
    { segment: 'العملاء المميزون', count: 25, percentage: 16.7 },
    { segment: 'العملاء النشطون', count: 60, percentage: 40 },
    { segment: 'العملاء الجدد', count: 35, percentage: 23.3 },
    { segment: 'عملاء غير نشطين', count: 30, percentage: 20 }
  ],
  churnRate: 15
};

// Functions to manage mock data
export function getMockCustomers(): Customer[] {
  if (typeof window === 'undefined') return mockCustomers;
  
  const stored = localStorage.getItem(MOCK_CUSTOMERS_KEY);
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch {
      return mockCustomers;
    }
  }
  
  localStorage.setItem(MOCK_CUSTOMERS_KEY, JSON.stringify(mockCustomers));
  return mockCustomers;
}

export function getMockFinancialTransactions(): FinancialTransaction[] {
  if (typeof window === 'undefined') return mockFinancialTransactions;
  
  const stored = localStorage.getItem(MOCK_FINANCIAL_TRANSACTIONS_KEY);
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch {
      return mockFinancialTransactions;
    }
  }
  
  localStorage.setItem(MOCK_FINANCIAL_TRANSACTIONS_KEY, JSON.stringify(mockFinancialTransactions));
  return mockFinancialTransactions;
}

export function getMockExpenseCategories(): ExpenseCategory[] {
  if (typeof window === 'undefined') return mockExpenseCategories;
  
  const stored = localStorage.getItem(MOCK_EXPENSE_CATEGORIES_KEY);
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch {
      return mockExpenseCategories;
    }
  }
  
  localStorage.setItem(MOCK_EXPENSE_CATEGORIES_KEY, JSON.stringify(mockExpenseCategories));
  return mockExpenseCategories;
}

export function getMockSalesAnalytics(): SalesAnalytics {
  return mockSalesAnalytics;
}

export function getMockInventoryAnalytics(): InventoryAnalytics {
  return mockInventoryAnalytics;
}

export function getMockCustomerAnalytics(): CustomerAnalytics {
  return mockCustomerAnalytics;
}

export function getMockCustomerSegments(): CustomerSegment[] {
  return mockCustomerSegments;
}

export function addMockCustomer(customer: Customer): void {
  if (typeof window === 'undefined') return;
  
  const customers = getMockCustomers();
  customers.unshift(customer);
  localStorage.setItem(MOCK_CUSTOMERS_KEY, JSON.stringify(customers));
}

export function updateMockCustomer(customerId: string, updates: Partial<Customer>): void {
  if (typeof window === 'undefined') return;
  
  const customers = getMockCustomers();
  const index = customers.findIndex(c => c.id === customerId);
  if (index !== -1) {
    customers[index] = { ...customers[index], ...updates };
    localStorage.setItem(MOCK_CUSTOMERS_KEY, JSON.stringify(customers));
  }
}

export function addMockFinancialTransaction(transaction: FinancialTransaction): void {
  if (typeof window === 'undefined') return;
  
  const transactions = getMockFinancialTransactions();
  transactions.unshift(transaction);
  localStorage.setItem(MOCK_FINANCIAL_TRANSACTIONS_KEY, JSON.stringify(transactions));
}
